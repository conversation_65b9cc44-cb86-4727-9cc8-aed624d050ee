package com.ibms.service.assets.extent.flowable.extension.service;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ibms.service.assets.extent.flowable.extension.domain.FlowAssignee;
import com.ibms.service.assets.extent.flowable.extension.domain.FlowButton;
import com.ibms.service.assets.extent.flowable.extension.domain.FlowCondition;
import com.ibms.service.assets.extent.flowable.extension.domain.TaskDefExtension;
import com.ibms.service.assets.extent.flowable.extension.mapper.TaskDefExtensionMapper;
import com.ibms.service.assets.extent.flowable.extension.service.dto.FlowAssigneeDTO;
import com.ibms.service.assets.extent.flowable.extension.service.dto.FlowButtonDTO;
import com.ibms.service.assets.extent.flowable.extension.service.dto.FlowConditionDTO;
import com.ibms.service.assets.extent.flowable.extension.service.dto.TaskDefExtensionDTO;
import com.ibms.service.assets.extent.flowable.extension.service.impl.FlowAssigneeServiceImpl;
import com.ibms.service.assets.extent.flowable.extension.service.impl.FlowButtonServiceImpl;
import com.ibms.service.assets.extent.flowable.extension.service.impl.FlowConditionServiceImpl;
import com.ibms.service.assets.extent.flowable.extension.service.mapstruct.FlowAssigneeWrapper;
import com.ibms.service.assets.extent.flowable.extension.service.mapstruct.FlowButtonWrapper;
import com.ibms.service.assets.extent.flowable.extension.service.mapstruct.FlowConditionWrapper;
import com.ibms.service.assets.extent.flowable.flow.constant.CommonConstants;
import com.ibms.service.assets.extent.flowable.flow.mapstruct.TaskDefExtensionWrapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.stream.Collectors;

/**
 * @Description
 * <AUTHOR>
 * @Date 2022/12/5 15:57
 */
@Service
public class TaskDefExtensionService extends ServiceImpl<TaskDefExtensionMapper, TaskDefExtension> {

    @Autowired
    private FlowAssigneeServiceImpl flowAssigneeService;
    @Autowired
    private FlowButtonServiceImpl flowButtonService;
    @Autowired
    private FlowConditionServiceImpl flowConditionService;
    @Autowired
    private TaskDefExtensionWrapper taskDefExtensionWrapper;


    public TaskDefExtensionDTO getById(String id) {

        TaskDefExtensionDTO taskDefExtension = toDTO(baseMapper.selectById(id));

        taskDefExtension.setFlowAssigneeList(flowAssigneeService.lambdaQuery().eq(FlowAssignee::getTaskDefId, id).list().stream().map(FlowAssigneeWrapper.INSTANCE::toDTO).collect(Collectors.toList()));
        taskDefExtension.setFlowButtonList(flowButtonService.lambdaQuery().eq(FlowButton::getTaskDefId, id).list().stream().map(FlowButtonWrapper.INSTANCE::toDTO).collect(Collectors.toList()));
        taskDefExtension.setFlowConditionList(flowConditionService.lambdaQuery().eq(FlowCondition::getTaskDefId, id).list().stream().map(FlowConditionWrapper.INSTANCE::toDTO).collect(Collectors.toList()));

        return taskDefExtension;
    }

    public void save(TaskDefExtensionDTO taskDefExtensionDTO) {

        TaskDefExtension taskDefExtension = taskDefExtensionWrapper.toEntity(taskDefExtensionDTO);
        super.saveOrUpdate(taskDefExtension);
        for (FlowAssigneeDTO flowAssigneeDTO : taskDefExtensionDTO.getFlowAssigneeList()) {
            if (flowAssigneeDTO.getId() == null) {
                continue;
            }

            if (CommonConstants.DELETED.equals(flowAssigneeDTO.getDelFlag())) {
                flowAssigneeService.removeById(flowAssigneeDTO.getId());
            } else {
                FlowAssignee flowAssignee = FlowAssigneeWrapper.INSTANCE.toEntity(flowAssigneeDTO);
                flowAssignee.setTaskDefId(taskDefExtension.getId());
                flowAssigneeService.saveOrUpdate(flowAssignee);
            }
        }
        for (FlowButtonDTO flowButtonDTO : taskDefExtensionDTO.getFlowButtonList()) {
            if (flowButtonDTO.getId() == null) {
                continue;
            }
            if (CommonConstants.DELETED.equals(flowButtonDTO.getDelFlag())) {
                flowButtonService.removeById(flowButtonDTO.getId());

            } else {
                FlowButton flowButton = FlowButtonWrapper.INSTANCE.toEntity(flowButtonDTO);
                flowButton.setTaskDefId(taskDefExtension.getId());
                flowButtonService.saveOrUpdate(flowButton);
            }
        }
        for (FlowConditionDTO flowConditionDTO : taskDefExtensionDTO.getFlowConditionList()) {
            if (flowConditionDTO.getId() == null) {
                continue;
            }
            if (CommonConstants.DELETED.equals(flowConditionDTO.getDelFlag())) {
                flowConditionService.removeById(flowConditionDTO.getId());
            } else {
                FlowCondition flowCondition = FlowConditionWrapper.INSTANCE.toEntity(flowConditionDTO);
                flowCondition.setTaskDefId(taskDefExtension.getId());
                flowConditionService.save(flowCondition);
            }
        }
    }

    public void delete(String id) {
        removeById(id);
        flowAssigneeService.lambdaUpdate().eq(FlowAssignee::getTaskDefId, id).remove();
        flowButtonService.lambdaUpdate().eq(FlowButton::getTaskDefId, id).remove();
        flowConditionService.lambdaUpdate().eq(FlowCondition::getTaskDefId, id).remove();
    }

    public void deleteByProcessDefId(String processDefId) {
        List<TaskDefExtension> list = lambdaQuery().eq(TaskDefExtension::getProcessDefId, processDefId).list();
        for (TaskDefExtension taskDefExtension : list) {
            String id = taskDefExtension.getId();
            delete(id);
        }

    }

    public TaskDefExtensionDTO toDTO(TaskDefExtension entity) {

        if (entity == null) {
            return null;
        }

        TaskDefExtensionDTO taskDefExtensionDTO = new TaskDefExtensionDTO();

        taskDefExtensionDTO.setCreateBy(entity.getCreateBy());
        taskDefExtensionDTO.setUpdateBy(entity.getUpdateBy());
        taskDefExtensionDTO.setId(entity.getId());
        taskDefExtensionDTO.setCreateTime(entity.getCreateTime());
        taskDefExtensionDTO.setUpdateTime(entity.getUpdateTime());
        taskDefExtensionDTO.setProcessDefId(entity.getProcessDefId());
        taskDefExtensionDTO.setTaskDefId(entity.getTaskDefId());

        return taskDefExtensionDTO;
    }


}
