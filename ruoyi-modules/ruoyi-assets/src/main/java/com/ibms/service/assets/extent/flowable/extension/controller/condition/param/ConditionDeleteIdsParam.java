package com.ibms.service.assets.extent.flowable.extension.controller.condition.param;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * @Description
 * <AUTHOR>
 * @Date 2022/11/28 12:16
 */
@Data
@ApiModel(value = "流程表达式根据id列表删除参数")
public class ConditionDeleteIdsParam {

    @NotBlank(message = "流程表达式id数组必填！")
    @ApiModelProperty("流程表达式id数组")
    private String ids;

}
