package com.ibms.service.assets.extent.flowable.extension.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.ibms.service.assets.extent.flowable.extension.domain.NodeSetting;

/**
 * @Description
 * <AUTHOR>
 * @Date 2022/11/30 11:37
 */
public interface NodeSettingService extends IService<NodeSetting> {

    public NodeSetting queryByKey(String processDefId, String taskDefId, String key);

    public void deleteByDefIdAndTaskId(NodeSetting nodeSetting);

    public void deleteByProcessDefId(String processDefId);

}
