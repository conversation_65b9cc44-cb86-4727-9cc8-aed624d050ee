package com.ibms.service.assets.extent.flowable.extension.controller.taskdefextension.param;

import com.google.common.collect.Lists;
import com.ibms.service.assets.extent.flowable.extension.domain.FlowAssignee;
import com.ibms.service.assets.extent.flowable.extension.domain.FlowButton;
import com.ibms.service.assets.extent.flowable.extension.domain.FlowCondition;
import lombok.Data;

import java.util.List;

/**
 * @Description
 * <AUTHOR>
 * @Date 2022/12/7 14:20
 */
@Data
public class TaskDefExtensionSaveParam {

    /**
     * 实体主键
     */
    protected String id;

    /**
     * 流程定义id
     */
    private String processDefId;
    /**
     * 任务定义id
     */
    private String taskDefId;

    private List<FlowAssignee> flowAssigneeList = Lists.newArrayList();

    private List<FlowButton> flowButtonList = Lists.newArrayList();

    private List<FlowCondition> flowConditionList = Lists.newArrayList();

}
