/**
 * Copyright © 2021-2026 <a href="http://www.jeeplus.org/">JeePlus</a> All rights reserved.
 */
package com.ibms.service.assets.extent.flowable.extension.controller.actcategory;

import cn.hutool.core.lang.tree.Tree;
import cn.hutool.core.lang.tree.TreeNodeConfig;
import cn.hutool.core.lang.tree.TreeUtil;
import com.ibms.service.assets.extent.flowable.extension.controller.actcategory.param.ActCategoryDeleteIdsParam;
import com.ibms.service.assets.extent.flowable.extension.controller.actcategory.param.ActCategoryGetByIdParam;
import com.ibms.service.assets.extent.flowable.extension.controller.actcategory.param.ActCategoryListParam;
import com.ibms.service.assets.extent.flowable.extension.controller.actcategory.param.ActCategorySavaOrUpdateParam;
import com.ibms.service.assets.extent.flowable.extension.domain.ActCategory;
import com.ibms.service.assets.extent.flowable.extension.service.ActCategoryService;
import com.ibms.service.assets.extent.flowable.extension.service.dto.ActCategoryDto;
import com.ruoyi.common.core.web.controller.BaseController;
import com.ruoyi.common.core.web.domain.AjaxResult;
import com.ruoyi.common.core.web.domain.TAjaxResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 流程分类Controller
 *
 * <AUTHOR>
 * @version 2022/11/29 16:42:09
 */
@Api(tags = "工作流-流程分类")
@RestController
@RequestMapping("/extension/actCategory")
public class ActCategoryController extends BaseController {

    @Autowired
    private ActCategoryService actCategoryService;

    /**
     * 获取JSON树形数据
     *
     * @return
     */
    @GetMapping("treeData")
    @ApiOperation(value = "获取JSON树形数据。")
    public TAjaxResult<List<ActCategory>> treeData(@Validated ActCategoryListParam param) {

        List<ActCategory> list = actCategoryService.list(param);

        // Json树形结构
        TreeNodeConfig config = new TreeNodeConfig();
        // config可以配置属性字段名和排序等等
        // config.setParentIdKey("parentId");
        // config.setDeep(20);//最大递归深度  默认无限制
        List<Tree<String>> treeNodes = TreeUtil.build(list, "0", config, (object, tree) -> {
            tree.setId(object.getId());// 必填属性
            tree.setParentId(object.getParentId());// 必填属性
            tree.setWeight(object.getSort());
            tree.setName(object.getName());
            // 扩展属性 ...
            tree.putExtra("remarks", object.getRemarks());
        });

        return new TAjaxResult().success(treeNodes);
    }

    /**
     * 根据id查询实例
     *
     * @return
     */
    @GetMapping("queryById")
    @ApiOperation(value = "根据id查询实例。")
    public TAjaxResult<ActCategoryDto> queryById(@Validated ActCategoryGetByIdParam param) {

        ActCategoryDto actCategoryDto = actCategoryService.queryById(param);

        return new TAjaxResult().success(actCategoryDto);
    }

    /**
     * 保存流程分类
     */
    @PostMapping("save")
    @ApiOperation(value = "保存/更新-流程分类。")
    public TAjaxResult saveOrUpdate(@RequestBody @Validated ActCategorySavaOrUpdateParam param) {

        // 新增或编辑表单保存
        Integer num = actCategoryService.saveOrUpdate(param);// 保存

        return new TAjaxResult<>().success(num);
    }

    /**
     * 删除流程分类
     */
    @ApiOperation(value = "删除流程分类。")
    @DeleteMapping("delete")
    public AjaxResult delete(@Validated ActCategoryDeleteIdsParam param) {

        Integer num = actCategoryService.deleteByIds(param);

        return toAjax(num);
    }


}
