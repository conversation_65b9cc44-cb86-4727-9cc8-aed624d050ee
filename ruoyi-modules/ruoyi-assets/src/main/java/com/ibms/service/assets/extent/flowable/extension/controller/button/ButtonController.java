/**
 * Copyright © 2021-2026 <a href="http://www.jeeplus.org/">JeePlus</a> All rights reserved.
 */
package com.ibms.service.assets.extent.flowable.extension.controller.button;

import com.ibms.service.assets.extent.flowable.extension.controller.button.param.*;
import com.ibms.service.assets.extent.flowable.extension.service.ButtonService;
import com.ibms.service.assets.extent.flowable.extension.domain.Button;
import com.ruoyi.common.core.web.controller.BaseController;
import com.ruoyi.common.core.web.domain.AjaxResult;
import com.ruoyi.common.core.web.domain.TAjaxResult;
import com.ruoyi.common.core.web.page.TableDataInfo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 常用按钮
 *
 * <AUTHOR>
 * @version 2022/11/28 16:51:49
 */
@Api(tags = "工作流-常用按钮")
@RestController
@RequestMapping("/extension/button")
public class ButtonController extends BaseController {

    @Autowired
    private ButtonService buttonService;

    /**
     * 常用按钮列表数据
     */
    @GetMapping("/list")
    @ApiOperation(value = "常用按钮列表数据")
    public TableDataInfo<Button> list(@Validated ButtonListParam param) {

        startPage();
        List<Button> list = buttonService.list(param);

        return getDataTable(list);
    }

    /**
     * 根据id查询数据
     *
     * @return
     */
    @GetMapping("queryById")
    @ApiOperation(value = "根据id获取按钮详情")
    public TAjaxResult<Button> queryById(@Validated ButtonGetByIdParam param) {

        Button button = buttonService.queryById(param);

        return new TAjaxResult().success(button);
    }


    /**
     * 保存/更新 常用按钮
     */
    @PostMapping("save")
    @ApiOperation(value = "保存/更新 常用按钮")
    public AjaxResult saveOrUpdate(@RequestBody @Validated ButtonSavaOrUpdateParam param) {

        // 新增或编辑表单保存
        Integer num = buttonService.saveOrUpdate(param);// 保存

        return toAjax(num);
    }


    /**
     * 批量删除常用按钮
     */
    @DeleteMapping("delete")
    @ApiOperation(value = "批量删除常用按钮")
    public AjaxResult delete(@Validated ButtonDeleteIdsParam param) {

        Integer num = buttonService.deleteByIds(param);

        return toAjax(num);
    }

    /**
     * 按钮名唯一性校验（数据库中不存在）
     */
    @ApiOperation(value = "按钮名唯一性校验（true存在，false）")
    @GetMapping("validateNameNoExist")
    public TAjaxResult validateNameNoExist(@Validated ButtonValidateNameParam param) {

        boolean result = buttonService.validateNameNoExist(param);

        return new TAjaxResult<>().success(result);
    }


    /**
     * 编码唯一性验证（数据库中不存在）
     */
    @GetMapping("validateCodeNoExist")
    @ApiOperation(value = "编码唯一性验证（true存在，false）")
    public AjaxResult validateCodeNoExist(@Validated ButtonValidateCodeParam param) {

        boolean result = buttonService.validateCodeNoExist(param);

        return toAjax(result);
    }

}
