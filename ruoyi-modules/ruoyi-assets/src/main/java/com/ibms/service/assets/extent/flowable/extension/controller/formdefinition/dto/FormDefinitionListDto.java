package com.ibms.service.assets.extent.flowable.extension.controller.formdefinition.dto;

import cn.hutool.json.JSONObject;
import lombok.Data;

import java.util.Date;

/**
 * @Description
 * <AUTHOR>
 * @Date 2022/12/7 10:46
 */
@Data
public class FormDefinitionListDto {


    private String id;
    private String createById;
    private Date createDate;
    private String updateById;
    private Date updateDate;
    private String remarks;
    private String delFlag;
    private String categoryId;
    private String name;
    private String categoryName;
    private String formDefinitionJsonId;
    private String formDefinitionJsonJsonBody;
    private String formDefinitionJsonVersion;
    private String formDefinitionJsonStatus;
    private String formDefinitionJsonIsPrimary;

}
