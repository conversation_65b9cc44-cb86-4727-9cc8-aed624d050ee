package com.ibms.service.assets.extent.flowable.extension.controller.actcategory.param;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * @Description
 * <AUTHOR>
 * @Date 2022/11/28 12:16
 */
@Data
@ApiModel(value = "根据id查询实例参数")
public class ActCategoryGetByIdParam {

    @NotBlank(message = "流程分类id必填")
    @ApiModelProperty("流程分类id")
    private String id;

}
