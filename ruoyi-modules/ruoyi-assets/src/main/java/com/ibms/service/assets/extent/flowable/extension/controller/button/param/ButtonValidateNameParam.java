package com.ibms.service.assets.extent.flowable.extension.controller.button.param;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * @Description
 * <AUTHOR>
 * @Date 2022/11/28 12:16
 */
@Data
@ApiModel(value = "常用按钮名称校验参数")
public class ButtonValidateNameParam {

    @NotBlank(message = "按钮名称必填")
    @ApiModelProperty("按钮名称")
    private String name;

}
