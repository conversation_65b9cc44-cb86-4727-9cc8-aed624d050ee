package com.ibms.service.assets;

import com.ruoyi.common.security.annotation.EnableCustomConfig;
import com.ruoyi.common.security.annotation.EnableRyFeignClients;
import com.ruoyi.common.swagger.annotation.EnableCustomSwagger2;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.actuate.autoconfigure.security.servlet.ManagementWebSecurityAutoConfiguration;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.autoconfigure.security.servlet.SecurityAutoConfiguration;
import org.springframework.boot.web.servlet.support.SpringBootServletInitializer;
import org.springframework.context.annotation.ComponentScan;


@EnableCustomConfig
@EnableCustomSwagger2
@EnableRyFeignClients
@SpringBootApplication(
        scanBasePackages = {"com.ibms.service.assets",
                "org.flowable.ui.modeler.properties",
                "org.flowable.ui.modeler.repository",
                "org.flowable.ui.modeler.service",
                "org.flowable.ui.common.service",
                "org.flowable.ui.common.repository",
                "org.flowable.ui.common.tenant",
                "com.ibms.service.assets.extent.flowable.config",
                "com.ruoyi",

//                "com.ibms.service.assets.extent.flowable.config.FlowableModelerAppProperties"
        },
        exclude = {SecurityAutoConfiguration.class, ManagementWebSecurityAutoConfiguration.class}
)
public class AssetsApplication extends SpringBootServletInitializer {

    public static void main(String[] args) {
        SpringApplication.run(AssetsApplication.class, args);

        System.out.println("(♥◠‿◠)ﾉﾞ  资产模块启动成功   ლ(´ڡ`ლ)ﾞ  \n" +
                "  _____  ____       _ \n" +
                " |  __ \\|  _ \\     | |\n" +
                " | |  | | |_) |    | |\n" +
                " | |  | |  _ < _   | |\n" +
                " | |__| | |_) | |__| |\n" +
                " |_____/|____/ \\____/ \n" +
                "                       \n"
        );
    }

}
