package com.ibms.service.assets.extent.flowable.extension.controller.condition.param;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * @Description
 * <AUTHOR>
 * @Date 2022/11/28 12:16
 */
@Data
@ApiModel(value = "流程表达式根据id参数")
public class ConditionGetByIdParam {

    @NotBlank(message = "流程表达式id必填")
    @ApiModelProperty("流程表达式id")
    private String id;

}
