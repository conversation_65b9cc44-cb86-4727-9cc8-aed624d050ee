package com.ibms.service.assets.extent.flowable.extension.controller.button.param;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * @Description
 * <AUTHOR>
 * @Date 2022/11/28 12:16
 */
@Data
@ApiModel(value = "保存/更新 常用按钮参数")
public class ButtonSavaOrUpdateParam {

    /**
     * 主键
     */
    @ApiModelProperty("主键(不为空则update，反之保存)")
    private String id;

    /**
     * 名称
     */
    @NotBlank(message = "名称必填")
    @ApiModelProperty("名称")
    private String name;

    /**
     * 编码
     */
    @NotBlank(message = "编码必填")
    @ApiModelProperty("编码")
    private String code;

    /**
     * 排序
     */
    @ApiModelProperty("排序")
    private Integer sort;

}
