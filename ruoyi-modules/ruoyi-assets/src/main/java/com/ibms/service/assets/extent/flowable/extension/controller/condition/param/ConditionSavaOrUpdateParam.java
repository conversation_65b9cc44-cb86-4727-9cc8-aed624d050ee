package com.ibms.service.assets.extent.flowable.extension.controller.condition.param;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @Description
 * <AUTHOR>
 * @Date 2022/11/28 12:16
 */
@Data
@ApiModel(value = "保存/更新 流程表达式参数")
public class ConditionSavaOrUpdateParam {

    /**
     * 主键
     */
    @ApiModelProperty("主键(不为空则update，反之保存)")
    private String id;

    /**
     * 名称
     */
    @ApiModelProperty("名称")
    private String name;

    /**
     * 表达式
     */
    @ApiModelProperty("表达式")
    private String expression;

    /**
     * 备注
     */
    @ApiModelProperty("备注")
    private String remarks;

}
