package com.ibms.service.assets.extent.flowable.extension.controller.condition.param;

import com.ruoyi.common.core.web.page.BasePage;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @Description
 * <AUTHOR>
 * @Date 2022/11/28 12:16
 */
@Data
@ApiModel(value = "流程表达式列表参数")
public class ConditionListParam extends BasePage {

    @ApiModelProperty("流程表达式名称")
    private String name;

}
