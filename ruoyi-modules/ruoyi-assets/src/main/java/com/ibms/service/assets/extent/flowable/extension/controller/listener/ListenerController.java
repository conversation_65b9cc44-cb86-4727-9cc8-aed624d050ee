/**
 * Copyright © 2021-2026 <a href="http://www.jeeplus.org/">JeePlus</a> All rights reserved.
 */
package com.ibms.service.assets.extent.flowable.extension.controller.listener;

import com.google.common.collect.Lists;
import com.ibms.service.assets.extent.flowable.extension.controller.listener.param.ListenerListParam;
import com.ibms.service.assets.extent.flowable.extension.domain.Listener;
import com.ibms.service.assets.extent.flowable.extension.service.ListenerService;
import com.ruoyi.common.core.web.controller.BaseController;
import com.ruoyi.common.core.web.domain.AjaxResult;
import com.ruoyi.common.core.web.domain.TAjaxResult;
import com.ruoyi.common.core.web.page.TableDataInfo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;

/**
 * 监听器Controller
 *
 * <AUTHOR>
 * @version 2021-10-14
 */
@Api(tags = "工作流-监听器")
@RestController
@RequestMapping("/extension/listener")
public class ListenerController extends BaseController {

    @Autowired
    private ListenerService listenerService;

    /**
     * 监听器列表数据
     */
    @GetMapping("/list")
    @ApiOperation(value = "监听器列表数据.")
    public TableDataInfo<Listener> list(ListenerListParam listener) throws Exception {

        List<Listener> listenerList = listenerService.getByList(listener);

        return getDataTable(listenerList);
    }

    /**
     * 根据id查询数据
     *
     * @param id
     * @return
     */
    @GetMapping("/queryById")
    @ApiOperation(value = "根据id查询数据.")
    public TAjaxResult<Listener> queryById(String id) {

        Listener listener = listenerService.getById(id);

        return new TAjaxResult().success(listener);
    }

    /**
     * 保存监听器
     */
    @PostMapping("/save")
    @ApiOperation(value = "保存监听器.")
    public AjaxResult save(@Valid @RequestBody Listener listener) {
        // 新增或编辑表单保存
        boolean b = listenerService.saveOrUpdate(listener);// 保存
        return toAjax(b);
    }


    /**
     * 批量删除监听器
     */
    @DeleteMapping("/delete")
    @ApiOperation(value = "批量删除监听器.")
    public AjaxResult delete(String ids) {
        String[] idArray = ids.split(",");
        boolean b = listenerService.removeByIds(Lists.newArrayList(idArray));
        return toAjax(b);
    }

}
