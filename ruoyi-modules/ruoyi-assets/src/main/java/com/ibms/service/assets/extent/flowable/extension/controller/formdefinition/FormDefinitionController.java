package com.ibms.service.assets.extent.flowable.extension.controller.formdefinition;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.ibms.service.assets.extent.flowable.extension.controller.formdefinition.param.FormDefinitionListParam;
import com.ibms.service.assets.extent.flowable.extension.service.FormDefinitionService;
import com.ibms.service.assets.extent.flowable.extension.service.dto.FormDefinitionDto;
import com.ruoyi.common.core.web.controller.BaseController;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * @Description 流程表单Controller
 * <AUTHOR>
 * @Date 2022/12/6 14:47
 */
@RestController
@RequestMapping(value = "/extension/formDefinition")
public class FormDefinitionController extends BaseController {

    @Autowired
    private FormDefinitionService formDefinitionService;


    /**
     * 流程表单列表数据
     */
    @GetMapping("/list")
    public ResponseEntity list(FormDefinitionListParam param, Page<FormDefinitionDto> page) {

        IPage<FormDefinitionDto> result = formDefinitionService.findPage(page, param);

        return ResponseEntity.ok(result);

    }

}
