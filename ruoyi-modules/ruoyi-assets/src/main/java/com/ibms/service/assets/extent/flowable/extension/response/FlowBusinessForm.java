package com.ibms.service.assets.extent.flowable.extension.response;

import lombok.Data;

/**
 * @Description flowable 流程启动-表单返回值
 * 用于FlowMapper.xml中更新proc_ins_id的值
 * <AUTHOR>
 * @Date 2023/1/13 9:24
 */
@Data
public class FlowBusinessForm {

    public static final String ASSETS_APPLY_BASE = "t_assets_apply_base";

    /**
     * 业务表
     * 备注：表单所操作的业务表
     */
    private String businessTable;

    /**
     * businessTable表的主键id
     */
    private String businessId;

}
