package com.ibms.service.assets.extent.flowable.extension.controller.button.param;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * @Description
 * <AUTHOR>
 * @Date 2022/11/28 12:16
 */
@Data
@ApiModel(value = "常用按钮id参数")
public class ButtonGetByIdParam {

    @NotBlank(message = "按钮id必填")
    @ApiModelProperty("按钮id")
    private String id;

}
