package com.ibms.service.assets.extent.flowable.extension.controller.taskdefextension;

import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.ibms.service.assets.extent.flowable.extension.service.dto.TaskDefExtensionDTO;
import com.ibms.service.assets.extent.flowable.extension.domain.TaskDefExtension;
import com.ibms.service.assets.extent.flowable.extension.service.TaskDefExtensionService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * @Description
 * <AUTHOR>
 * @Date 2022/12/5 15:29
 */
@RestController
@RequestMapping(value = "/extension/taskDefExtension")
public class TaskDefExtensionController {

    @Autowired
    private TaskDefExtensionService taskDefExtensionService;

    /**
     * 工作流扩展列表数据
     */
    @GetMapping("/list")
    public ResponseEntity list(TaskDefExtension taskDefExtension, Page<TaskDefExtension> page) throws Exception {

        QueryWrapper<TaskDefExtension> objectQueryWrapper = new QueryWrapper<>();

        IPage<TaskDefExtension> result = taskDefExtensionService.page(page, objectQueryWrapper);
        return ResponseEntity.ok(result);
    }

    /**
     * 根据Id获取工作流扩展数据
     */
    @GetMapping("/queryById")
    public ResponseEntity<TaskDefExtensionDTO> queryById(String id) {
        return ResponseEntity.ok(taskDefExtensionService.getById(id));
    }

    @GetMapping("/queryByDefIdAndTaskId")
    public ResponseEntity<TaskDefExtensionDTO> queryByDefIdAndTaskId(TaskDefExtensionDTO taskDefExtension) throws Exception {
        if (StrUtil.isBlank(taskDefExtension.getProcessDefId()) || StrUtil.isBlank(taskDefExtension.getTaskDefId())) {
            return ResponseEntity.ok(null);
        }
        List<TaskDefExtension> list = taskDefExtensionService.lambdaQuery()
                .eq(TaskDefExtension::getProcessDefId, taskDefExtension.getProcessDefId())
                .eq(TaskDefExtension::getTaskDefId, taskDefExtension.getTaskDefId())
                .list();
        if (list.size() > 1) {
            throw new Exception("重复的task id定义!");
        } else if (list.size() == 1) {
            String id = list.get(0).getId();
            return ResponseEntity.ok(taskDefExtensionService.getById(id));
        } else {
            return ResponseEntity.ok(taskDefExtension);
        }

    }

    /**
     * 保存工作流扩展
     */
    @PostMapping("save")
    public ResponseEntity save(@RequestBody List<TaskDefExtensionDTO> taskDefExtensionList) {

        for (TaskDefExtensionDTO taskDefExtension : taskDefExtensionList) {
            List<TaskDefExtension> list = taskDefExtensionService.lambdaQuery()
                    .eq(TaskDefExtension::getProcessDefId, taskDefExtension.getProcessDefId())
                    .eq(TaskDefExtension::getTaskDefId, taskDefExtension.getTaskDefId()).list();
            // 删除旧数据
            for (TaskDefExtension defExtension : list) {
                taskDefExtensionService.delete(defExtension.getId());
            }
            // 保存新数据
            taskDefExtensionService.save(taskDefExtension);// 保存
        }
        return ResponseEntity.ok("保存工作流扩展成功");

    }


}
