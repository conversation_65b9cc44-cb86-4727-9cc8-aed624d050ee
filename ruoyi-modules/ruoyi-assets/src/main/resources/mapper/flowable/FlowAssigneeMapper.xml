<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.ibms.service.assets.plugins.flowable.extension.mapper.FlowAssigneeMapper">

    <resultMap id="BaseResultMap" type="com.ibms.service.assets.extent.flowable.extension.domain.FlowAssignee">
        <result column="id" property="id"/>
        <result column="type" property="type"/>
        <result column="assign_value" property="assignValue"/>
        <result column="assign_condition" property="assignCondition"/>
        <result column="operation_type" property="operationType"/>
        <result column="sort" property="sort"/>
        <result column="task_def_id" property="taskDefId"/>
        <result column="create_time" property="createTime"/>
        <result column="create_by" property="createBy"/>
        <result column="update_time" property="updateTime"/>
        <result column="update_by" property="updateBy"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,
        type,
        assign_value,
        assign_condition,
        operation_type,
        sort,
        task_def_id,
        create_time,
        create_by,
        update_time,
        update_by
    </sql>

</mapper>
