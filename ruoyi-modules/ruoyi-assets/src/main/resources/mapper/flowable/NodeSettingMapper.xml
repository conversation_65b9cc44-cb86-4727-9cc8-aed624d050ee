<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.ibms.service.assets.plugins.flowable.extension.mapper.NodeSettingMapper">

    <resultMap id="BaseResultMap" type="com.ibms.service.assets.extent.flowable.extension.domain.NodeSetting">
        <result column="id" property="id"/>
        <result column="process_def_id" property="processDefId"/>
        <result column="task_def_id" property="taskDefId"/>
        <result column="node_key" property="nodeKey"/>
        <result column="node_value" property="nodeValue"/>
        <result column="create_time" property="createTime"/>
        <result column="create_by" property="createBy"/>
        <result column="update_time" property="updateTime"/>
        <result column="update_by" property="updateBy"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,
        process_def_id,
        task_def_id,
        node_key,
        node_value,
        create_time,
        create_by,
        update_time,
        update_by
    </sql>

</mapper>
