<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.ibms.service.assets.extent.flowable.extension.mapper.FormDefinitionMapper">

    <resultMap id="BaseResultMap" type="com.ibms.service.assets.extent.flowable.extension.domain.FormDefinition">
        <result column="id" property="id"/>
        <result column="create_by" property="createBy"/>
        <result column="create_time" property="createTime"/>
        <result column="update_by" property="updateBy"/>
        <result column="update_time" property="updateTime"/>
        <result column="remarks" property="remarks"/>
        <result column="del_flag" property="delFlag"/>
        <result column="category_id" property="categoryId"/>
        <result column="name" property="name"/>
        <result column="version" property="version"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,
        create_by,
        create_time,
        update_by,
        update_time,
        remarks,
        del_flag,
        category_id,
        name,
        version
    </sql>


    <select id="findList" resultType="com.ibms.service.assets.extent.flowable.extension.service.dto.FormDefinitionDto">
        SELECT
        a.id AS "id",
        a.create_by AS "createById",
        a.create_time AS "createTime",
        a.update_by AS "updateById",
        a.update_time AS "updateTime",
        a.remarks AS "remarks",
        a.del_flag AS "delFlag",
        a.category_id AS "categoryId",
        a.name AS "name",
        b.name AS "categoryName",
        c.id AS "formDefinitionJsonId",
        c.json_body AS "formDefinitionJsonJsonBody",
        c.version AS "formDefinitionJsonVersion",
        c.status AS "formDefinitionJsonStatus",
        c.is_primary AS "formDefinitionJsonIsPrimary"
        FROM act_extension_form_def a
        LEFT JOIN act_extension_form_category b ON b.id = a.category_id
        LEFT JOIN act_extension_form_def_json c ON c.form_definition_id = a.id
        WHERE
        (a.del_flag = 0 AND c.is_primary = 1)
    </select>


</mapper>
