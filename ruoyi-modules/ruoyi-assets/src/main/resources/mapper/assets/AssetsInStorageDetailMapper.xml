<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ibms.service.assets.web.mapper.assetsinstoragedetail.AssetsInStorageDetailMapper">


    <resultMap id="BaseResultMap" type="com.ibms.service.assets.web.domain.AssetsInStorageDetail">
        <result column="id" property="id"/>
        <result column="parent_base_id" property="parentBaseId"/>
        <result column="stock_data_id" property="stockDataId"/>
        <result column="correlation_apply_id" property="correlationApplyId"/>
        <result column="assets_source" property="assetsSource"/>
        <result column="purchase_time" property="purchaseTime"/>
        <result column="number" property="number"/>
        <result column="supplier" property="supplier"/>
        <result column="storage_place_id" property="storagePlaceId"/>
        <result column="create_by" property="createBy"/>
        <result column="create_time" property="createTime"/>
        <result column="update_by" property="updateBy"/>
        <result column="update_time" property="updateTime"/>
        <result column="del_flag" property="delFlag"/>
        <result column="remark" property="remark"/>
        <result column="original_value_amount" property="originalValueAmount"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,
        parent_base_id,
        stock_data_id,
        correlation_apply_id,
        assets_source,
        purchase_time,
        number,
        supplier,
        storage_place_id,
        create_by,
        create_time,
        update_by,
        update_time,
        del_flag,
        original_value_amount,
        remark
    </sql>
    <select id="selectDetailList"
            resultType="com.ibms.service.assets.web.controller.assetsinstoragedetail.vo.AssetsInStorageDetailListVo"
            parameterType="com.ibms.service.assets.web.controller.assetsinstoragedetail.param.AssetsInStorageDetailInfoParam">
        SELECT
        a.id,
        a.parent_base_id,
        a.stock_data_id,
        a.correlation_apply_id,
        a.assets_source,
        a.purchase_time,
        a.number,
        a.supplier,
        a.storage_place_id,
        a.create_by,
        a.create_time,
        a.update_by,
        a.update_time,
        a.del_flag,
        a.original_value_amount,
        a.remark,
        b.name AS stockDataName,
        b.unit AS unit,
        b.specification AS specification,
        c.place_storage as storagePlaceName
        FROM
        t_assets_in_storage_detail AS a
        LEFT JOIN t_stock_data_info AS b ON a.stock_data_id = b.id
        left join t_assets_place_info as c on a.storage_place_id = c.id
        WHERE
        a.parent_base_id = #{applyBaseId}
        ORDER BY
        a.create_time DESC
    </select>

</mapper>
