<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ibms.service.assets.web.mapper.assetsplaceinfo.AssetsPlaceInfoMapper">


    <resultMap id="BaseResultMap" type="com.ibms.service.assets.web.domain.AssetsPlaceInfo">
        <result column="id" property="id"/>
        <result column="dept_id" property="deptId"/>
        <result column="dept_name" property="deptName"/>
        <result column="administrator_id" property="administratorId"/>
        <result column="administrator_name" property="administratorName"/>
        <result column="place_storage" property="placeStorage"/>
        <result column="create_by" property="createBy"/>
        <result column="create_time" property="createTime"/>
        <result column="update_by" property="updateBy"/>
        <result column="update_time" property="updateTime"/>
        <result column="del_flag" property="delFlag"/>
        <result column="dept_ancestors" property="deptAncestors"/>
        <result column="remark" property="remark"/>
    </resultMap>


    <sql id="Base_Column_List">
        id,
        dept_id,
        dept_name,
        administrator_id,
        administrator_name,
        place_storage,
        dept_ancestors,
        create_by,
        create_time,
        update_by,
        update_time,
        del_flag,
        remark
    </sql>


</mapper>
