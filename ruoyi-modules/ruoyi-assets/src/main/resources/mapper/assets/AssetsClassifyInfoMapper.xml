<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ibms.service.assets.web.mapper.assetsclassifyinfo.AssetsClassifyInfoMapper">

    <resultMap id="BaseResultMap" type="com.ibms.service.assets.web.domain.AssetsClassifyInfo">
        <result column="id" property="id"/>
        <result column="name" property="name"/>
        <result column="classify_type" property="classifyType"/>
        <result column="sort" property="sort"/>
        <result column="statement" property="statement"/>
        <result column="parent_id" property="parentId"/>
        <result column="ancestors" property="ancestors"/>
        <result column="create_time" property="createTime"/>
        <result column="create_by" property="createBy"/>
        <result column="update_time" property="updateTime"/>
        <result column="update_by" property="updateBy"/>
        <result column="del_flag" property="delFlag"/>
        <result column="remark" property="remark"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,
        name,
        classify_type,
        sort,
        statement,
        parent_id,
        ancestors,
        create_time,
        create_by,
        update_time,
        update_by,
        del_flag,
        remark
    </sql>
    <select id="getPageList" resultType="com.ibms.service.assets.web.domain.AssetsClassifyInfo"
            parameterType="com.ibms.service.assets.web.controller.assetsclassifyinfo.param.AssetsClassifyListParam">
        SELECT
        a.id,
        a.name,
        a.classify_type,
        a.sort,
        a.statement,
        a.parent_id,
        a.ancestors,
        a.create_time,
        a.create_by,
        a.update_time,
        a.update_by,
        a.del_flag,
        a.remark,
        b.id
        FROM
        t_assets_classify_info AS a
        LEFT JOIN ( SELECT id, parent_id FROM t_assets_classify_info WHERE del_flag = '0' GROUP BY parent_id ) AS b ON
        a.id = b.parent_id
        WHERE
        b.id IS NULL and a.del_flag = '0'
        <if test="classifyType != null and classifyType != '' ">
            and a.classify_type = #{assetsType}
        </if>
        order by a.create_time desc
    </select>

</mapper>
