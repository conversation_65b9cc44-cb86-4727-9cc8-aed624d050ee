<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.ibms.service.assets.web.mapper.assetsgivebackdetail.AssetsGiveBackDetailMapper">


    <select id="getDetialList"
            resultType="com.ibms.service.assets.web.mapper.assetsgivebackdetail.dto.AssetsGiveBackDetailAndArchivesDataDto">
        select
        d.id as detialId,
        d.handover_time as handoverTime,
        d.recipient as recipient,
        d.remark as remark,
        f.name as stockDataName,
        f.id as assetsId,
        f.specification as specification,
        f.place_id as placeId,
        p.place_storage as placeName
        from
        t_assets_give_back_detail d,
        t_fixed_assets_archives_base f,
        t_assets_place_info p
        where
        d.parent_base_id = ${applyBaseId}
        and d.assets_id = f.id
        and f.place_id = p.id
        order by d.create_time desc

    </select>
</mapper>
