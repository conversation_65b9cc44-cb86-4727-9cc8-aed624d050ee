<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.ibms.service.assets.web.mapper.assetsborrowgetdetail.AssetsBorrowGetDetailMapper">


    <!--    <select id="getAlreadyReceived"-->
    <!--            resultType="com.ibms.service.assets.web.mapper.assetsborrowgetdetail.dto.AssetsBorrowGetDetailAndArchivesDataDto">-->
    <!--        SELECT-->
    <!--            d.id AS detialId,-->
    <!--            d.status AS borrowStatus,-->
    <!--            b.id AS assetsId,-->
    <!--            b.name AS name,-->
    <!--            b.unit AS unit,-->
    <!--            b.brand AS brand,-->
    <!--            b.specification AS specification,-->
    <!--            b.classify_id AS classifyId,-->
    <!--            i.place_storage AS placeStorage-->
    <!--        FROM-->
    <!--            t_assets_borrow_get_detail d,-->
    <!--            t_fixed_assets_archives_base b,-->
    <!--            t_assets_place_info i-->
    <!--        WHERE-->
    <!--            d.parent_base_id = ${applyBaseId}-->
    <!--          AND d.assets_id = b.id-->
    <!--          AND i.id = b.place_id-->
    <!--        ORDER BY-->
    <!--            d.create_time DESC-->

    <!--    </select>-->

</mapper>
