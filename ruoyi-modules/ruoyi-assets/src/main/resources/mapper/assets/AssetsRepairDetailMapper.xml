<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ibms.service.assets.web.mapper.assetsrepairdetail.AssetsRepairDetailMapper">


    <resultMap id="BaseResultMap" type="com.ibms.service.assets.web.domain.AssetsRepairDetail">
        <result column="id" property="id"/>
        <result column="parent_base_id" property="parentBaseId"/>
        <result column="assets_id" property="assetsId"/>
        <result column="repair_reasons" property="repairReasons"/>
        <result column="create_by" property="createBy"/>
        <result column="create_time" property="createTime"/>
        <result column="update_by" property="updateBy"/>
        <result column="update_time" property="updateTime"/>
        <result column="del_flag" property="delFlag"/>
        <result column="remark" property="remark"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,
        parent_base_id,
        assets_id,
        repair_reasons,
        create_by,
        create_time,
        update_by,
        update_time,
        del_flag,
        remark
    </sql>
    <select id="selectDetailList"
            resultType="com.ibms.service.assets.web.controller.assetsrepairdetail.vo.AssetsRepairDetailListVo"
            parameterType="com.ibms.service.assets.web.controller.assetsrepairdetail.param.AssetsRepairDetailInfoParam">
        SELECT
        a.id,
        a.parent_base_id,
        a.assets_id,
        a.repair_reasons,
        a.create_by,
        a.create_time,
        a.update_by,
        a.update_time,
        a.del_flag,
        a.remark,
        b.name AS name,
        b.original_value_amount as originalValueAmount,
        b.warranty_condition as warrantyCondition,
        b.original_value_amount as originalValueAmount,
        b.brand as brand,
        b.assets_source as assetsSource,
        b.supplier as supplier,
        b.specification as specification
        FROM
        t_assets_repair_detail AS a
        LEFT JOIN t_fixed_assets_archives_base AS b ON a.assets_id = b.id
        where a.parent_base_id = #{applyBaseId}
        order by a.create_time desc

    </select>
    <select id="selectDetailListByInIds"
            resultType="com.ibms.service.assets.web.controller.assetsrepairdetail.vo.AssetsRepairDetailListVo"
            parameterType="java.util.List"
    >
        SELECT
        a.id,
        a.parent_base_id,
        a.assets_id,
        a.repair_reasons,
        a.create_by,
        a.create_time,
        a.update_by,
        a.update_time,
        a.del_flag,
        a.remark,
        b.name AS name,
        b.original_value_amount as originalValueAmount,
        b.warranty_condition as warrantyCondition,
        b.original_value_amount as originalValueAmount,
        b.brand as brand,
        b.assets_source as assetsSource,
        b.supplier as supplier,
        b.specification as specification
        FROM
        t_assets_repair_detail AS a
        LEFT JOIN t_fixed_assets_archives_base AS b ON a.assets_id = b.id
        where a.parent_base_id in
        <foreach collection="list" item="id" index="index" open="(" close=")" separator=",">
            #{id}
        </foreach>
        order by a.create_time desc
    </select>

</mapper>
