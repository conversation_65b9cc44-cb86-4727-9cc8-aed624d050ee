<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.ibms.service.assets.web.mapper.assetsInventoryDetail.AssetsInventoryDetailMapper">
    <resultMap id="BaseResultMap" type="com.ibms.service.assets.web.domain.AssetsInventoryDetail">
        <result column="id" property="id"/>
        <result column="parent_base_id" property="parentBaseId"/>
        <result column="assets_id" property="assetsId"/>
        <result column="inventory_time" property="inventoryTime"/>
        <result column="assets_status" property="assetsStatus"/>
        <result column="inventory_status" property="inventoryStatus"/>
        <result column="create_by" property="createBy"/>
        <result column="create_time" property="createTime"/>
        <result column="update_by" property="updateBy"/>
        <result column="update_time" property="updateTime"/>
        <result column="del_flag" property="delFlag"/>
        <result column="remark" property="remark"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,
        parent_base_id,
        assets_id,
        inventory_time,
        assets_status,
        inventory_status,
        create_by,
        create_time,
        update_by,
        update_time,
        del_flag,
        remark
    </sql>

    <select id="selectByCondition" resultType="java.util.Map">
        select aab.apply_time as applyTime,
        aid.inventory_time as inventoryTime,
        aab.apply_user_name as applyUserName,
        aab.apply_dept_name as applyDeptName,
        aid.id as id,
        aid.assets_status as assetsStatus,
        aid.remark as remark,
        aid.assets_id as assetsId,
        aid.deal_base_id as dealBaseId,
        faab.warranty_condition as warrantyCondition,
        faab.assets_source as assetsSource,
        faab.supplier as supplier,
        faab.purchase_time as purchaseTime,
        faab.original_value_amount as originalValueAmount,
        faab.classify_id as classifyId,
        faab.name as assetsName,
        faab.specification as specification,
        faab.unit as unit,
        faab.brand as brand,
        faab.use_scope as useScope
        from t_assets_apply_base aab
        left join t_assets_inventory_detail aid on aab.id = aid.parent_base_id
        left join t_fixed_assets_archives_base faab on aid.assets_id = faab.id
        <where>
            <if test="applyUserId != null and applyUserId != ''">
                and aab.apply_user_id = #{applyUserId}
            </if>
            <if test="inventoryStatus != null and inventoryStatus != ''">
                and aid.inventory_status = #{inventoryStatus}
            </if>
            <if test="applyUserName != null and applyUserName != ''">
                and aab.apply_user_name like concat('%', #{applyUserName}, '%')
            </if>
            <if test="applyTime != null">
                and aab.apply_time = #{applyTime}
            </if>
            <if test="assetsName != null and assetsName != ''">
                and faab.name like concat('%', #{assetsName}, '%')
            </if>
            <if test="specification != null and specification != ''">
                and faab.specification like concat('%', #{specification}, '%')
            </if>
            <if test="assetsStatus != null and assetsStatus != ''">
                and aid.assets_status = #{assetsStatus}
            </if>
            <if test="baseType != null and baseType != ''">
                and aab.base_type = #{baseType}
            </if>
            <if test="useScope != null and useScope != ''">
                and faab.use_scope = #{useScope}
            </if>
        </where>
    </select>
</mapper>
