<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ibms.service.assets.web.mapper.stockdatainfo.StockDataInfoMapper">


    <resultMap id="BaseResultMap" type="com.ibms.service.assets.web.domain.StockDataInfo">
        <result column="id" property="id"/>
        <result column="type" property="type"/>
        <result column="name" property="name"/>
        <result column="specification" property="specification"/>
        <result column="unit" property="unit"/>
        <result column="classify_id" property="classifyId"/>
        <result column="brand" property="brand"/>
        <result column="expected_life" property="expectedLife"/>
        <result column="affiliation_company" property="affiliationCompany"/>
        <result column="manufacturers" property="manufacturers"/>
        <result column="stock_number" property="stockNumber"/>
        <result column="maintenance_number" property="maintenanceNumber"/>
        <result column="use_number" property="useNumber"/>
        <result column="total_number" property="totalNumber"/>
        <result column="warning" property="warning"/>
        <result column="prewarning_value" property="prewarningValue"/>
        <result column="img_url" property="imgUrl"/>
        <result column="create_time" property="createTime"/>
        <result column="create_by" property="createBy"/>
        <result column="update_by" property="updateBy"/>
        <result column="update_time" property="updateTime"/>
        <result column="del_flag" property="delFlag"/>
        <result column="remark" property="remark"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,
        type,
        name,
        specification,
        unit,
        classify_id,
        brand,
        expected_life,
        affiliation_company,
        manufacturers,
        stock_number,
        maintenance_number,
        use_number,
        total_number,
        warning as warning,
        prewarning_value,
        img_url,
        create_time,
        create_by,
        update_by,
        update_time,
        del_flag,
        remark
    </sql>
    <select id="getStockDataInfoForUpdateById" resultType="com.ibms.service.assets.web.domain.StockDataInfo"
            parameterType="Integer">
        select
        id,
        type,
        name,
        specification,
        unit,
        classify_id,
        brand,
        expected_life,
        affiliation_company,
        manufacturers,
        stock_number,
        maintenance_number,
        use_number,
        total_number,
        warning as warning,
        prewarning_value,
        img_url,
        create_time,
        create_by,
        update_by,
        update_time,
        del_flag,
        remark
        from t_stock_data_info where id = #{id} for update
    </select>
    <select id="getPageList" resultType="com.ibms.service.assets.web.domain.StockDataInfo"
            parameterType="com.ibms.service.assets.web.controller.stockdatainfo.param.StockDataInfoListParam">
        select
        id,
        type,
        name,
        specification,
        unit,
        classify_id,
        brand,
        use_scope,
        expected_life,
        affiliation_company,
        manufacturers,
        stock_number,
        maintenance_number,
        use_number,
        total_number,
        warning as warning,
        prewarning_value,
        img_url,
        create_time,
        create_by,
        update_by,
        update_time,
        del_flag,
        remark
        from t_stock_data_info
        <where>
            del_flag = 0
            <if test="type != null and type != '' ">
                and type = #{type}
            </if>
            <if test="useScope != null and useScope != '' ">
                and use_scope = #{useScope}
            </if>
            <if test="brand != null and brand != '' ">
                and brand like concat('%',#{brand},'%')
            </if>
            <if test="name != null and name != '' ">
                and name like concat('%',#{name},'%')
            </if>
            <if test="status != null and status != '' ">
                and status = #{status}
            </if>
            <if test="warning != null and warning != '' ">
                and warning = #{warning}
            </if>
        </where>
        order by create_time desc
    </select>
    <select id="getStockDataInfoById" resultType="com.ibms.service.assets.web.domain.StockDataInfo"
            parameterType="integer">
        select
        id,
        type,
        name,
        specification,
        unit,
        classify_id,
        brand,
        expected_life,
        affiliation_company,
        manufacturers,
        stock_number,
        maintenance_number,
        use_number,
        total_number,
        warning as warning,
        prewarning_value,
        img_url,
        create_time,
        create_by,
        update_by,
        update_time,
        del_flag,
        remark
        from t_stock_data_info where id = #{id}
    </select>

</mapper>
