<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.ibms.service.assets.web.mapper.assetsreturnstockdetail.AssetsReturnStockDetailMapper">
    <select id="getDetialList"
            resultType="com.ibms.service.assets.web.mapper.assetsreturnstockdetail.dto.AssetsReturnStockDetailAndArchivesDto">
        SELECT
        d.id AS detailId,
        d.assets_receive_id AS assetsReceiveId,
        b.specification AS specification,
        b.name AS assetsName,
        b.id AS assetsId,
        b.unit AS unit,
        i.place_storage AS placeStorage,
        b.classify_id
        FROM
        t_assets_return_stock_detail d
        LEFT JOIN t_fixed_assets_archives_base b ON d.assets_id = b.id
        LEFT JOIN t_assets_place_info i ON i.id = d.storage_place_id
        where
        d.parent_base_id = #{applyBaseId}
        order by d.create_time desc

    </select>
</mapper>
