<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ibms.service.assets.web.mapper.assetsmaintaindetail.AssetsMaintainDetailMapper">


    <resultMap id="BaseResultMap" type="com.ibms.service.assets.web.domain.AssetsMaintainDetail">
        <result column="id" property="id"/>
        <result column="parent_base_id" property="parentBaseId"/>
        <result column="assets_id" property="assetsId"/>
        <result column="correlation_apply_id" property="correlationApplyId"/>
        <result column="problem_description" property="problemDescription"/>
        <result column="maintain_mode" property="maintainMode"/>
        <result column="maintain_time" property="maintainTime"/>
        <result column="maintain_cost" property="maintainCost"/>
        <result column="fittings_cost" property="fittingsCost"/>
        <result column="change_value_amount" property="changeValueAmount"/>
        <result column="change_cost" property="changeCost"/>
        <result column="maintain_agent" property="maintainAgent"/>
        <result column="create_by" property="createBy"/>
        <result column="create_time" property="createTime"/>
        <result column="update_by" property="updateBy"/>
        <result column="update_time" property="updateTime"/>
        <result column="del_flag" property="delFlag"/>
        <result column="remark" property="remark"/>
        <result column="original_state" property="originalState"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,
        parent_base_id,
        assets_id,
        correlation_apply_id,
        problem_description,
        maintain_mode,
        maintain_time,
        maintain_cost,
        fittings_cost,
        change_value_amount,
        change_cost,
        maintain_agent,
        original_state,
        create_by,
        create_time,
        update_by,
        update_time,
        del_flag,
        remark
    </sql>
    <select id="selectDetailList"
            resultType="com.ibms.service.assets.web.controller.assetsmaintaindetail.vo.AssetsMaintainDetailListVo"
            parameterType="com.ibms.service.assets.web.controller.assetsmaintaindetail.param.AssetsMaintainInfoParam">
        SELECT
        a.id,
        a.parent_base_id,
        a.assets_id,
        a.correlation_apply_id,
        a.problem_description,
        a.maintain_mode,
        a.maintain_time,
        a.maintain_cost,
        a.fittings_cost,
        a.change_value_amount,
        a.change_cost,
        a.maintain_agent,
        a.original_state,
        a.create_by,
        a.create_time,
        a.update_by,
        a.update_time,
        a.del_flag,
        a.remark,
        b.name AS name,
        b.original_value_amount as originalValueAmount,
        b.warranty_condition as warrantyCondition,
        b.original_value_amount as originalValueAmount,
        b.brand as brand,
        b.assets_source as assetsSource,
        b.supplier as supplier,
        b.specification as specification
        FROM
        t_assets_maintain_detail AS a
        LEFT JOIN t_fixed_assets_archives_base AS b ON a.assets_id = b.id
        where a.parent_base_id = #{applyBaseId}
        order by a.create_time desc
    </select>


</mapper>
