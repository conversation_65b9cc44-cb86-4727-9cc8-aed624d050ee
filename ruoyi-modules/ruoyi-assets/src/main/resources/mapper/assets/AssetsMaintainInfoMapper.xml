<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.ibms.service.assets.web.mapper.assetsmaintaininfo.AssetsMaintainInfoMapper">
    <resultMap id="BaseResultMap"
               type="com.ibms.service.assets.web.domain.AssetsMaintainInfo">
        <result column="id" property="id"/>
        <result column="assets_id" property="assetsId"/>
        <result column="maintain_time" property="maintainTime"/>
        <result column="maintain_cost" property="maintainCost"/>
        <result column="last_maintain_time" property="lastMaintainTime"/>
        <result column="next_maintain_time" property="nextMaintainTime"/>
        <result column="agent_user_id" property="agentUserId"/>
        <result column="agent_user_name" property="agentUserName"/>
        <result column="agent_dept_id" property="agentDeptId"/>
        <result column="agent_dept_name" property="agentDeptName"/>
        <result column="create_by" property="createBy"/>
        <result column="create_time" property="createTime"/>
        <result column="update_by" property="updateBy"/>
        <result column="update_time" property="updateTime"/>
        <result column="del_flag" property="delFlag"/>
        <result column="remark" property="remark"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,
        assets_id,
        maintain_time,
        maintain_cost,
        last_maintain_time,
        next_maintain_time,
        agent_user_id,
        agent_user_name,
        agent_dept_id,
        agent_dept_name,
        create_by,
        create_time,
        update_by,
        update_time,
        del_flag,
        remark
    </sql>
    <select id="getInfoById"
            resultType="com.ibms.service.assets.web.controller.assetsmaintaininfo.vo.AssetsMaintainInfoGetByIdVo"
            parameterType="String">

        SELECT
        a.id,
        a.assets_id,
        a.maintain_time,
        a.maintain_cost,
        a.last_maintain_time,
        a.next_maintain_time,
        a.agent_user_id,
        a.agent_user_name,
        a.agent_dept_id,
        a.agent_dept_name,
        a.create_by,
        a.create_time,
        a.update_by,
        a.update_time,
        a.del_flag,
        a.remark,
        b.name AS assetsName,
        b.original_value_amount as originalValueAmount,
        b.specification as specification,
        b.use_scope as useScope
        FROM
        t_assets_maintain_info AS a
        LEFT JOIN t_fixed_assets_archives_base AS b ON a.assets_id = b.id
        where a.id = #{id}


    </select>
</mapper>
