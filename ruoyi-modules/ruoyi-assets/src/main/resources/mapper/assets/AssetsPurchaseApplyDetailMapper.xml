<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ibms.service.assets.web.mapper.assetspurchaseapplydetail.AssetsPurchaseApplyDetailMapper">


    <resultMap id="BaseResultMap" type="com.ibms.service.assets.web.domain.AssetsPurchaseApplyDetail">
        <result column="id" property="id"/>
        <result column="parent_base_id" property="parentBaseId"/>
        <result column="stock_data_id" property="stockDataId"/>
        <result column="correlation_apply_id" property="correlationApplyId"/>
        <result column="number" property="number"/>
        <result column="expect_unit_price" property="expectUnitPrice"/>
        <result column="supplier" property="supplier"/>
        <result column="create_by" property="createBy"/>
        <result column="create_time" property="createTime"/>
        <result column="update_by" property="updateBy"/>
        <result column="update_time" property="updateTime"/>
        <result column="del_flag" property="delFlag"/>
        <result column="remark" property="remark"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,
        parent_base_id,
        stock_data_id,
        correlation_apply_id,
        number,
        expect_unit_price,
        supplier,
        create_by,
        create_time,
        update_by,
        update_time,
        del_flag,
        remark
    </sql>

    <select id="selectDetailList"
            resultType="com.ibms.service.assets.web.controller.assetspurchaseapplydetail.vo.AssetsPurchaseApplyDetailVo"
            parameterType="com.ibms.service.assets.web.controller.assetspurchaseapplydetail.param.AssetsPurchaseApplyDetailInfoParam"
    >
        SELECT
        a.id,
        a.parent_base_id,
        a.stock_data_id,
        a.correlation_apply_id,
        a.number,
        a.expect_unit_price,
        a.supplier,
        a.create_by,
        a.create_time,
        a.update_by,
        a.update_time,
        a.del_flag,
        a.remark,
        b.name AS stockDataName,
        b.unit AS unit,
        b.specification AS specification
        FROM
        t_assets_purchase_apply_detail AS a
        LEFT JOIN t_stock_data_info AS b ON a.stock_data_id = b.id
        WHERE
        a.parent_base_id = #{applyBaseId}
        ORDER BY
        a.create_time DESC

    </select>


    <select id="getDetailListByIds"
            resultType="com.ibms.service.assets.web.controller.assetspurchaseapplydetail.vo.AssetsPurchaseApplyDetailVo"
            parameterType="java.util.List">
        SELECT
        a.id,
        a.parent_base_id,
        a.stock_data_id,
        a.correlation_apply_id,
        a.number,
        a.expect_unit_price,
        a.supplier,
        a.create_by,
        a.create_time,
        a.update_by,
        a.update_time,
        a.del_flag,
        a.remark,
        b.name AS stockDataName,
        b.unit AS unit,
        b.specification AS specification
        FROM
        t_assets_purchase_apply_detail AS a
        LEFT JOIN t_stock_data_info AS b ON a.stock_data_id = b.id
        WHERE
        a.parent_base_id IN
        <foreach collection="list" item="id" index="index" open="(" close=")" separator=",">
            #{id}
        </foreach>
        ORDER BY
        a.create_time DESC
    </select>


</mapper>
