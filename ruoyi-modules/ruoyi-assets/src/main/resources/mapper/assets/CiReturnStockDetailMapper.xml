<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.ibms.service.assets.web.mapper.cireturnstockdetail.CiReturnStockDetailMapper">
    <select id="getDetialList"
            resultType="com.ibms.service.assets.web.mapper.cireturnstockdetail.dto.CiReturnStockDetailAndStockDto"
            parameterType="String">

        select
        b.name as name,
        b.id as stockDataId,
        b.unit as unit,
        b.brand as brand,
        a.number,
        b.specification as specification,
        b.classify_id,
        b.use_scope as useScope

        from
        t_ci_return_stock_detail a,
        t_stock_data_info b
        where
        a.parent_base_id = #{applyBaseId}
        and a.stock_data_id = b.id
        order by a.create_time desc

    </select>
</mapper>
