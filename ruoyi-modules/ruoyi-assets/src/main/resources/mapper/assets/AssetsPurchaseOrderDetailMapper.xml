<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ibms.service.assets.web.mapper.assetspurchaseorderdetail.AssetsPurchaseOrderDetailMapper">


    <resultMap id="BaseResultMap" type="com.ibms.service.assets.web.domain.AssetsPurchaseOrderDetail">
        <result column="id" property="id"/>
        <result column="parent_base_id" property="parentBaseId"/>
        <result column="stock_data_id" property="stockDataId"/>
        <result column="correlation_apply_id" property="correlationApplyId"/>
        <result column="number" property="number"/>
        <result column="unit_price" property="unitPrice"/>
        <result column="total_price" property="totalPrice"/>
        <result column="supplier" property="supplier"/>
        <result column="acceptor_id" property="acceptorId"/>
        <result column="acceptor_name" property="acceptorName"/>
        <result column="create_by" property="createBy"/>
        <result column="create_time" property="createTime"/>
        <result column="update_by" property="updateBy"/>
        <result column="update_time" property="updateTime"/>
        <result column="del_flag" property="delFlag"/>
        <result column="remark" property="remark"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,
        parent_base_id,
        stock_data_id,
        correlation_apply_id,
        number,
        unit_price,
        total_price,
        supplier,
        acceptor_id,
        acceptor_name,
        create_by,
        create_time,
        update_by,
        update_time,
        del_flag,
        remark
    </sql>


    <select id="getDetailListByIds"
            resultType="com.ibms.service.assets.web.controller.assetspurchaseorderdetail.vo.AssetsPurchaseOrderDetailListVo"
            parameterType="java.util.List">
        SELECT
        a.id,
        a.parent_base_id,
        a.stock_data_id,
        a.correlation_apply_id,
        a.number,
        a.unit_price,
        a.total_price,
        a.supplier,
        a.acceptor_id,
        a.acceptor_name,
        a.create_by,
        a.create_time,
        a.update_by,
        a.update_time,
        a.del_flag,
        a.remark,
        b.name AS stockDataName,
        b.unit AS unit,
        b.specification AS specification
        FROM
        t_assets_purchase_order_detail AS a
        LEFT JOIN t_stock_data_info AS b ON a.stock_data_id = b.id
        WHERE
        a.parent_base_id IN
        <foreach collection="list" item="id" index="index" open="(" close=")" separator=",">
            #{id}
        </foreach>
        ORDER BY
        a.create_time DESC
    </select>
    <select id="selectDetailList"
            resultType="com.ibms.service.assets.web.controller.assetspurchaseorderdetail.vo.AssetsPurchaseOrderDetailListVo"
            parameterType="com.ibms.service.assets.web.controller.assetspurchaseorderdetail.param.AssetsPurchaseOrderDetailInfoParam"
    >
        SELECT
        a.id,
        a.parent_base_id,
        a.stock_data_id,
        a.correlation_apply_id,
        a.number,
        a.unit_price,
        a.total_price,
        a.supplier,
        a.acceptor_id,
        a.acceptor_name,
        a.create_by,
        a.create_time,
        a.update_by,
        a.update_time,
        a.del_flag,
        a.remark,
        b.name AS stockDataName,
        b.unit AS unit,
        b.specification AS specification
        FROM
        t_assets_purchase_order_detail AS a
        LEFT JOIN t_stock_data_info AS b ON a.stock_data_id = b.id
        WHERE
        a.parent_base_id = #{applyBaseId}
        ORDER BY
        a.create_time DESC

    </select>
    <select id="acceptancePageList" resultType="com.ibms.service.assets.web.domain.AssetsApplyBase"
            parameterType="com.ibms.service.assets.web.controller.assetspurchaseorderdetail.param.AcceptancePageListParam">

        SELECT
        b.id,
        b.base_type,
        b.apply_level,
        b.proc_ins_id,
        b.apply_user_id,
        b.apply_user_name,
        b.apply_dept_id,
        b.apply_dept_name,
        b.apply_time,
        b.instructions,
        b.files_url,
        b.correlation_select_id,
        b.base_status,
        b.apply_total,
        b.treated_number,
        b.keeper_treated_number,
        b.create_by,
        b.create_time,
        b.update_by,
        b.update_time,
        b.del_flag,
        b.remark,
        a.id,
        a.parent_base_id,
        a.acceptor_id
        FROM
        ( SELECT id, parent_base_id, acceptor_id FROM t_assets_purchase_order_detail WHERE del_flag = '0' GROUP BY
        parent_base_id, acceptor_id ) AS a
        LEFT JOIN t_assets_apply_base AS b ON a.parent_base_id = b.id
        <where>
            b.base_status = '1'
            and b.base_type = '13'
            and b.del_flag = '0'
            <if test="applyUserName != null and applyUserName != ''">
                and b.apply_user_name like concat('%',#{applyUserName},'%')
            </if>
            <if test="applyLevel != null and applyLevel != ''">
                and b.apply_level = #{applyLevel}
            </if>
            <if test="id != null and id != ''">
                and b.id like concat('%',#{id},'%')
            </if>
            <if test="acceptorId != null and acceptorId != ''">
                and a.acceptor_id = #{acceptorId}
            </if>
        </where>
        order by b.create_time desc


    </select>


</mapper>
