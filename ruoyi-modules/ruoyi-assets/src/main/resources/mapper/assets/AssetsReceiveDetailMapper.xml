<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.ibms.service.assets.web.mapper.assetsreceivedetail.AssetsReceiveDetailMapper">

    <resultMap id="BaseResultMap" type="com.ibms.service.assets.web.domain.AssetsReceiveDetail">
        <result column="id" property="id"/>
        <result column="parent_base_id" property="parentBaseId"/>
        <result column="stock_data_id" property="stockDataId"/>
        <result column="assets_type_code" property="assetsTypeCode"/>
        <result column="keeper_number" property="keeperNumber"/>
        <result column="apply_number" property="applyNumber"/>
        <result column="use_place" property="usePlace"/>
        <result column="use_person_id" property="usePersonId"/>
        <result column="use_person_name" property="usePersonName"/>
        <result column="receive_type" property="receiveType"/>
        <result column="create_by" property="createBy"/>
        <result column="create_time" property="createTime"/>
        <result column="update_by" property="updateBy"/>
        <result column="update_time" property="updateTime"/>
        <result column="del_flag" property="delFlag"/>
        <result column="remark" property="remark"/>
    </resultMap>

    <sql id="Base_Column_List">
        a.id,
        a.parent_base_id,
        a.stock_data_id,
        a.assets_type_code,
        a.keeper_number,
        a.apply_number,
        a.use_place,
        a.use_person_id,
        a.use_person_name,
        a.receive_type,
        a.create_by,
        a.create_time,
        a.update_by,
        a.update_time,
        a.del_flag,
        a.remark
    </sql>


    <select id="getDetialList"
            resultType="com.ibms.service.assets.web.mapper.assetsreceivedetail.dto.AssetsReceiveDetailAndStockDataDto">
        select
        d.id as detialId,
        d.receive_type as receiveType,
        s.name as stockDataName,
        s.specification as specification,
        s.unit as unit,
        s.id as stockDataId,
        s.classify_id as classifyId,
        s.brand as brand,
        d.use_person_id as usePersonId,
        d.use_person_name as usePersonName,
        d.use_place as usePlace,
        d.apply_number as applyNumber
        from t_assets_receive_detail d,
        t_stock_data_info s
        where d.parent_base_id = ${applyBaseId}
        and d.stock_data_id = s.id
        order by 'create_time' desc;

    </select>

    <select id="selectNoFinish"
            resultType="com.ibms.service.assets.web.mapper.assetsreceivedetail.dto.AssetsReceiveDetailAndStockDataFinishDto"
            parameterType="String">
        SELECT
        t.id AS detailId,
        IFNULL(t.keeper_number,0) AS keeperNumber,
        t.apply_number AS applyNumber,
        s.name AS name,
        s.specification AS specification,
        s.unit AS unit,
        s.classify_id AS classifyId,
        s.brand AS brand,
        s.id as stockDataId,
        s.stock_number AS stockNumber,
        s.use_scope AS useScope

        FROM
        t_assets_receive_detail t left join
        t_stock_data_info s on t.stock_data_id = s.id
        WHERE
        t.parent_base_id = #{applyBaseId}
        and IFNULL(t.keeper_number,0) &lt; t.apply_number
        <if test="null != assetsName and '' != assetsName">
            and s.name like concat('%',#{assetsName},'%')
        </if>
        ORDER BY s.name COLLATE utf8mb4_unicode_ci;

    </select>
    <select id="getDetailListByIds"
            resultType="com.ibms.service.assets.web.controller.assetspurchaseapplydetail.vo.AllReceiveDetailListByIdsVo"
            parameterType="java.util.List">
        SELECT
        a.id,
        a.parent_base_id,
        a.stock_data_id,
        a.apply_number,
        a.del_flag,
        b.name AS stockDataName,
        b.unit AS unit,
        b.specification AS specification
        FROM
        t_assets_receive_detail AS a
        LEFT JOIN t_stock_data_info AS b ON a.stock_data_id = b.id
        WHERE
        a.parent_base_id IN
        <foreach collection="list" item="id" index="index" open="(" close=")" separator=",">
            #{id}
        </foreach>
        ORDER BY
        a.create_time DESC
    </select>
    <select id="getFixedAssetsByStockDataIdList"
            resultType="com.ibms.service.assets.web.mapper.assetsreceivedetail.dto.FixedAssetsByStockDataIdListDto"
            parameterType="com.ibms.service.assets.web.controller.assetsreceivedetail.param.FixedAssetsByStockDataIdParam">
        SELECT
        a.id as assets_id,
        a.stock_data_id,
        a.name as assetsName,
        a.brand,
        a.specification,
        a.classify_id,
        a.unit,
        a.status,
        a.place_id,
        a.use_scope AS useScope,
        c.place_storage,
        b.id as detailId
        FROM
        t_fixed_assets_archives_base AS a
        LEFT JOIN t_assets_receive_detail AS b ON a.stock_data_id = b.stock_data_id
        LEFT JOIN t_assets_place_info as c on a.place_id = c.id
        WHERE
        a.status = '1' and a.del_flag = '0'
        <if test="assetsId != null and assetsId != ''">
            and a.id like concat('%',#{assetsId},'%')
        </if>
        <if test="assetsName != null and assetsName != ''">
            and a.name like concat('%',#{assetsName},'%')
        </if>
        <if test="useScope != null and useScope != ''">
            and a.use_scope = #{useScope}
        </if>
        <if test="detailIdLists != null ">
            and b.id in
            <foreach collection="detailIdLists" item="detailId" open="(" close=")" separator=",">
                #{detailId}
            </foreach>
        </if>
        order by a.create_time desc
    </select>

</mapper>
