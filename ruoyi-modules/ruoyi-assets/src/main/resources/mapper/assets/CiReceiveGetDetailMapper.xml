<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.ibms.service.assets.web.mapper.cireceivegetdetail.CiReceiveGetDetailMapper">

    <select id="sumGetNumer" resultType="java.lang.Integer">
        select ifnull(sum(number),0) from t_ci_receive_get_detail where ci_receive_detail_id = ${detialId} and
        receive_status = 2;
    </select>

    <select id="getCiAlreadyReceive"
            resultType="com.ibms.service.assets.web.mapper.cireceivegetdetail.dto.CiAlreadyReceiveDto">
        select
        s.name as name,
        s.specification as specification,
        s.classify_id as classifyId,
        s.brand as brand,
        s.unit as unit,
        s.id as stockDataId,
        s.use_scope as useScope
        from
        t_stock_data_info s
        where
        s.id in ( select distinct stock_data_id from t_ci_receive_get_detail where recipient_id = ${userId} and
        receive_status = 2 )
        <if test="null != assetsName and '' != assetsName">
            and s.name like '%'+${assetsName}+'%'
        </if>
        <if test="null != specification and '' != specification">
            and s.specification like '%'+${specification}+'%'
        </if>
        <if test="useScope != null and useScope != ''">
            and s.use_scope = #{useScope}
        </if>
        order by s.create_time desc

    </select>

</mapper>
