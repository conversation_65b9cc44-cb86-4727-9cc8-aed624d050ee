<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.ibms.service.assets.web.mapper.assetsborrowdetail.AssetsBorrowDetailMapper">

    <select id="getDetialList"
            resultType="com.ibms.service.assets.web.mapper.assetsborrowdetail.dto.AssetsBorrowDetailAndStockDataDto">
        select
        d.id as detialId,
        d.borrow_number as applyNumber,
        d.stock_data_id as stockDataId,
        i.name as stockDataName,
        i.unit as unit,
        i.brand as brand,
        i.classify_id as classifyId,
        i.specification as specification
        from t_assets_borrow_detail d left join t_stock_data_info i on d.stock_data_id = i.id
        where d.parent_base_id = ${applyBaseId}
        order by d.create_time desc
    </select>


    <select id="selectNoFinish"
            resultType="com.ibms.service.assets.web.mapper.assetsborrowdetail.dto.AssetsBorrowDetailAndStockDataFinishDto">
        select
        t.id as detailId,
        t.keeper_number as keeperNumber,
        t.borrow_number as applyNumber,
        s.name as name,
        s.specification as specification,
        s.unit as unit,
        s.classify_id as classifyId,
        s.brand as brand,
        s.id as stockDataId,
        s.stock_number as stockNumber,
        s.use_scope as useScope

        from t_assets_borrow_detail t
        left join t_stock_data_info s on s.id = t.stock_data_id
        where t.parent_base_id = ${applyBaseId}
        and t.keeper_number &lt; t.borrow_number
        <if test="null != assetsName and '' != assetsName">
            and s.name like CONCAT('%',#{assetsName},'%')
        </if>
        order by t.create_time desc

    </select>

    <select id="getAlreadyReceived"
            resultType="com.ibms.service.assets.web.mapper.assetsborrowdetail.dto.AssetsBorrowDetailAndArchivesDataDto">
        SELECT
        d.id AS detailId,
        d.special_status AS borrowStatus,
        b.id AS assetsId,
        b.name AS name,
        b.unit AS unit,
        b.brand AS brand,
        b.specification AS specification,
        b.classify_id AS classifyId,
        i.place_storage AS placeStorage
        FROM
        t_assets_receive_get_detail d
        left join t_fixed_assets_archives_base b on d.assets_id = b.id
        left join t_assets_place_info i on i.id = b.place_id
        WHERE d.parent_base_id = ${applyBaseId}
        AND d.receive_status = 2
        ORDER BY d.create_time DESC

    </select>


</mapper>
