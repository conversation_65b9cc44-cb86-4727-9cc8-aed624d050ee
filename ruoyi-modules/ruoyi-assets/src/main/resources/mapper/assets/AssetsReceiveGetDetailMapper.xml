<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.ibms.service.assets.web.mapper.assetsreceivegetdetail.AssetsReceiveGetDetailMapper">
    <select id="getAlreadyGet"
            resultType="com.ibms.service.assets.web.mapper.assetsreceivegetdetail.dto.AssetsReceiveGetDetailAlreadyDto">

        SELECT a.id AS assetsId,
        a.name AS name,
        a.unit AS unit,
        a.brand AS brand,
        a.classify_id AS classifyId,
        a.specification AS specification,
        a.use_place AS placeStorage
        FROM t_assets_receive_get_detail g
        INNER JOIN
        t_fixed_assets_archives_base a ON g.assets_id = a.id
        WHERE g.parent_base_id = ${applyBaseId}
        AND g.receive_status = ${receiveStatus}
        ORDER BY g.create_time DESC

    </select>
    <select id="getNotReturnStockReceivedList"
            resultType="com.ibms.service.assets.web.mapper.assetsreceivegetdetail.dto.NotReceivedListDto"
            parameterType="String">
        SELECT
        a.id as assetsId,
        a.stock_data_id,
        a.name as name,
        a.unit,
        a.brand,
        a.classify_id,
        a.specification,
        a.use_scope as useScope,
        b.id as detailId

        FROM
        t_fixed_assets_archives_base AS a
        LEFT JOIN t_assets_return_stock_detail AS b ON a.id = b.assets_id
        WHERE
        b.receive_status = '1'
        and b.parent_base_id = #{applyBaseId}
        order by b.create_time desc
    </select>
    <select id="getNotGiveBackReceivedList"
            resultType="com.ibms.service.assets.web.mapper.assetsreceivegetdetail.dto.NotReceivedListDto">

        SELECT
        a.id as assetsId,
        a.stock_data_id,
        a.name as name,
        a.unit,
        a.brand,
        a.classify_id,
        a.specification,
        a.use_scope as useScope,
        b.id as detailId

        FROM
        t_fixed_assets_archives_base AS a
        LEFT JOIN t_assets_give_back_detail AS b ON a.id = b.assets_id
        WHERE
        b.receive_status = '1'
        and b.parent_base_id = #{applyBaseId}
        order by b.create_time desc
    </select>
</mapper>
