<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.ibms.service.assets.web.mapper.cireceivedetail.CiReceiveDetailMapper">


    <select id="getDetialList"
            resultType="com.ibms.service.assets.web.mapper.cireceivedetail.dto.CiReceiveDetailAndStockDataDto">
        select
        r.id as detialId,
        r.apply_number as applyNumber,
        s.name as stockDataName,
        s.specification as specification,
        s.unit as unit,
        s.brand as brand,
        s.stock_number as stockNumber,
        s.id as stockDataId,
        s.classify_id as classifyId
        from
        t_ci_receive_detail r,
        t_stock_data_info s
        where
        r.parent_base_id = ${applyBaseId}
        and r.stock_data_id = s.id
        <if test="null != status and '' != status">
            and r.status = ${status}
        </if>
        order by r.create_time desc
    </select>

    <select id="getCiDetailListByIds"
            resultType="com.ibms.service.assets.web.controller.assetspurchaseapplydetail.vo.AllReceiveDetailListByIdsVo"
            parameterType="java.util.List">
        SELECT
        a.id,
        a.parent_base_id,
        a.stock_data_id,
        a.apply_number,
        a.del_flag,
        b.name AS stockDataName,
        b.unit AS unit,
        b.specification AS specification
        FROM
        t_ci_receive_detail AS a
        LEFT JOIN t_stock_data_info AS b ON a.stock_data_id = b.id
        WHERE
        a.del_flag = '0' and
        a.parent_base_id IN
        <foreach collection="list" item="id" index="index" open="(" close=")" separator=",">
            #{id}
        </foreach>
        ORDER BY
        a.create_time DESC
    </select>

    <select id="getKeeperNoAllotDetail"
            resultType="com.ibms.service.assets.web.mapper.cireceivedetail.dto.KeeperNoAllotDetailDto">
        select
        r.id as ciReceiveDetailId,
        r.apply_number as applyNumber,
        r.issue_number as issueNumber,
        s.stock_number as stockNumber,
        s.name as name,
        s.specification as specification,
        s.classify_id as classifyId,
        s.unit as unit,
        s.id as stockDataId,
        s.use_scope as useScope,
        s.brand as brand
        from
        t_ci_receive_detail r,
        t_stock_data_info s
        where r.status = 2
        and r.stock_data_id = s.id
        and r.parent_base_id = ${applyBaseId}
        <if test="null != name and '' != name">
            and s.name like '%${name}%'
        </if>
        <if test="null != specification and '' != specification">
            and s.specification like '%${specification}%'
        </if>
        order by s.create_time desc

    </select>

</mapper>
