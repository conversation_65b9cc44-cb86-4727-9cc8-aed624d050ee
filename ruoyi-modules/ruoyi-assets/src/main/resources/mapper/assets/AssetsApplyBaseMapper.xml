<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.ibms.service.assets.web.mapper.assetsapplybase.AssetsApplyBaseMapper">

    <resultMap id="BaseResultMap" type="com.ibms.service.assets.web.domain.AssetsApplyBase">
        <result column="id" property="id"/>
        <result column="base_type" property="baseType"/>
        <result column="apply_level" property="applyLevel"/>
        <result column="proc_ins_id" property="procInsId"/>
        <result column="apply_user_id" property="applyUserId"/>
        <result column="apply_user_name" property="applyUserName"/>
        <result column="apply_dept_id" property="applyDeptId"/>
        <result column="apply_dept_name" property="applyDeptName"/>
        <result column="apply_time" property="applyTime"/>
        <result column="instructions" property="instructions"/>
        <result column="files_url" property="filesUrl"/>
        <result column="release_status" property="releaseStatus"/>
        <result column="flow_status" property="flowStatus"/>
        <result column="correlation_select_id" property="correlationSelectId"/>
        <result column="base_status" property="baseStatus"/>
        <result column="apply_total" property="applyTotal"/>
        <result column="treated_number" property="treatedNumber"/>
        <result column="keeper_treated_number" property="keeperTreatedNumber"/>
        <result column="create_by" property="createBy"/>
        <result column="create_time" property="createTime"/>
        <result column="update_by" property="updateBy"/>
        <result column="update_time" property="updateTime"/>
        <result column="del_flag" property="delFlag"/>
        <result column="remark" property="remark"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,
        base_type,
        apply_level,
        proc_ins_id,
        apply_user_id,
        apply_user_name,
        apply_dept_id,
        apply_dept_name,
        instructions,
        files_url,
        release_status,
        flow_status,
        correlation_select_id,
        base_status,
        apply_total,
        treated_number,
        keeper_treated_number,
        create_by,
        create_time,
        update_by,
        update_time,
        del_flag,
        remark
    </sql>
    <select id="getAllUseAssetsNumber"
            resultType="com.ibms.service.assets.web.controller.assetshomepage.vo.AllUseAssetsAndCiNumberVo">
        SELECT
        apply_dept_name AS deptName,
        COUNT( apply_total ) AS number
        FROM
        ( SELECT parent_base_id FROM t_assets_receive_detail GROUP BY parent_base_id UNION ALL SELECT parent_base_id
        FROM t_assets_borrow_detail GROUP BY parent_base_id ) AS a
        LEFT JOIN t_assets_apply_base AS b ON a.parent_base_id = b.id
        where b.audit_status = '2'
        GROUP BY
        apply_dept_name
        order by number desc
    </select>
    <select id="getAllUseCiNumber"
            resultType="com.ibms.service.assets.web.controller.assetshomepage.vo.AllUseAssetsAndCiNumberVo">
        SELECT
        apply_dept_name AS deptName,
        COUNT( apply_total ) AS number
        FROM
        ( SELECT parent_base_id FROM t_ci_receive_detail GROUP BY parent_base_id ) AS a
        LEFT JOIN t_assets_apply_base AS b on a.parent_base_id = b.id
        where b.audit_status = '2'
        group by apply_dept_name
        order by number desc
    </select>

</mapper>
