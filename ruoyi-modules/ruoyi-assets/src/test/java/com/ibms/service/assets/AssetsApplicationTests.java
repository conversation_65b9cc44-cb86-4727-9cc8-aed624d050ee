package com.ibms.service.assets;

import org.flowable.engine.*;
import org.flowable.engine.impl.cfg.StandaloneProcessEngineConfiguration;
import org.flowable.engine.repository.Deployment;
import org.flowable.engine.repository.ProcessDefinition;
import org.flowable.engine.runtime.ProcessInstance;
import org.flowable.task.api.Task;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

@SpringBootTest
class AssetsApplicationTests {

    private ProcessEngineConfiguration cfg = null;


    @BeforeEach
    public void before() {
        cfg = new StandaloneProcessEngineConfiguration()
                .setJdbcUrl("***************************************************************************************")
                .setJdbcUsername("ibms-assets")
                .setJdbcPassword("PAxnYxC5j32FrX26")
                .setJdbcDriver("com.mysql.cj.jdbc.Driver")
                // 如果数据库表结构不存在就新建
                .setDatabaseSchemaUpdate(ProcessEngineConfiguration.DB_SCHEMA_UPDATE_TRUE);
    }


    /**
     * 部署流程
     */
    @Test
    public void deploy() {

        ProcessEngine processEngine = cfg.buildProcessEngine();

        RepositoryService repositoryService = processEngine.getRepositoryService();
        Deployment deployment = repositoryService.createDeployment()
                .addClasspathResource("jeeplus-单步骤.bpmn20.xml")
                .name("请求流程")
                .deploy();

        System.out.println(deployment.getId() + deployment.getName());

    }


    /**
     * 启动一个流程实例
     */
    @Test
    public void runProcess() {

        ProcessEngine processEngine = cfg.buildProcessEngine();

        // 通过RuntimeService来启动流程实例
        RuntimeService runtimeService = processEngine.getRuntimeService();
        Map<String, Object> params = new HashMap();
        params.put("employee", "张三");// 员工姓名
        params.put("leaveDays", 3);
        params.put("reason", "工作累了，想出去玩玩！");

        ProcessInstance holidayRequest = runtimeService.startProcessInstanceByKey("Process_1671672545721", params);
        System.out.println("流程定义id为：" + holidayRequest.getProcessInstanceId());
        System.out.println("流程定义名称为：" + holidayRequest.getName());
        System.out.println("业务id为：" + holidayRequest.getBusinessKey());
        System.out.println("getActivityId：" + holidayRequest.getActivityId());
        System.out.println("getActivityId：" + holidayRequest.getId());


    }

    /**
     * 查询流程
     */
    @Test
    public void deployQuery() {

        ProcessEngine processEngine = cfg.buildProcessEngine();

        RepositoryService repositoryService = processEngine.getRepositoryService();
//        DeploymentQuery deploymentQuery = repositoryService.createDeploymentQuery();//创建查询对象
//        Deployment deployment = deploymentQuery.deploymentId("2501").singleResult();

        ProcessDefinition processDefinition = repositoryService.createProcessDefinitionQuery()  // 流程定义
                .deploymentId("20001")
                .singleResult();


        System.out.println(processDefinition.getId());
        System.out.println(processDefinition.getName());
        System.out.println(processDefinition.getDescription());
        System.out.println(processDefinition.getDeploymentId());
    }

    /**
     * 删除流程定义
     */
    @Test
    public void deleleDeploy() {

        ProcessEngine processEngine = cfg.buildProcessEngine();

        RepositoryService repositoryService = processEngine.getRepositoryService();
        // 如果部署的流程已经启动则不允许删除
//        repositoryService.deleteDeployment("2501");

        repositoryService.deleteDeployment("2501", true);// 级联删除


    }


    /**
     * 查询任务
     */
    @Test
    public void queryTask() {
        ProcessEngine processEngine = cfg.buildProcessEngine();
        TaskService taskService = processEngine.getTaskService();
        List<Task> list = taskService.createTaskQuery()
                .processDefinitionKey("holidayRequest")// 指定查询的流程编号
                .taskAssignee("zhangsan").list();

        for (Task task : list) {
            System.out.println("id:" + task.getId());
            System.out.println("name:" + task.getName());
            System.out.println("注释：" + task.getDescription());
            System.out.println("代理人" + task.getAssignee());
            System.out.println(task.getTaskDefinitionKey());
        }

    }

    /**
     * 处理完成当前任务
     */
    @Test
    public void completeTask() {
        ProcessEngine processEngine = cfg.buildProcessEngine();

        TaskService taskService = processEngine.getTaskService();
        List<Task> list = taskService.createTaskQuery()
                .processDefinitionKey("holidayRequest")
                .taskAssignee("zhangsan")
                .list();
        for (Task task : list) {
            // 创建流程变量
            Map<String, Object> map = new HashMap<>();
            map.put("approved", false);

            taskService.complete(task.getId(), map);
        }


    }


}
