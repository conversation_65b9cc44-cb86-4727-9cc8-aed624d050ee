package com.ruoyi.custom.admin.mealManagement.controller;

import java.util.List;
import javax.servlet.http.HttpServletResponse;

import com.ruoyi.common.core.web.domain.TAjaxResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.log.annotation.Log;
import com.ruoyi.common.log.enums.BusinessType;
import com.ruoyi.custom.admin.mealManagement.domain.RecipeMenuTypeBaseInfo;
import com.ruoyi.custom.admin.mealManagement.service.IRecipeMenuTypeBaseInfoService;
import com.ruoyi.common.core.web.controller.BaseController;
import com.ruoyi.common.core.web.domain.AjaxResult;
import com.ruoyi.common.core.utils.poi.ExcelUtil;
import com.ruoyi.common.core.web.page.TableDataInfo;
import springfox.documentation.annotations.ApiIgnore;

/**
 * 食谱菜单类型基本信息Controller
 *
 * <AUTHOR>
 * @date 2022-04-21
 */
@RestController
@RequestMapping("/recipeMenuTypeBaseInfo")
@Api(value = "食谱菜单类型基本信息", tags = "食谱菜单类型基本信息")
public class RecipeMenuTypeBaseInfoController extends BaseController {
    @Autowired
    private IRecipeMenuTypeBaseInfoService recipeMenuTypeBaseInfoService;

    /**
     * 查询食谱菜单类型基本信息列表
     */
    //@RequiresPermissions("mealManagement:recipeMenuTypeBaseInfo:list")
    @GetMapping("/list")
    @ApiIgnore
    public TableDataInfo list(RecipeMenuTypeBaseInfo recipeMenuTypeBaseInfo) {
        startPage();
        List<RecipeMenuTypeBaseInfo> list = recipeMenuTypeBaseInfoService.selectRecipeMenuTypeBaseInfoList(recipeMenuTypeBaseInfo);
        return getDataTable(list);
    }

    /**
     * 导出食谱菜单类型基本信息列表
     */
    //@RequiresPermissions("mealManagement:recipeMenuTypeBaseInfo:export")
    @Log(platform = "1", title = "食谱菜单类型基本信息", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    @ApiIgnore
    public void export(HttpServletResponse response, RecipeMenuTypeBaseInfo recipeMenuTypeBaseInfo) {
        List<RecipeMenuTypeBaseInfo> list = recipeMenuTypeBaseInfoService.selectRecipeMenuTypeBaseInfoList(recipeMenuTypeBaseInfo);
        ExcelUtil<RecipeMenuTypeBaseInfo> util = new ExcelUtil<RecipeMenuTypeBaseInfo>(RecipeMenuTypeBaseInfo.class);
        util.exportExcel(response, list, "食谱菜单类型基本信息数据");
    }

    /**
     * 获取食谱菜单类型基本信息详细信息
     */
    //@RequiresPermissions("mealManagement:recipeMenuTypeBaseInfo:query")
    @GetMapping(value = "/{id}")
    @ApiOperation(value = "获取食谱菜单类型基本信息详细信息")
    public TAjaxResult<RecipeMenuTypeBaseInfo> getInfo(@PathVariable("id") Long id) {
        return new TAjaxResult<RecipeMenuTypeBaseInfo>(recipeMenuTypeBaseInfoService.selectRecipeMenuTypeBaseInfoById(id));
    }


    @ApiOperation(value = "保存食谱菜单类型")
    @PostMapping("/save")
    public AjaxResult save(@RequestBody RecipeMenuTypeBaseInfo recipeMenuTypeBaseInfo) {
        if (null == recipeMenuTypeBaseInfo.getId() || recipeMenuTypeBaseInfo.getId() == 0) {
            return add(recipeMenuTypeBaseInfo);
        } else {
            return edit(recipeMenuTypeBaseInfo);
        }
    }

    /**
     * 新增食谱菜单类型基本信息
     */
    //@RequiresPermissions("mealManagement:recipeMenuTypeBaseInfo:add")
    @Log(platform = "1", title = "食谱菜单类型基本信息", businessType = BusinessType.INSERT)
    @PostMapping
    @ApiIgnore
    public AjaxResult add(@RequestBody RecipeMenuTypeBaseInfo recipeMenuTypeBaseInfo) {
        return toAjax(recipeMenuTypeBaseInfoService.insertRecipeMenuTypeBaseInfo(recipeMenuTypeBaseInfo));
    }

    /**
     * 修改食谱菜单类型基本信息
     */
    //@RequiresPermissions("mealManagement:recipeMenuTypeBaseInfo:edit")
    @Log(platform = "1", title = "食谱菜单类型基本信息", businessType = BusinessType.UPDATE)
    @PutMapping
    @ApiIgnore
    public AjaxResult edit(@RequestBody RecipeMenuTypeBaseInfo recipeMenuTypeBaseInfo) {
        return toAjax(recipeMenuTypeBaseInfoService.updateRecipeMenuTypeBaseInfo(recipeMenuTypeBaseInfo));
    }

    /**
     * 删除食谱菜单类型基本信息
     */
    //@RequiresPermissions("mealManagement:recipeMenuTypeBaseInfo:remove")
    @Log(platform = "1", title = "食谱菜单类型基本信息", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    @ApiOperation(value = "删除食谱菜单类型")
    public AjaxResult remove(@PathVariable Long[] ids) {
        return toAjax(recipeMenuTypeBaseInfoService.deleteRecipeMenuTypeBaseInfoByIds(ids));
    }

    @GetMapping("/getRecipeMenuTypeList")
    @ApiOperation(value = "获取全量食谱菜单类型")
    public AjaxResult getRecipeMenuTypeList() {
        return AjaxResult.success().put("data", recipeMenuTypeBaseInfoService.getRecipeMenuTypeList());
    }
}
