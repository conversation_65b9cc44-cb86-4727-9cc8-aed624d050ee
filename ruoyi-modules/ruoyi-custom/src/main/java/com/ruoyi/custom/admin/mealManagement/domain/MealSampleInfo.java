package com.ruoyi.custom.admin.mealManagement.domain;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.ruoyi.common.core.annotation.Excel;
import com.ruoyi.common.core.web.domain.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * 餐食留样信息对象 t_meal_sample_info
 *
 * <AUTHOR>
 * @date 2024-11-22
 */
@Data
@ApiModel(value = "餐食留样信息")
public class MealSampleInfo extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @ApiModelProperty(value = "主键")
    private Long id;

    /**
     * 食品名称
     */
    @Excel(name = "食品名称")
    @ApiModelProperty(value = "食品名称")
    private String foodName;

    /**
     * 餐次，字典：custom_meal_sample_info_meal_type；1:早餐，2：午餐，3：晚餐，4：其他
     */
    @Excel(name = "餐次", readConverterExp = "1=早餐,2=午餐,3=晚餐,4=其他")
    @ApiModelProperty(value = "餐次，字典：custom_meal_sample_info_meal_type；1:早餐，2：午餐，3：晚餐，4：其他")
    private String mealType;

    /**
     * 留样时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm")
    @Excel(name = "留样时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm")
    @ApiModelProperty(value = "留样时间")
    private Date sampleTime;

    /**
     * 标签信息
     */
    @Excel(name = "标签信息")
    @ApiModelProperty(value = "标签信息")
    private String labelInfo;

    /**
     * 留存位置
     */
    @Excel(name = "留存位置")
    @ApiModelProperty(value = "留存位置")
    private String storageLocation;

    /**
     * 图片地址
     */
    @Excel(name = "图片地址")
    @ApiModelProperty(value = "图片地址，多个逗号隔开")
    private String imgUrls;
}

