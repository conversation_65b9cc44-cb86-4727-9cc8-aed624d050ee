package com.ruoyi.custom.admin.securityguard.controller;

import com.ruoyi.common.core.utils.poi.ExcelUtil;
import com.ruoyi.common.core.web.controller.BaseController;
import com.ruoyi.common.core.web.domain.AjaxResult;
import com.ruoyi.common.core.web.page.TableDataInfo;
import com.ruoyi.common.log.annotation.Log;
import com.ruoyi.common.log.enums.BusinessType;
import com.ruoyi.common.security.annotation.RequiresPermissions;
import com.ruoyi.custom.admin.securityguard.domain.SecurityGuardManufacturerInfo;
import com.ruoyi.custom.admin.securityguard.service.ISecurityGuardManufacturerInfoService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 厂商信息Controller
 *
 * <AUTHOR>
 * @date 2023-02-03
 */
@RestController
@RequestMapping("/manufacturer")
@Api(tags = {"厂商信息"})
public class SecurityGuardManufacturerInfoController extends BaseController {
    @Autowired
    private ISecurityGuardManufacturerInfoService securityGuardManufacturerInfoService;

    /**
     * 查询厂商信息列表
     */
    // @RequiresPermissions("securityguard:manufacturer:list")
    @GetMapping("/list")
    @ApiOperation("查询厂商信息列表")
    public TableDataInfo list(SecurityGuardManufacturerInfo securityGuardManufacturerInfo) {
        startPage();
        List<SecurityGuardManufacturerInfo> list = securityGuardManufacturerInfoService.selectSecurityGuardManufacturerInfoList(securityGuardManufacturerInfo);
        return getDataTable(list);
    }

    /**
     * 导出厂商信息列表
     */
    // @RequiresPermissions("securityguard:manufacturer:export")
    @Log(title = "厂商信息", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    @ApiOperation("厂商信息")
    public void export(HttpServletResponse response, SecurityGuardManufacturerInfo securityGuardManufacturerInfo) {
        List<SecurityGuardManufacturerInfo> list = securityGuardManufacturerInfoService.selectSecurityGuardManufacturerInfoList(securityGuardManufacturerInfo);
        ExcelUtil<SecurityGuardManufacturerInfo> util = new ExcelUtil<SecurityGuardManufacturerInfo>(SecurityGuardManufacturerInfo.class);
        util.exportExcel(response, list, "厂商信息数据");
    }

    /**
     * 获取厂商信息详细信息
     */
    // @RequiresPermissions("securityguard:manufacturer:query")
    @GetMapping(value = "/{id}")
    @ApiOperation("厂商信息")
    public AjaxResult getInfo(@PathVariable("id") Long id) {
        return AjaxResult.success(securityGuardManufacturerInfoService.selectSecurityGuardManufacturerInfoById(id));
    }

    /**
     * 新增厂商信息
     */
    // @RequiresPermissions("securityguard:manufacturer:add")
    @Log(title = "厂商信息", businessType = BusinessType.INSERT)
    @PostMapping
    @ApiOperation("新增厂商信息")
    public AjaxResult add(@RequestBody SecurityGuardManufacturerInfo securityGuardManufacturerInfo) {
        return toAjax(securityGuardManufacturerInfoService.insertSecurityGuardManufacturerInfo(securityGuardManufacturerInfo));
    }

    /**
     * 修改厂商信息
     */
    // @RequiresPermissions("securityguard:manufacturer:edit")
    @Log(title = "修改厂商信息", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody SecurityGuardManufacturerInfo securityGuardManufacturerInfo) {
        return toAjax(securityGuardManufacturerInfoService.updateSecurityGuardManufacturerInfo(securityGuardManufacturerInfo));
    }

    /**
     * 删除厂商信息
     */
    // @RequiresPermissions("securityguard:manufacturer:remove")
    @Log(title = "厂商信息", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    @ApiOperation("删除厂商信息")
    public AjaxResult remove(@PathVariable Long[] ids) {
        return toAjax(securityGuardManufacturerInfoService.deleteSecurityGuardManufacturerInfoByIds(ids));
    }
}
