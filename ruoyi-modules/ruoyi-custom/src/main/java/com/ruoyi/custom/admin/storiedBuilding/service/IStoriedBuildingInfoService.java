package com.ruoyi.custom.admin.storiedBuilding.service;

import java.util.List;

import cn.hutool.json.JSONObject;
import com.ruoyi.custom.admin.storiedBuilding.domain.StoriedBuildingInfo;
import com.ruoyi.custom.admin.storiedBuilding.domain.TreeSelect;

/**
 * 楼栋信息Service接口
 *
 * <AUTHOR>
 * @date 2022-03-29
 */
public interface IStoriedBuildingInfoService {
    /**
     * 查询楼栋信息
     *
     * @param id 楼栋信息主键
     * @return 楼栋信息
     */
    public StoriedBuildingInfo selectStoriedBuildingInfoById(Long id);


    public List<StoriedBuildingInfo> getInfoByPid(Long pid);

    /**
     * 查询楼栋信息列表
     *
     * @param storiedBuildingInfo 楼栋信息
     * @return 楼栋信息集合
     */
    public List<StoriedBuildingInfo> selectStoriedBuildingInfoList(StoriedBuildingInfo storiedBuildingInfo);

    /**
     * 新增楼栋信息
     *
     * @param storiedBuildingInfo 楼栋信息
     * @return 结果
     */
    public int insertStoriedBuildingInfo(StoriedBuildingInfo storiedBuildingInfo);

    /**
     * 修改楼栋信息
     *
     * @param storiedBuildingInfo 楼栋信息
     * @return 结果
     */
    public int updateStoriedBuildingInfo(StoriedBuildingInfo storiedBuildingInfo);


    /**
     * 根据id修改房间的床位数
     *
     * @param id
     * @param totalBedNumber
     * @return
     */
    public int updateStoriedBedNum(Long id, Long totalBedNumber, Long occupancyNumber);

    /**
     * 批量删除楼栋信息
     *
     * @param ids 需要删除的楼栋信息主键集合
     * @return 结果
     */
    public int deleteStoriedBuildingInfoByIds(Long[] ids);

    /**
     * 删除楼栋信息信息
     *
     * @param id 楼栋信息主键
     * @return 结果
     */
    public int deleteStoriedBuildingInfoById(Long id);

    /**
     * 构建前端所需要下拉树结构
     *
     * @param
     * @return 下拉树结构列表
     */
    public List<TreeSelect> buildBuildingTreeSelect(List<StoriedBuildingInfo> storiedBuildingInfo);


    /**
     * 构建前端所需要下拉树结构
     *
     * @param
     * @return 下拉树结构列表
     */
    public List<TreeSelect> buildBuildingTreeSelectList(List<StoriedBuildingInfo> storiedBuildingInfo);

    /**
     * 构建前端所需要树结构
     *
     * @param
     * @return 树结构列表
     */
    public List<StoriedBuildingInfo> buildTree(List<StoriedBuildingInfo> storiedBuildingInfo);

    public String importData(List<JSONObject> storiedBuildingInfo, Boolean isUpdateSupport);


    /**
     * 根据id获取是否有下级
     *
     * @param id
     * @return
     */
    public Boolean hasChildById(Long id);


    public List<JSONObject> getTreeData(String type, String id);

}
