package com.ruoyi.custom.admin.storiedBuilding.mapper;

import java.util.List;

import com.ruoyi.custom.admin.storiedBuilding.domain.RoomTypeIndexInfo;

/**
 * 房间和类型的关联Mapper接口
 *
 * <AUTHOR>
 * @date 2022-03-31
 */
public interface RoomTypeIndexInfoMapper {
    /**
     * 查询房间和类型的关联
     *
     * @param id 房间和类型的关联主键
     * @return 房间和类型的关联
     */
    public RoomTypeIndexInfo selectRoomTypeIndexInfoById(Long id);

    /**
     * 根据房间id获取关联数据
     *
     * @param RoomId
     * @return
     */
    public RoomTypeIndexInfo selectRoomTypeIndexInfoByRoomId(Long RoomId);

    /**
     * 查询房间和类型的关联列表
     *
     * @param roomTypeIndexInfo 房间和类型的关联
     * @return 房间和类型的关联集合
     */
    public List<RoomTypeIndexInfo> selectRoomTypeIndexInfoList(RoomTypeIndexInfo roomTypeIndexInfo);

    /**
     * 新增房间和类型的关联
     *
     * @param roomTypeIndexInfo 房间和类型的关联
     * @return 结果
     */
    public int insertRoomTypeIndexInfo(RoomTypeIndexInfo roomTypeIndexInfo);

    /**
     * 修改房间和类型的关联
     *
     * @param roomTypeIndexInfo 房间和类型的关联
     * @return 结果
     */
    public int updateRoomTypeIndexInfo(RoomTypeIndexInfo roomTypeIndexInfo);

    /**
     * 删除房间和类型的关联
     *
     * @param id 房间和类型的关联主键
     * @return 结果
     */
    public int deleteRoomTypeIndexInfoById(Long id);

    /**
     * 批量删除房间和类型的关联
     *
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteRoomTypeIndexInfoByIds(Long[] ids);

    /**
     * 根据房间id批量删除房间和类型的关联
     *
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteByRoomId(Long[] ids);

    /**
     * 当前房间类型是否有在用
     *
     * @param typeId
     * @return
     */
    public int hasRoomTypeId(Long typeId);
}
