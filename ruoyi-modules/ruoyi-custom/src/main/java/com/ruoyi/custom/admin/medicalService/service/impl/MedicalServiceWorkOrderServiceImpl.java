package com.ruoyi.custom.admin.medicalService.service.impl;


import com.ruoyi.custom.admin.medicalService.domain.MedicalServiceWorkOrder;
import com.ruoyi.custom.admin.medicalService.mapper.MedicalServiceWorkOrderMapper;
import com.ruoyi.custom.admin.medicalService.service.IMedicalServiceWorkOrderService;
import com.ruoyi.custom.utils.OrderUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 医护服务工单Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-01-14
 */
@Service
public class MedicalServiceWorkOrderServiceImpl implements IMedicalServiceWorkOrderService {
    @Autowired
    private MedicalServiceWorkOrderMapper medicalServiceWorkOrderMapper;

    /**
     * 查询医护服务工单
     *
     * @param id 医护服务工单主键
     * @return 医护服务工单
     */
    @Override
    public MedicalServiceWorkOrder selectMedicalServiceWorkOrderById(Long id) {
        return medicalServiceWorkOrderMapper.selectMedicalServiceWorkOrderById(id);
    }

    /**
     * 查询医护服务工单列表
     *
     * @param medicalServiceWorkOrder 医护服务工单
     * @return 医护服务工单
     */
    @Override
    public List<MedicalServiceWorkOrder> selectMedicalServiceWorkOrderList(MedicalServiceWorkOrder medicalServiceWorkOrder) {
        return medicalServiceWorkOrderMapper.selectMedicalServiceWorkOrderList(medicalServiceWorkOrder);
    }

    /**
     * 新增医护服务工单
     *
     * @param medicalServiceWorkOrder 医护服务工单
     * @return 结果
     */
    @Override
    public int insertMedicalServiceWorkOrder(MedicalServiceWorkOrder medicalServiceWorkOrder) {
        medicalServiceWorkOrder.setWorkOrderNumber(OrderUtils.getDoctorServiceWorkOrderCode());
        // medicalServiceWorkOrder.setServiceProgress("0"); // 进行中
        return medicalServiceWorkOrderMapper.insertMedicalServiceWorkOrder(medicalServiceWorkOrder);
    }

    /**
     * 修改医护服务工单
     *
     * @param medicalServiceWorkOrder 医护服务工单
     * @return 结果
     */
    @Override
    public int updateMedicalServiceWorkOrder(MedicalServiceWorkOrder medicalServiceWorkOrder) {
        return medicalServiceWorkOrderMapper.updateMedicalServiceWorkOrder(medicalServiceWorkOrder);
    }

    /**
     * 批量删除医护服务工单
     *
     * @param ids 需要删除的医护服务工单主键
     * @return 结果
     */
    @Override
    public int deleteMedicalServiceWorkOrderByIds(Long[] ids) {
        return medicalServiceWorkOrderMapper.deleteMedicalServiceWorkOrderByIds(ids);
    }

    /**
     * 删除医护服务工单信息
     *
     * @param id 医护服务工单主键
     * @return 结果
     */
    @Override
    public int deleteMedicalServiceWorkOrderById(Long id) {
        return medicalServiceWorkOrderMapper.deleteMedicalServiceWorkOrderById(id);
    }
}

