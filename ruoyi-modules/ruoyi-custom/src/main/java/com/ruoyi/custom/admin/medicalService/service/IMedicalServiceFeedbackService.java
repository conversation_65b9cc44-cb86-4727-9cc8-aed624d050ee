package com.ruoyi.custom.admin.medicalService.service;

import com.ruoyi.custom.admin.medicalService.domain.MedicalServiceFeedback;

import java.util.List;

/**
 * 医护服务反馈Service接口
 *
 * <AUTHOR>
 * @date 2025-01-15
 */
public interface IMedicalServiceFeedbackService {
    /**
     * 查询医护服务反馈
     *
     * @param id 医护服务反馈主键
     * @return 医护服务反馈
     */
    public MedicalServiceFeedback selectMedicalServiceFeedbackById(Long id);

    /**
     * 查询医护服务反馈列表
     *
     * @param medicalServiceFeedback 医护服务反馈
     * @return 医护服务反馈集合
     */
    public List<MedicalServiceFeedback> selectMedicalServiceFeedbackList(MedicalServiceFeedback medicalServiceFeedback);

    /**
     * 新增医护服务反馈
     *
     * @param medicalServiceFeedback 医护服务反馈
     * @return 结果
     */
    public int insertMedicalServiceFeedback(MedicalServiceFeedback medicalServiceFeedback);

    /**
     * 修改医护服务反馈
     *
     * @param medicalServiceFeedback 医护服务反馈
     * @return 结果
     */
    public int updateMedicalServiceFeedback(MedicalServiceFeedback medicalServiceFeedback);

    /**
     * 批量删除医护服务反馈
     *
     * @param ids 需要删除的医护服务反馈主键集合
     * @return 结果
     */
    public int deleteMedicalServiceFeedbackByIds(Long[] ids);

    /**
     * 删除医护服务反馈信息
     *
     * @param id 医护服务反馈主键
     * @return 结果
     */
    public int deleteMedicalServiceFeedbackById(Long id);
}

