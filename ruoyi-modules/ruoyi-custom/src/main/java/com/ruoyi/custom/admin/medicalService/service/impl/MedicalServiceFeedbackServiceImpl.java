package com.ruoyi.custom.admin.medicalService.service.impl;


import java.util.List;

import com.ruoyi.custom.admin.medicalService.domain.MedicalServiceFeedback;
import com.ruoyi.custom.admin.medicalService.mapper.MedicalServiceFeedbackMapper;
import com.ruoyi.custom.admin.medicalService.service.IMedicalServiceFeedbackService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * 医护服务反馈Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-01-15
 */
@Service
public class MedicalServiceFeedbackServiceImpl implements IMedicalServiceFeedbackService {
    @Autowired
    private MedicalServiceFeedbackMapper medicalServiceFeedbackMapper;

    /**
     * 查询医护服务反馈
     *
     * @param id 医护服务反馈主键
     * @return 医护服务反馈
     */
    @Override
    public MedicalServiceFeedback selectMedicalServiceFeedbackById(Long id) {
        return medicalServiceFeedbackMapper.selectMedicalServiceFeedbackById(id);
    }

    /**
     * 查询医护服务反馈列表
     *
     * @param medicalServiceFeedback 医护服务反馈
     * @return 医护服务反馈
     */
    @Override
    public List<MedicalServiceFeedback> selectMedicalServiceFeedbackList(MedicalServiceFeedback medicalServiceFeedback) {
        return medicalServiceFeedbackMapper.selectMedicalServiceFeedbackList(medicalServiceFeedback);
    }

    /**
     * 新增医护服务反馈
     *
     * @param medicalServiceFeedback 医护服务反馈
     * @return 结果
     */
    @Override
    public int insertMedicalServiceFeedback(MedicalServiceFeedback medicalServiceFeedback) {
        return medicalServiceFeedbackMapper.insertMedicalServiceFeedback(medicalServiceFeedback);
    }

    /**
     * 修改医护服务反馈
     *
     * @param medicalServiceFeedback 医护服务反馈
     * @return 结果
     */
    @Override
    public int updateMedicalServiceFeedback(MedicalServiceFeedback medicalServiceFeedback) {
        return medicalServiceFeedbackMapper.updateMedicalServiceFeedback(medicalServiceFeedback);
    }

    /**
     * 批量删除医护服务反馈
     *
     * @param ids 需要删除的医护服务反馈主键
     * @return 结果
     */
    @Override
    public int deleteMedicalServiceFeedbackByIds(Long[] ids) {
        return medicalServiceFeedbackMapper.deleteMedicalServiceFeedbackByIds(ids);
    }

    /**
     * 删除医护服务反馈信息
     *
     * @param id 医护服务反馈主键
     * @return 结果
     */
    @Override
    public int deleteMedicalServiceFeedbackById(Long id) {
        return medicalServiceFeedbackMapper.deleteMedicalServiceFeedbackById(id);
    }
}

