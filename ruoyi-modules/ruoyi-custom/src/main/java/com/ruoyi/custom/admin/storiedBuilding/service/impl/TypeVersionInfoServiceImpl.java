package com.ruoyi.custom.admin.storiedBuilding.service.impl;

import com.ruoyi.common.core.utils.DateUtils;
import com.ruoyi.common.security.utils.SecurityUtils;
import com.ruoyi.custom.admin.storiedBuilding.domain.TypeVersionInfo;
import com.ruoyi.custom.admin.storiedBuilding.mapper.TypeVersionInfoMapper;
import com.ruoyi.custom.admin.storiedBuilding.service.ITypeVersionInfoService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 房间类型版本Service业务层处理
 *
 * <AUTHOR>
 * @date 2022-04-01
 */
@Service
public class TypeVersionInfoServiceImpl implements ITypeVersionInfoService {
    @Autowired
    private TypeVersionInfoMapper typeVersionInfoMapper;

    /**
     * 查询房间类型版本
     *
     * @param id 房间类型版本主键
     * @return 房间类型版本
     */
    @Override
    public TypeVersionInfo selectTypeVersionInfoById(Long id) {
        return typeVersionInfoMapper.selectTypeVersionInfoById(id);
    }

    /**
     * 查询房间类型版本列表
     *
     * @param typeVersionInfo 房间类型版本
     * @return 房间类型版本
     */
    @Override
    public List<TypeVersionInfo> selectTypeVersionInfoList(TypeVersionInfo typeVersionInfo) {
        return typeVersionInfoMapper.selectTypeVersionInfoList(typeVersionInfo);
    }

    /**
     * 新增房间类型版本
     *
     * @param typeVersionInfo 房间类型版本
     * @return 结果
     */
    @Override
    public int insertTypeVersionInfo(TypeVersionInfo typeVersionInfo) {
        // 获取创建时间和创建人id
        typeVersionInfo.setCreateTime(DateUtils.getNowDate());
        typeVersionInfo.setCreateBy(String.valueOf(SecurityUtils.getUserId()));
        // 查看有多少个版本
        TypeVersionInfo type = new TypeVersionInfo();
        type.setTypeId(typeVersionInfo.getTypeId());
        List<TypeVersionInfo> typeVersionInfos = typeVersionInfoMapper.selectTypeVersionInfoList(type);
        // 查看是否有正在使用的版本，如果有就改为停用状态
        TypeVersionInfo versionInfo = typeVersionInfoMapper.getVersionInfo(typeVersionInfo.getTypeId(), "0");
        if (null != versionInfo) {
            versionInfo.setStatus("1");
            updateTypeVersionInfo(versionInfo);
        }
        // 写版本号
        if (typeVersionInfos.size() > 0) {
            int i = typeVersionInfos.size() + 1;
            typeVersionInfo.setVersion("V." + i);
        } else {
            typeVersionInfo.setVersion("V.1");
        }
        return typeVersionInfoMapper.insertTypeVersionInfo(typeVersionInfo);
    }

    /**
     * 修改房间类型版本
     *
     * @param typeVersionInfo 房间类型版本
     * @return 结果
     */
    @Override
    public int updateTypeVersionInfo(TypeVersionInfo typeVersionInfo) {
        typeVersionInfo.setUpdateTime(DateUtils.getNowDate());
        return typeVersionInfoMapper.updateTypeVersionInfo(typeVersionInfo);
    }

    /**
     * 批量删除房间类型版本
     *
     * @param ids 需要删除的房间类型版本主键
     * @return 结果
     */
    @Override
    public int deleteTypeVersionInfoByIds(Long[] ids) {
        return typeVersionInfoMapper.deleteTypeVersionInfoByIds(ids);
    }

    /**
     * 删除房间类型版本信息
     *
     * @param id 房间类型版本主键
     * @return 结果
     */
    @Override
    public int deleteTypeVersionInfoById(Long id) {
        return typeVersionInfoMapper.deleteTypeVersionInfoById(id);
    }
}
