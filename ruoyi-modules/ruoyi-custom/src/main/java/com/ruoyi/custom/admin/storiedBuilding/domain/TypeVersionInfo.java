package com.ruoyi.custom.admin.storiedBuilding.domain;

import com.ruoyi.common.core.annotation.Excel;
import com.ruoyi.common.core.web.domain.BaseEntity;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.math.BigDecimal;

/**
 * 房间类型版本对象 t_type_verion_info
 *
 * <AUTHOR>
 * @date 2022-04-01
 */
public class TypeVersionInfo extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /**
     * $column.columnComment
     */
    private Long id;

    /**
     * 房间类型id
     */
    @Excel(name = "房间类型id")
    private String typeId;

    /**
     * 状态
     */
    @Excel(name = "状态")
    private String status;

    /**
     * 月费用
     */
    @Excel(name = "月费用")
    private BigDecimal fees;

    /**
     * 日费用
     */
    @Excel(name = "日费用")
    private BigDecimal dailyFee;

    /**
     * 版本号
     */
    @Excel(name = "版本号")
    private String version;

    /**
     * 床位数
     */
    @Excel(name = "床位数")
    private Long bedNum;

    /**
     * 逻辑删除标记（0：显示；1：隐藏
     */
    private String delFlag;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getTypeId() {
        return typeId;
    }

    public void setTypeId(String typeId) {
        this.typeId = typeId;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public BigDecimal getFees() {
        return fees;
    }

    public void setFees(BigDecimal fees) {
        this.fees = fees;
    }

    public String getVersion() {
        return version;
    }

    public void setVersion(String version) {
        this.version = version;
    }

    public Long getBedNum() {
        return bedNum;
    }

    public void setBedNum(Long bedNum) {
        this.bedNum = bedNum;
    }

    public String getDelFlag() {
        return delFlag;
    }

    public void setDelFlag(String delFlag) {
        this.delFlag = delFlag;
    }

    public BigDecimal getDailyFee() {
        return dailyFee;
    }

    public void setDailyFee(BigDecimal dailyFee) {
        this.dailyFee = dailyFee;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this, ToStringStyle.MULTI_LINE_STYLE)
                .append("id", getId())
                .append("typeId", getTypeId())
                .append("status", getStatus())
                .append("fees", getFees())
                .append("version", getVersion())
                .append("bedNum", getBedNum())
                .append("createTime", getCreateTime())
                .append("createBy", getCreateBy())
                .append("updateTime", getUpdateTime())
                .append("updateBy", getUpdateBy())
                .append("delFlag", getDelFlag())
                .append("remark", getRemark())
                .toString();
    }
}
