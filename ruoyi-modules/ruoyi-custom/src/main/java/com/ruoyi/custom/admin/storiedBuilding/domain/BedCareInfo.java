package com.ruoyi.custom.admin.storiedBuilding.domain;

import java.util.Date;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.core.annotation.Excel;
import com.ruoyi.common.core.web.domain.BaseEntity;

/**
 * 床位和护工关联信息对象 t_bed_care_info
 *
 * <AUTHOR>
 * @date 2022-03-31
 */
@ApiModel(value = "床位和护工关联信息")
public class BedCareInfo extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /**
     * $column.columnComment
     */
    private Long id;

    /**
     * 床位id
     */
    @Excel(name = "床位id")
    @ApiModelProperty(value = "床位id")
    private Long bedId;

    /**
     * 护工id
     */
    @Excel(name = "护工id")
    @ApiModelProperty(value = "护工id")
    private Long careWorkerId;

    /**
     * 开始日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "开始日期", width = 30, dateFormat = "yyyy-MM-dd")
    @ApiModelProperty(value = "开始日期")
    private Date beginDate;

    /**
     * 结束日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "结束日期", width = 30, dateFormat = "yyyy-MM-dd")
    @ApiModelProperty(value = "结束日期")
    private Date endDate;

    /**
     * 护理状态（服务中，已结束）
     */
    @Excel(name = "护理状态", readConverterExp = "0:服务中，1:已结束")
    @ApiModelProperty(value = "护理状态")
    private String careState;

    /**
     * 床位名称
     */
    @Excel(name = "床位名称", readConverterExp = "床位名称")
    @ApiModelProperty(value = "床位名称")
    private String bedName;

    /**
     * 房间名称
     */
    @Excel(name = "房间名称", readConverterExp = "房间名称")
    @ApiModelProperty(value = "房间名称")
    private String roomName;

    /**
     * 楼层名称
     */
    @Excel(name = "楼层名称", readConverterExp = "楼层名称")
    @ApiModelProperty(value = "楼层名称")
    private String floorName;

    /**
     * 楼栋名称
     */
    @Excel(name = "楼栋名称", readConverterExp = "楼栋名称")
    @ApiModelProperty(value = "楼栋名称")
    private String buildingName;

    /**
     * 逻辑删除标记（0：显示；1：隐藏
     */
    private String delFlag;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getBedId() {
        return bedId;
    }

    public void setBedId(Long bedId) {
        this.bedId = bedId;
    }

    public Long getCareWorkerId() {
        return careWorkerId;
    }

    public void setCareWorkerId(Long careWorkerId) {
        this.careWorkerId = careWorkerId;
    }

    public Date getBeginDate() {
        return beginDate;
    }

    public void setBeginDate(Date beginDate) {
        this.beginDate = beginDate;
    }

    public Date getEndDate() {
        return endDate;
    }

    public void setEndDate(Date endDate) {
        this.endDate = endDate;
    }

    public String getCareState() {
        return careState;
    }

    public void setCareState(String careState) {
        this.careState = careState;
    }

    public String getBedName() {
        return bedName;
    }

    public void setBedName(String bedName) {
        this.bedName = bedName;
    }

    public String getRoomName() {
        return roomName;
    }

    public void setRoomName(String roomName) {
        this.roomName = roomName;
    }

    public String getFloorName() {
        return floorName;
    }

    public void setFloorName(String floorName) {
        this.floorName = floorName;
    }

    public String getBuildingName() {
        return buildingName;
    }

    public void setBuildingName(String buildingName) {
        this.buildingName = buildingName;
    }

    public String getDelFlag() {
        return delFlag;
    }

    public void setDelFlag(String delFlag) {
        if ("".equals(delFlag) || delFlag == null) {
            this.delFlag = "0";// 去除该属性的前后空格并进行非空非null判断
        } else {
            this.delFlag = delFlag;
        }
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this, ToStringStyle.MULTI_LINE_STYLE)
                .append("id", getId())
                .append("bedId", getBedId())
                .append("careWorkerId", getCareWorkerId())
                .append("beginDate", getBeginDate())
                .append("endDate", getEndDate())
                .append("careState", getCareState())
                .append("createTime", getCreateTime())
                .append("createBy", getCreateBy())
                .append("updateTime", getUpdateTime())
                .append("updateBy", getUpdateBy())
                .append("delFlag", getDelFlag())
                .append("remark", getRemark())
                .toString();
    }
}
