package com.ruoyi.custom.admin.storiedBuilding.domain;

import com.ruoyi.common.core.web.domain.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.core.annotation.Excel;

import java.util.ArrayList;
import java.util.List;

/**
 * 楼栋信息对象 t_storied_building_info
 *
 * <AUTHOR>
 * @date 2022-03-29
 */
@ApiModel(value = "楼栋信息")
public class StoriedBuildingInfo extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /**
     * $column.columnComment
     */
    private Long id;

    /**
     * 名称
     */
    @Excel(name = "名称")
    @ApiModelProperty(value = "名称")
    private String name;

    /**
     * 类型
     */
    @Excel(name = "类型")
    @ApiModelProperty(value = "类型")
    private String type;

    /**
     * 父ID
     */
    @ApiModelProperty(value = "父ID")
    private Long parentId;

    /**
     * 祖级列表
     */
    private String ancestors;

    /**
     * 排序
     */
    @Excel(name = "排序")
    @ApiModelProperty(value = "排序")
    private Long sort;

    /**
     * 楼层总数
     */
    @Excel(name = "楼层总数")
    @ApiModelProperty(value = "楼层总数")
    private Long totalFloorsNumber;

    /**
     * 房间总数
     */
    @Excel(name = "房间总数")
    @ApiModelProperty(value = "房间总数")
    private Long totalRoomsNumber;

    /**
     * 床位总数
     */
    @Excel(name = "床位总数")
    @ApiModelProperty(value = "床位总数")
    private Long totalBedNumber;

    /**
     * 入住人数
     */
    @Excel(name = "入住人数")
    @ApiModelProperty(value = "入住人数")
    private Long occupancyNumber;

    /**
     * 房间类型
     */
    @Excel(name = "房间类型")
    @ApiModelProperty(value = "房间类型")
    private String roomType;
    @ApiModelProperty(value = "房间类型名称")
    private String roomTypeName;

    /**
     * 逻辑删除标记（0：显示；1：隐藏
     */
    private String delFlag;

    @Excel(name = "编号")
    @ApiModelProperty(value = "编号")
    private String serialNumber;

    /**
     * 子部门
     */
    private List<StoriedBuildingInfo> children = new ArrayList<StoriedBuildingInfo>();

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public Long getSort() {
        return sort;
    }

    public void setSort(Long sort) {
        this.sort = sort;
    }

    public Long getTotalFloorsNumber() {
        return totalFloorsNumber;
    }

    public void setTotalFloorsNumber(Long totalFloorsNumber) {
        this.totalFloorsNumber = totalFloorsNumber;
    }

    public Long getTotalRoomsNumber() {
        return totalRoomsNumber;
    }

    public void setTotalRoomsNumber(Long totalRoomsNumber) {
        this.totalRoomsNumber = totalRoomsNumber;
    }

    public Long getTotalBedNumber() {
        return totalBedNumber;
    }

    public void setTotalBedNumber(Long totalBedNumber) {
        this.totalBedNumber = totalBedNumber;
    }

    public Long getOccupancyNumber() {
        return occupancyNumber;
    }

    public void setOccupancyNumber(Long occupancyNumber) {
        this.occupancyNumber = occupancyNumber;
    }

    public String getRoomType() {
        return roomType;
    }

    public void setRoomType(String roomType) {
        this.roomType = roomType;
    }

    public String getDelFlag() {
        return delFlag;
    }

    public void setDelFlag(String delFlag) {
        if ("".equals(delFlag) || delFlag == null) {
            this.delFlag = "0";// 去除该属性的前后空格并进行非空非null判断
        } else {
            this.delFlag = delFlag;
        }
    }

    public Long getParentId() {
        return parentId;
    }

    public void setParentId(Long parentId) {
        this.parentId = parentId;
    }

    public String getAncestors() {
        return ancestors;
    }

    public void setAncestors(String ancestors) {
        this.ancestors = ancestors;
    }

    public List<StoriedBuildingInfo> getChildren() {
        return children;
    }

    public void setChildren(List<StoriedBuildingInfo> children) {
        this.children = children;
    }

    public String getRoomTypeName() {
        return roomTypeName;
    }

    public void setRoomTypeName(String roomTypeName) {
        this.roomTypeName = roomTypeName;
    }

    public String getSerialNumber() {
        return serialNumber;
    }

    public void setSerialNumber(String serialNumber) {
        this.serialNumber = serialNumber;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this, ToStringStyle.MULTI_LINE_STYLE)
                .append("id", getId())
                .append("name", getName())
                .append("type", getType())
                .append("sort", getSort())
                .append("totalFloorsNumber", getTotalFloorsNumber())
                .append("totalRoomsNumber", getTotalRoomsNumber())
                .append("totalBedNumber", getTotalBedNumber())
                .append("occupancyNumber", getOccupancyNumber())
                .append("roomType", getRoomType())
                .append("parentId", getParentId())
                .append("ancestors", getAncestors())
                .append("createTime", getCreateTime())
                .append("createBy", getCreateBy())
                .append("updateTime", getUpdateTime())
                .append("updateBy", getUpdateBy())
                .append("delFlag", getDelFlag())
                .append("remark", getRemark())
                .toString();
    }
}
