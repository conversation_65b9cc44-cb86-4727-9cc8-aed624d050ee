package com.ruoyi.custom.admin.mealManagement.service.impl;

import java.util.List;

import cn.hutool.json.JSONObject;
import com.ruoyi.common.core.utils.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.custom.admin.mealManagement.mapper.RecipeMenuTypeBaseInfoMapper;
import com.ruoyi.custom.admin.mealManagement.domain.RecipeMenuTypeBaseInfo;
import com.ruoyi.custom.admin.mealManagement.service.IRecipeMenuTypeBaseInfoService;

/**
 * 食谱菜单类型基本信息Service业务层处理
 *
 * <AUTHOR>
 * @date 2022-04-21
 */
@Service
public class RecipeMenuTypeBaseInfoServiceImpl implements IRecipeMenuTypeBaseInfoService {
    @Autowired
    private RecipeMenuTypeBaseInfoMapper recipeMenuTypeBaseInfoMapper;

    /**
     * 查询食谱菜单类型基本信息
     *
     * @param id 食谱菜单类型基本信息主键
     * @return 食谱菜单类型基本信息
     */
    @Override
    public RecipeMenuTypeBaseInfo selectRecipeMenuTypeBaseInfoById(Long id) {
        return recipeMenuTypeBaseInfoMapper.selectRecipeMenuTypeBaseInfoById(id);
    }

    /**
     * 查询食谱菜单类型基本信息列表
     *
     * @param recipeMenuTypeBaseInfo 食谱菜单类型基本信息
     * @return 食谱菜单类型基本信息
     */
    @Override
    public List<RecipeMenuTypeBaseInfo> selectRecipeMenuTypeBaseInfoList(RecipeMenuTypeBaseInfo recipeMenuTypeBaseInfo) {
        return recipeMenuTypeBaseInfoMapper.selectRecipeMenuTypeBaseInfoList(recipeMenuTypeBaseInfo);
    }

    /**
     * 新增食谱菜单类型基本信息
     *
     * @param recipeMenuTypeBaseInfo 食谱菜单类型基本信息
     * @return 结果
     */
    @Override
    public int insertRecipeMenuTypeBaseInfo(RecipeMenuTypeBaseInfo recipeMenuTypeBaseInfo) {
        recipeMenuTypeBaseInfo.setCreateTime(DateUtils.getNowDate());
        return recipeMenuTypeBaseInfoMapper.insertRecipeMenuTypeBaseInfo(recipeMenuTypeBaseInfo);
    }

    /**
     * 修改食谱菜单类型基本信息
     *
     * @param recipeMenuTypeBaseInfo 食谱菜单类型基本信息
     * @return 结果
     */
    @Override
    public int updateRecipeMenuTypeBaseInfo(RecipeMenuTypeBaseInfo recipeMenuTypeBaseInfo) {
        recipeMenuTypeBaseInfo.setUpdateTime(DateUtils.getNowDate());
        return recipeMenuTypeBaseInfoMapper.updateRecipeMenuTypeBaseInfo(recipeMenuTypeBaseInfo);
    }

    /**
     * 批量删除食谱菜单类型基本信息
     *
     * @param ids 需要删除的食谱菜单类型基本信息主键
     * @return 结果
     */
    @Override
    public int deleteRecipeMenuTypeBaseInfoByIds(Long[] ids) {
        return recipeMenuTypeBaseInfoMapper.deleteRecipeMenuTypeBaseInfoByIds(ids);
    }

    /**
     * 删除食谱菜单类型基本信息信息
     *
     * @param id 食谱菜单类型基本信息主键
     * @return 结果
     */
    @Override
    public int deleteRecipeMenuTypeBaseInfoById(Long id) {
        return recipeMenuTypeBaseInfoMapper.deleteRecipeMenuTypeBaseInfoById(id);
    }

    @Override
    public List<JSONObject> getRecipeMenuTypeList() {
        return recipeMenuTypeBaseInfoMapper.getRecipeMenuTypeList();
    }
}
