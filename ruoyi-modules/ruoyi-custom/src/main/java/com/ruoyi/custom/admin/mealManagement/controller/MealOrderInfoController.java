package com.ruoyi.custom.admin.mealManagement.controller;

import com.ruoyi.common.core.utils.poi.ExcelUtil;
import com.ruoyi.common.core.web.controller.BaseController;
import com.ruoyi.common.core.web.domain.AjaxResult;
import com.ruoyi.common.core.web.page.TableDataInfo;
import com.ruoyi.common.log.annotation.Log;
import com.ruoyi.common.log.enums.BusinessType;
import com.ruoyi.custom.admin.mealManagement.domain.MealOrderInfo;
import com.ruoyi.custom.admin.mealManagement.service.MealOrderInfoService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 点餐信息Controller
 *
 * <AUTHOR>
 * @date 2024-11-28
 */
@RestController
@RequestMapping("/mealOrder")
@Api(value = "点餐信息Controller", tags = "点餐信息")
public class MealOrderInfoController extends BaseController {
    @Autowired
    private MealOrderInfoService mealOrderInfoService;

    /**
     * 查询点餐信息列表
     */
    // @RequiresPermissions("custom:info:list")
    @GetMapping("/list")
    @ApiOperation(value = "获取点餐信息列表")
    public TableDataInfo list(MealOrderInfo mealOrderInfo) {
        startPage();
        List<MealOrderInfo> list = mealOrderInfoService.selectMealOrderInfoList(mealOrderInfo);
        return getDataTable(list);
    }

    /**
     * 导出点餐信息列表
     */
    // @RequiresPermissions("custom:info:export")
    @Log(title = "点餐信息", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    @ApiOperation(value = "导出点餐信息列表")
    public void export(HttpServletResponse response, MealOrderInfo mealOrderInfo) {
        List<MealOrderInfo> list = mealOrderInfoService.selectMealOrderInfoList(mealOrderInfo);
        ExcelUtil<MealOrderInfo> util = new ExcelUtil<MealOrderInfo>(MealOrderInfo.class);
        util.exportExcel(response, list, "点餐信息数据");
    }

    /**
     * 获取点餐信息详细信息
     */
    // @RequiresPermissions("custom:info:query")
    @GetMapping(value = "/{id}")
    @ApiOperation(value = "获取点餐信息详细信息")
    public AjaxResult getInfo(@PathVariable("id") Long id) {
        return AjaxResult.success(mealOrderInfoService.selectMealOrderInfoById(id));
    }

    /**
     * 新增点餐信息
     */
    // @RequiresPermissions("custom:info:add")
    @Log(title = "点餐信息", businessType = BusinessType.INSERT)
    @PostMapping
    @ApiOperation(value = "新增点餐信息")
    public AjaxResult add(@RequestBody MealOrderInfo mealOrderInfo) {
        return toAjax(mealOrderInfoService.insertMealOrderInfo(mealOrderInfo));
    }

    /**
     * 修改点餐信息
     */
    // @RequiresPermissions("custom:info:edit")
    @Log(title = "点餐信息", businessType = BusinessType.UPDATE)
    @PutMapping
    @ApiOperation(value = "修改点餐信息")
    public AjaxResult edit(@RequestBody MealOrderInfo mealOrderInfo) {
        return toAjax(mealOrderInfoService.updateMealOrderInfo(mealOrderInfo));
    }

    /**
     * 删除点餐信息
     */
    // @RequiresPermissions("custom:info:remove")
    @Log(title = "点餐信息", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    @ApiOperation(value = "删除点餐信息")
    public AjaxResult remove(@PathVariable Long[] ids) {
        return toAjax(mealOrderInfoService.deleteMealOrderInfoByIds(ids));
    }
}

