package com.ruoyi.custom.admin.storiedBuilding.domain;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.core.annotation.Excel;
import com.ruoyi.common.core.web.domain.BaseEntity;

/**
 * 床位基础信息对象 t_bed_base_info
 *
 * <AUTHOR>
 * @date 2022-03-31
 */
@ApiModel(value = "床位基础信息")
public class BedBaseInfo extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /**
     * $column.columnComment
     */
    private Long id;

    /**
     * 床位名称
     */
    @Excel(name = "床位名称")
    @ApiModelProperty("床位名称")
    private String bedName;

    /**
     * 床位编号
     */
    @Excel(name = "床位编号")
    @ApiModelProperty("床位编号")
    private String bedNum;

    /**
     * 床位状态(入住，空闲，停用)
     */
    @Excel(name = "床位状态(入住，空闲，停用)")
    @ApiModelProperty("床位状态(0:入住，1:空闲，2:停用)")
    private String bedState;

    /**
     * 护工关联表id
     */
    @Excel(name = "护工关联表id")
    @ApiModelProperty("护工关联表id")
    private String careIndex;

    /**
     * 床位费用版本表id
     */
//    @Excel(name = "床位费用版本表id")
    private String feeIndex;

    /**
     * 入住记录表关联id
     */
    @Excel(name = "入住记录表关联id")
    @ApiModelProperty("入住记录表关联id")
    private String liveRecordsIndex;

    /**
     * 逻辑删除标记（0：显示；1：隐藏
     */
    private String delFlag;

    /**
     * 房间id
     */
    @Excel(name = "房间id")
    @ApiModelProperty("房间id")
    private Long roomId;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getBedName() {
        return bedName;
    }

    public void setBedName(String bedName) {
        this.bedName = bedName;
    }

    public String getBedNum() {
        return bedNum;
    }

    public void setBedNum(String bedNum) {
        this.bedNum = bedNum;
    }

    public String getBedState() {
        return bedState;
    }

    public void setBedState(String bedState) {
        this.bedState = bedState;
    }

    public String getCareIndex() {
        return careIndex;
    }

    public void setCareIndex(String careIndex) {
        this.careIndex = careIndex;
    }

    public String getFeeIndex() {
        return feeIndex;
    }

    public void setFeeIndex(String feeIndex) {
        this.feeIndex = feeIndex;
    }

    public String getLiveRecordsIndex() {
        return liveRecordsIndex;
    }

    public void setLiveRecordsIndex(String liveRecordsIndex) {
        this.liveRecordsIndex = liveRecordsIndex;
    }

    public String getDelFlag() {
        return delFlag;
    }

    public void setDelFlag(String delFlag) {
        if ("".equals(delFlag) || delFlag == null) {
            this.delFlag = "0";// 去除该属性的前后空格并进行非空非null判断
        } else {
            this.delFlag = delFlag;
        }
    }

    public Long getRoomId() {
        return roomId;
    }

    public void setRoomId(Long roomId) {
        this.roomId = roomId;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this, ToStringStyle.MULTI_LINE_STYLE)
                .append("id", getId())
                .append("bedName", getBedName())
                .append("bedNum", getBedNum())
                .append("bedState", getBedState())
                .append("careIndex", getCareIndex())
                .append("feeIndex", getFeeIndex())
                .append("liveRecordsIndex", getLiveRecordsIndex())
                .append("createTime", getCreateTime())
                .append("createBy", getCreateBy())
                .append("updateTime", getUpdateTime())
                .append("updateBy", getUpdateBy())
                .append("delFlag", getDelFlag())
                .append("remark", getRemark())
                .append("roomId", getRoomId())
                .toString();
    }
}
