package com.ruoyi.custom.admin.mealManagement.service.impl;

import com.ruoyi.custom.admin.mealManagement.domain.MealSampleInfo;
import com.ruoyi.custom.admin.mealManagement.mapper.MealSampleInfoMapper;
import com.ruoyi.custom.admin.mealManagement.service.MealSampleInfoService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 餐食留样信息Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-11-25
 */
@Service
public class MealSampleInfoServiceImpl implements MealSampleInfoService {
    @Autowired
    private MealSampleInfoMapper mealSampleInfoMapper;

    /**
     * 查询餐食留样信息
     *
     * @param id 餐食留样信息主键
     * @return 餐食留样信息
     */
    @Override
    public MealSampleInfo selectMealSampleInfoById(Long id) {
        return mealSampleInfoMapper.selectMealSampleInfoById(id);
    }

    /**
     * 查询餐食留样信息列表
     *
     * @param mealSampleInfo 餐食留样信息
     * @return 餐食留样信息
     */
    @Override
    public List<MealSampleInfo> selectMealSampleInfoList(MealSampleInfo mealSampleInfo) {
        return mealSampleInfoMapper.selectMealSampleInfoList(mealSampleInfo);
    }

    /**
     * 新增餐食留样信息
     *
     * @param mealSampleInfo 餐食留样信息
     * @return 结果
     */
    @Override
    public int insertMealSampleInfo(MealSampleInfo mealSampleInfo) {
        return mealSampleInfoMapper.insertMealSampleInfo(mealSampleInfo);
    }

    /**
     * 修改餐食留样信息
     *
     * @param mealSampleInfo 餐食留样信息
     * @return 结果
     */
    @Override
    public int updateMealSampleInfo(MealSampleInfo mealSampleInfo) {
        return mealSampleInfoMapper.updateMealSampleInfo(mealSampleInfo);
    }

    /**
     * 批量删除餐食留样信息
     *
     * @param ids 需要删除的餐食留样信息主键
     * @return 结果
     */
    @Override
    public int deleteMealSampleInfoByIds(Long[] ids) {
        return mealSampleInfoMapper.deleteMealSampleInfoByIds(ids);
    }

    /**
     * 删除餐食留样信息信息
     *
     * @param id 餐食留样信息主键
     * @return 结果
     */
    @Override
    public int deleteMealSampleInfoById(Long id) {
        return mealSampleInfoMapper.deleteMealSampleInfoById(id);
    }
}

