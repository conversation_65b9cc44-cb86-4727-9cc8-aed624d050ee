package com.ruoyi.custom.admin.mealManagement.service.impl;


import com.ruoyi.custom.admin.mealManagement.domain.MealOrderInfo;
import com.ruoyi.custom.admin.mealManagement.mapper.MealOrderInfoMapper;
import com.ruoyi.custom.admin.mealManagement.service.MealOrderInfoService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 点餐信息Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-11-28
 */
@Service
public class MealOrderInfoServiceImpl implements MealOrderInfoService {
    @Autowired
    private MealOrderInfoMapper mealOrderInfoMapper;

    /**
     * 查询点餐信息
     *
     * @param id 点餐信息主键
     * @return 点餐信息
     */
    @Override
    public MealOrderInfo selectMealOrderInfoById(Long id) {
        return mealOrderInfoMapper.selectMealOrderInfoById(id);
    }

    /**
     * 查询点餐信息列表
     *
     * @param mealOrderInfo 点餐信息
     * @return 点餐信息
     */
    @Override
    public List<MealOrderInfo> selectMealOrderInfoList(MealOrderInfo mealOrderInfo) {
        return mealOrderInfoMapper.selectMealOrderInfoList(mealOrderInfo);
    }

    /**
     * 新增点餐信息
     *
     * @param mealOrderInfo 点餐信息
     * @return 结果
     */
    @Override
    public int insertMealOrderInfo(MealOrderInfo mealOrderInfo) {
        return mealOrderInfoMapper.insertMealOrderInfo(mealOrderInfo);
    }

    /**
     * 修改点餐信息
     *
     * @param mealOrderInfo 点餐信息
     * @return 结果
     */
    @Override
    public int updateMealOrderInfo(MealOrderInfo mealOrderInfo) {
        return mealOrderInfoMapper.updateMealOrderInfo(mealOrderInfo);
    }

    /**
     * 批量删除点餐信息
     *
     * @param ids 需要删除的点餐信息主键
     * @return 结果
     */
    @Override
    public int deleteMealOrderInfoByIds(Long[] ids) {
        return mealOrderInfoMapper.deleteMealOrderInfoByIds(ids);
    }

    /**
     * 删除点餐信息信息
     *
     * @param id 点餐信息主键
     * @return 结果
     */
    @Override
    public int deleteMealOrderInfoById(Long id) {
        return mealOrderInfoMapper.deleteMealOrderInfoById(id);
    }
}

