package com.ruoyi.custom.admin.mealManagement.service;

import com.ruoyi.custom.admin.mealManagement.domain.MealSampleInfo;

import java.util.List;


/**
 * 餐食留样信息Service接口
 *
 * <AUTHOR>
 * @date 2024-11-25
 */
public interface MealSampleInfoService {
    /**
     * 查询餐食留样信息
     *
     * @param id 餐食留样信息主键
     * @return 餐食留样信息
     */
    public MealSampleInfo selectMealSampleInfoById(Long id);

    /**
     * 查询餐食留样信息列表
     *
     * @param mealSampleInfo 餐食留样信息
     * @return 餐食留样信息集合
     */
    public List<MealSampleInfo> selectMealSampleInfoList(MealSampleInfo mealSampleInfo);

    /**
     * 新增餐食留样信息
     *
     * @param mealSampleInfo 餐食留样信息
     * @return 结果
     */
    public int insertMealSampleInfo(MealSampleInfo mealSampleInfo);

    /**
     * 修改餐食留样信息
     *
     * @param mealSampleInfo 餐食留样信息
     * @return 结果
     */
    public int updateMealSampleInfo(MealSampleInfo mealSampleInfo);

    /**
     * 批量删除餐食留样信息
     *
     * @param ids 需要删除的餐食留样信息主键集合
     * @return 结果
     */
    public int deleteMealSampleInfoByIds(Long[] ids);

    /**
     * 删除餐食留样信息信息
     *
     * @param id 餐食留样信息主键
     * @return 结果
     */
    public int deleteMealSampleInfoById(Long id);
}

