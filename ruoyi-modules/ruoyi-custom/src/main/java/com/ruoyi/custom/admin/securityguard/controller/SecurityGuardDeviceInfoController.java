package com.ruoyi.custom.admin.securityguard.controller;

import com.ruoyi.common.core.utils.poi.ExcelUtil;
import com.ruoyi.common.core.web.controller.BaseController;
import com.ruoyi.common.core.web.domain.AjaxResult;
import com.ruoyi.common.core.web.page.TableDataInfo;
import com.ruoyi.common.log.annotation.Log;
import com.ruoyi.common.log.enums.BusinessType;
import com.ruoyi.custom.admin.securityguard.domain.SecurityGuardDeviceInfo;
import com.ruoyi.custom.admin.securityguard.service.ISecurityGuardDeviceInfoService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 设备信息Controller
 *
 * <AUTHOR>
 * @date 2023-02-03
 */
@RestController
@RequestMapping("/device")
@Api(tags = {"设备信息"})
public class SecurityGuardDeviceInfoController extends BaseController {
    @Autowired
    private ISecurityGuardDeviceInfoService securityGuardDeviceInfoService;

    /**
     * 查询设备信息列表
     */
    // @RequiresPermissions("securityguard:device:list")
    @GetMapping("/list")
    @ApiOperation("查询设备信息列表")
    public TableDataInfo list(SecurityGuardDeviceInfo securityGuardDeviceInfo) {
        startPage();
        List<SecurityGuardDeviceInfo> list = securityGuardDeviceInfoService.selectSecurityGuardDeviceInfoList(securityGuardDeviceInfo);
        return getDataTable(list);
    }

    /**
     * 导出设备信息列表
     */
    // @RequiresPermissions("securityguard:device:export")
    @Log(title = "设备信息", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    @ApiOperation("导出设备信息列表")
    public void export(HttpServletResponse response, SecurityGuardDeviceInfo securityGuardDeviceInfo) {
        List<SecurityGuardDeviceInfo> list = securityGuardDeviceInfoService.selectSecurityGuardDeviceInfoList(securityGuardDeviceInfo);
        ExcelUtil<SecurityGuardDeviceInfo> util = new ExcelUtil<SecurityGuardDeviceInfo>(SecurityGuardDeviceInfo.class);
        util.exportExcel(response, list, "设备信息数据");
    }

    /**
     * 获取设备信息详细信息
     */
    // @RequiresPermissions("securityguard:device:query")
    @GetMapping(value = "/{id}")
    @ApiOperation("获取设备信息详细信息")
    public AjaxResult getInfo(@PathVariable("id") Long id) {
        return AjaxResult.success(securityGuardDeviceInfoService.selectSecurityGuardDeviceInfoById(id));
    }

    /**
     * 新增设备信息
     */
    // @RequiresPermissions("securityguard:device:add")
    @Log(title = "设备信息", businessType = BusinessType.INSERT)
    @PostMapping
    @ApiOperation("新增设备信息")
    public AjaxResult add(@RequestBody SecurityGuardDeviceInfo securityGuardDeviceInfo) {
        return toAjax(securityGuardDeviceInfoService.insertSecurityGuardDeviceInfo(securityGuardDeviceInfo));
    }

    /**
     * 修改设备信息
     */
    // @RequiresPermissions("securityguard:device:edit")
    @Log(title = "设备信息", businessType = BusinessType.UPDATE)
    @PutMapping
    @ApiOperation("修改设备信息")
    public AjaxResult edit(@RequestBody SecurityGuardDeviceInfo securityGuardDeviceInfo) {
        return toAjax(securityGuardDeviceInfoService.updateSecurityGuardDeviceInfo(securityGuardDeviceInfo));
    }

    /**
     * 删除设备信息
     */
    // @RequiresPermissions("securityguard:device:remove")
    @Log(title = "设备信息", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    @ApiOperation("删除设备信息")
    public AjaxResult remove(@PathVariable Long[] ids) {
        return toAjax(securityGuardDeviceInfoService.deleteSecurityGuardDeviceInfoByIds(ids));
    }
}
