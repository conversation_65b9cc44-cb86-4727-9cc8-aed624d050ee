package com.ruoyi.custom.admin.medicalService.domain;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

@Data
@ApiModel(value = "医护服务反馈表")
public class MedicalServiceFeedback {

    @ApiModelProperty(value = "主键")
    private Long id;

    @ApiModelProperty(value = "医护服务工单，对应‘医护服务工单表’")
    private Long workOrderId;

    @ApiModelProperty(value = "反馈医生id，对应sys_user")
    private Long doctorId;

    @ApiModelProperty(value = "反馈医生")
    private String doctorName;

    @ApiModelProperty(value = "反馈内容")
    private String feedbackContent;

    @ApiModelProperty(value = "反馈时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm")
    private Date feedbackTime;

    @ApiModelProperty(value = "图片url,多个逗号隔开")
    private String imageUrls;
}

