package com.ruoyi.custom.admin.securityguard.controller;

import com.ruoyi.common.core.utils.poi.ExcelUtil;
import com.ruoyi.common.core.web.controller.BaseController;
import com.ruoyi.common.core.web.domain.AjaxResult;
import com.ruoyi.common.core.web.page.TableDataInfo;
import com.ruoyi.common.log.annotation.Log;
import com.ruoyi.common.log.enums.BusinessType;
import com.ruoyi.custom.admin.securityguard.domain.SecurityGuardDeviceType;
import com.ruoyi.custom.admin.securityguard.service.ISecurityGuardDeviceTypeService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 设备类型Controller
 *
 * <AUTHOR>
 * @date 2025-03-29
 */
@RestController
@RequestMapping("/deviceType")
@Api(tags = {"设备类型"})
public class SecurityGuardDeviceTypeController extends BaseController {
    @Autowired
    private ISecurityGuardDeviceTypeService securityGuardDeviceTypeService;

    /**
     * 查询设备类型列表
     */
    // @RequiresPermissions("custom:type:list")
    @GetMapping("/list")
    @ApiOperation("查询设备类型列表")
    public TableDataInfo list(SecurityGuardDeviceType securityGuardDeviceType) {
        startPage();
        List<SecurityGuardDeviceType> list = securityGuardDeviceTypeService.selectSecurityGuardDeviceTypeList(securityGuardDeviceType);
        return getDataTable(list);
    }

    /**
     * 导出设备类型列表
     */
    // @RequiresPermissions("custom:type:export")
    @Log(title = "设备类型", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    @ApiOperation("导出设备类型列表")
    public void export(HttpServletResponse response, SecurityGuardDeviceType securityGuardDeviceType) {
        List<SecurityGuardDeviceType> list = securityGuardDeviceTypeService.selectSecurityGuardDeviceTypeList(securityGuardDeviceType);
        ExcelUtil<SecurityGuardDeviceType> util = new ExcelUtil<SecurityGuardDeviceType>(SecurityGuardDeviceType.class);
        util.exportExcel(response, list, "设备类型数据");
    }

    /**
     * 获取设备类型详细信息
     */
    // @RequiresPermissions("custom:type:query")
    @GetMapping(value = "/{id}")
    @ApiOperation("获取设备类型详细信息")
    public AjaxResult getInfo(@PathVariable("id") Long id) {
        return AjaxResult.success(securityGuardDeviceTypeService.selectSecurityGuardDeviceTypeById(id));
    }

    /**
     * 新增设备类型
     */
    // @RequiresPermissions("custom:type:add")
    @Log(title = "设备类型", businessType = BusinessType.INSERT)
    @PostMapping
    @ApiOperation("新增设备类型")
    public AjaxResult add(@RequestBody SecurityGuardDeviceType securityGuardDeviceType) {
        return toAjax(securityGuardDeviceTypeService.insertSecurityGuardDeviceType(securityGuardDeviceType));
    }

    /**
     * 修改设备类型
     */
    // @RequiresPermissions("custom:type:edit")
    @Log(title = "设备类型", businessType = BusinessType.UPDATE)
    @PutMapping
    @ApiOperation("修改设备类型")
    public AjaxResult edit(@RequestBody SecurityGuardDeviceType securityGuardDeviceType) {
        return toAjax(securityGuardDeviceTypeService.updateSecurityGuardDeviceType(securityGuardDeviceType));
    }

    /**
     * 删除设备类型（逻辑删除）
     */
    // @RequiresPermissions("custom:type:remove")
    @Log(title = "设备类型", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    @ApiOperation("删除设备类型（逻辑删除）")
    public AjaxResult remove(@PathVariable Long[] ids) {
        return toAjax(securityGuardDeviceTypeService.deleteSecurityGuardDeviceTypeByIds(ids));
    }
}

