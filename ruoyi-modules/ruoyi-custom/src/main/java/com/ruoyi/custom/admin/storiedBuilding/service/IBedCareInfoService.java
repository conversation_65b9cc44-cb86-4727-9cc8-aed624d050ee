package com.ruoyi.custom.admin.storiedBuilding.service;

import java.util.List;

import com.ruoyi.custom.admin.storiedBuilding.domain.BedCareInfo;

/**
 * 床位和护工关联信息Service接口
 *
 * <AUTHOR>
 * @date 2022-03-31
 */
public interface IBedCareInfoService {
    /**
     * 查询床位和护工关联信息
     *
     * @param id 床位和护工关联信息主键
     * @return 床位和护工关联信息
     */
    public BedCareInfo selectBedCareInfoById(Long id);

    /**
     * 查询床位和护工关联信息列表
     *
     * @param bedCareInfo 床位和护工关联信息
     * @return 床位和护工关联信息集合
     */
    public List<BedCareInfo> selectBedCareInfoList(BedCareInfo bedCareInfo);

    /**
     * 新增床位和护工关联信息
     *
     * @param bedCareInfo 床位和护工关联信息
     * @return 结果
     */
    public int insertBedCareInfo(BedCareInfo bedCareInfo);

    /**
     * 修改床位和护工关联信息
     *
     * @param bedCareInfo 床位和护工关联信息
     * @return 结果
     */
    public int updateBedCareInfo(BedCareInfo bedCareInfo);

    /**
     * 批量删除床位和护工关联信息
     *
     * @param ids 需要删除的床位和护工关联信息主键集合
     * @return 结果
     */
    public int deleteBedCareInfoByIds(String[] ids);

    /**
     * 删除床位和护工关联信息信息
     *
     * @param id 床位和护工关联信息主键
     * @return 结果
     */
    public int deleteBedCareInfoById(String id);

    /**
     * 根据护工id获取床位
     *
     * @param careWorkerId
     * @return
     */
    String getGroupCareBed(String careWorkerId);

    int saveBedCareInfo(String[] beds, String careWorkerId);

}
