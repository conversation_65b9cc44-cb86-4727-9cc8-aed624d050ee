package com.ruoyi.custom.admin.mealManagement.domain;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.ruoyi.common.core.annotation.Excel;
import com.ruoyi.common.core.web.domain.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * 点餐信息对象 t_meal_order_info
 *
 * <AUTHOR>
 * @date 2024-11-22
 */
@Data
@ApiModel(value = "点餐信息")
public class MealOrderInfo extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @ApiModelProperty(value = "主键")
    private Long id;

    /**
     * 用餐人数
     */
    @Excel(name = "用餐人数")
    @ApiModelProperty(value = "用餐人数")
    private Integer numberOfDiners;

    /**
     * 餐次，字典：custom_meal_sample_info_meal_type；1:早餐，2：午餐，3：晚餐，4：其他
     */
    @Excel(name = "餐次", readConverterExp = "1=早餐,2=午餐,3=晚餐,4=其他")
    @ApiModelProperty(value = "餐次，字典：custom_meal_sample_info_meal_type；1:早餐，2：午餐，3：晚餐，4：其他")
    private String mealType;

    /**
     * 用餐日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "用餐日期", width = 30, dateFormat = "yyyy-MM-dd")
    @ApiModelProperty(value = "用餐日期")
    private Date mealDate;

    /**
     * 备注
     */
    @Excel(name = "备注")
    @ApiModelProperty(value = "备注")
    private String remarks;
}

