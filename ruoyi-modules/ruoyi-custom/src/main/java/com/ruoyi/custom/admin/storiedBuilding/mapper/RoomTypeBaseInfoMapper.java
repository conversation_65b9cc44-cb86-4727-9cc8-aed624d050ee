package com.ruoyi.custom.admin.storiedBuilding.mapper;

import java.util.List;

import cn.hutool.json.JSONObject;
import com.ruoyi.custom.admin.storiedBuilding.domain.RoomTypeBaseInfo;

/**
 * 房间类型Mapper接口
 *
 * <AUTHOR>
 * @date 2022-03-31
 */
public interface RoomTypeBaseInfoMapper {
    /**
     * 查询房间类型
     *
     * @param id 房间类型主键
     * @return 房间类型
     */
    public RoomTypeBaseInfo selectRoomTypeBaseInfoById(Long id);

    /**
     * 查询房间类型列表
     *
     * @param roomTypeBaseInfo 房间类型
     * @return 房间类型集合
     */
    public List<RoomTypeBaseInfo> selectRoomTypeBaseInfoList(RoomTypeBaseInfo roomTypeBaseInfo);

    /**
     * 新增房间类型
     *
     * @param roomTypeBaseInfo 房间类型
     * @return 结果
     */
    public int insertRoomTypeBaseInfo(RoomTypeBaseInfo roomTypeBaseInfo);

    /**
     * 修改房间类型
     *
     * @param roomTypeBaseInfo 房间类型
     * @return 结果
     */
    public int updateRoomTypeBaseInfo(RoomTypeBaseInfo roomTypeBaseInfo);

    /**
     * 删除房间类型
     *
     * @param id 房间类型主键
     * @return 结果
     */
    public int deleteRoomTypeBaseInfoById(Long id);

    /**
     * 批量删除房间类型
     *
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteRoomTypeBaseInfoByIds(Long[] ids);

    public JSONObject selectName();

    public List<JSONObject> selectData();

    /**
     * 获取房间类型的key和value值
     *
     * @return
     */
    public List<JSONObject> getRoomTypeLabelAndValue();

    /**
     * 根据房间id获取房间的费用基础信息
     *
     * @return
     */
    public JSONObject getRoomTypeBase(Long roomId);


}
