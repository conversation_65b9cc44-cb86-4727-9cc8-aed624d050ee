package com.ruoyi.custom.admin.mealManagement.controller;

import java.util.List;
import javax.servlet.http.HttpServletResponse;

import cn.hutool.json.JSONArray;
import com.ruoyi.common.core.web.domain.TAjaxResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.log.annotation.Log;
import com.ruoyi.common.log.enums.BusinessType;
import com.ruoyi.custom.admin.mealManagement.domain.RecipeDetailsInfo;
import com.ruoyi.custom.admin.mealManagement.service.IRecipeDetailsInfoService;
import com.ruoyi.common.core.web.controller.BaseController;
import com.ruoyi.common.core.web.domain.AjaxResult;
import com.ruoyi.common.core.utils.poi.ExcelUtil;
import com.ruoyi.common.core.web.page.TableDataInfo;
import springfox.documentation.annotations.ApiIgnore;

/**
 * 食谱详情Controller
 *
 * <AUTHOR>
 * @date 2022-04-21
 */
@RestController
@RequestMapping("/recipeDetailsInfo")
@Api(value = "食谱详情", tags = "食谱详情")
public class RecipeDetailsInfoController extends BaseController {
    @Autowired
    private IRecipeDetailsInfoService recipeDetailsInfoService;

    /**
     * 查询食谱详情列表
     */
    //@RequiresPermissions("mealManagement:recipeDetailsInfo:list")
    @GetMapping("/list")
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "query", name = "pageNum", value = "当前页码", required = false, dataTypeClass = String.class),
            @ApiImplicitParam(paramType = "query", name = "pageSize", value = "每页条数", required = false, dataTypeClass = String.class),
            @ApiImplicitParam(paramType = "query", name = "typeId", value = "食谱类型id", required = true, dataTypeClass = String.class),

    })
    @ApiOperation(value = "食谱详情列表")
    public TableDataInfo<RecipeDetailsInfo> list(@ApiIgnore RecipeDetailsInfo recipeDetailsInfo) {
        startPage();
        List<RecipeDetailsInfo> list = recipeDetailsInfoService.selectRecipeDetailsInfoList(recipeDetailsInfo);
        return getDataTable(list);
    }

    /**
     * 导出食谱详情列表
     */
    //@RequiresPermissions("mealManagement:recipeDetailsInfo:export")
    @Log(platform = "1", title = "食谱详情", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    @ApiIgnore
    public void export(HttpServletResponse response, RecipeDetailsInfo recipeDetailsInfo) {
        List<RecipeDetailsInfo> list = recipeDetailsInfoService.selectRecipeDetailsInfoList(recipeDetailsInfo);
        ExcelUtil<RecipeDetailsInfo> util = new ExcelUtil<RecipeDetailsInfo>(RecipeDetailsInfo.class);
        util.exportExcel(response, list, "食谱详情数据");
    }

    /**
     * 获取食谱详情详细信息
     */
    //@RequiresPermissions("mealManagement:recipeDetailsInfo:query")
    @GetMapping(value = "/{id}")
    @ApiOperation(value = "获取食谱详情详细信息")
    public TAjaxResult<RecipeDetailsInfo> getInfo(@PathVariable("id") Long id) {
        return new TAjaxResult<RecipeDetailsInfo>(recipeDetailsInfoService.selectRecipeDetailsInfoById(id));
    }


    /**
     * 保存食谱
     */
    //@RequiresPermissions("mealManagement:recipeDetailsInfo:add")
    @Log(platform = "1", title = "食谱详情", businessType = BusinessType.INSERT)
    @PostMapping("/save")
    @ApiOperation(value = "保存食谱")
    public AjaxResult save(@RequestBody RecipeDetailsInfo recipeDetailsInfo) {
        JSONArray array = recipeDetailsInfo.getArray();
        recipeDetailsInfo.setData(array.toString());
        if (null == recipeDetailsInfo.getId() || recipeDetailsInfo.getId() == 0) {
            return add(recipeDetailsInfo);
        } else {
            return edit(recipeDetailsInfo);
        }
    }


    /**
     * 新增食谱详情
     */
    //@RequiresPermissions("mealManagement:recipeDetailsInfo:add")
    @Log(platform = "1", title = "食谱详情", businessType = BusinessType.INSERT)
    @PostMapping
    @ApiIgnore
    public AjaxResult add(@RequestBody RecipeDetailsInfo recipeDetailsInfo) {
        return toAjax(recipeDetailsInfoService.insertRecipeDetailsInfo(recipeDetailsInfo));
    }

    /**
     * 修改食谱详情
     */
    //@RequiresPermissions("mealManagement:recipeDetailsInfo:edit")
    @Log(platform = "1", title = "食谱详情", businessType = BusinessType.UPDATE)
    @PutMapping
    @ApiIgnore
    public AjaxResult edit(@RequestBody RecipeDetailsInfo recipeDetailsInfo) {
        return toAjax(recipeDetailsInfoService.updateRecipeDetailsInfo(recipeDetailsInfo));
    }

    /**
     * 删除食谱详情
     */
    //@RequiresPermissions("mealManagement:recipeDetailsInfo:remove")
    @Log(platform = "1", title = "食谱详情", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    @ApiOperation(value = "删除食谱详情")
    public AjaxResult remove(@PathVariable Long[] ids) {
        return toAjax(recipeDetailsInfoService.deleteRecipeDetailsInfoByIds(ids));
    }
}
