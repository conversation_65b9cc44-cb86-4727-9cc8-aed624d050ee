package com.ruoyi.custom.admin.medicalService.controller;

import com.ruoyi.common.core.utils.poi.ExcelUtil;
import com.ruoyi.common.core.web.controller.BaseController;
import com.ruoyi.common.core.web.domain.AjaxResult;
import com.ruoyi.common.core.web.page.TableDataInfo;
import com.ruoyi.common.log.annotation.Log;
import com.ruoyi.common.log.enums.BusinessType;
import com.ruoyi.custom.admin.medicalService.domain.MedicalServiceFeedback;
import com.ruoyi.custom.admin.medicalService.service.IMedicalServiceFeedbackService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 医护服务反馈Controller
 *
 * <AUTHOR>
 * @date 2025-01-15
 */
@RestController
@RequestMapping("/medicalService/feedback")
@Api(tags = "医护服务反馈")
public class MedicalServiceFeedbackController extends BaseController {
    @Autowired
    private IMedicalServiceFeedbackService medicalServiceFeedbackService;

    /**
     * 查询医护服务反馈列表
     */
    // @RequiresPermissions("custom:feedback:list")
    @GetMapping("/list")
    @ApiOperation(value = "获取列表")
    public TableDataInfo list(MedicalServiceFeedback medicalServiceFeedback) {
        startPage();
        List<MedicalServiceFeedback> list = medicalServiceFeedbackService.selectMedicalServiceFeedbackList(medicalServiceFeedback);
        return getDataTable(list);
    }

    /**
     * 获取医护服务反馈详细信息
     */
    // @RequiresPermissions("custom:feedback:query")
    @GetMapping(value = "/{id}")
    @ApiOperation(value = "获取详情")
    public AjaxResult getInfo(@PathVariable("id") Long id) {
        return AjaxResult.success(medicalServiceFeedbackService.selectMedicalServiceFeedbackById(id));
    }

    /**
     * 新增医护服务反馈
     */
    // @RequiresPermissions("custom:feedback:add")
    @Log(title = "医护服务反馈", businessType = BusinessType.INSERT)
    @PostMapping
    @ApiOperation(value = "新增")
    public AjaxResult add(@RequestBody MedicalServiceFeedback medicalServiceFeedback) {
        if (medicalServiceFeedback.getWorkOrderId() == null) {
            return AjaxResult.error("工单号不能为空");
        }
        return toAjax(medicalServiceFeedbackService.insertMedicalServiceFeedback(medicalServiceFeedback));
    }

    /**
     * 修改医护服务反馈
     */
    // @RequiresPermissions("custom:feedback:edit")
    @Log(title = "医护服务反馈", businessType = BusinessType.UPDATE)
    @PutMapping
    @ApiOperation(value = "修改")
    public AjaxResult edit(@RequestBody MedicalServiceFeedback medicalServiceFeedback) {
        if (medicalServiceFeedback.getId() == null) {
            return AjaxResult.error("id不能为空");
        }
        return toAjax(medicalServiceFeedbackService.updateMedicalServiceFeedback(medicalServiceFeedback));
    }

    /**
     * 删除医护服务反馈
     */
    // @RequiresPermissions("custom:feedback:remove")
    @Log(title = "医护服务反馈", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    @ApiOperation(value = "删除")
    public AjaxResult remove(@PathVariable Long[] ids) {
        return toAjax(medicalServiceFeedbackService.deleteMedicalServiceFeedbackByIds(ids));
    }

    /**
     * 导出医护服务反馈列表
     */
    // @RequiresPermissions("custom:feedback:export")
    @Log(title = "医护服务反馈", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, MedicalServiceFeedback medicalServiceFeedback) {
        List<MedicalServiceFeedback> list = medicalServiceFeedbackService.selectMedicalServiceFeedbackList(medicalServiceFeedback);
        ExcelUtil<MedicalServiceFeedback> util = new ExcelUtil<MedicalServiceFeedback>(MedicalServiceFeedback.class);
        util.exportExcel(response, list, "医护服务反馈数据");
    }
}
