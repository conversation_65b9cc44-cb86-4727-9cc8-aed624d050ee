package com.ruoyi.custom.admin.storiedBuilding.mapper;

import java.util.List;

import com.ruoyi.custom.admin.storiedBuilding.domain.BedCareInfo;
import org.apache.ibatis.annotations.Param;

/**
 * 床位和护工关联信息Mapper接口
 *
 * <AUTHOR>
 * @date 2022-03-31
 */
public interface BedCareInfoMapper {
    /**
     * 查询床位和护工关联信息
     *
     * @param id 床位和护工关联信息主键
     * @return 床位和护工关联信息
     */
    public BedCareInfo selectBedCareInfoById(Long id);

    /**
     * 查询床位和护工关联信息列表
     *
     * @param bedCareInfo 床位和护工关联信息
     * @return 床位和护工关联信息集合
     */
    public List<BedCareInfo> selectBedCareInfoList(BedCareInfo bedCareInfo);

    /**
     * 新增床位和护工关联信息
     *
     * @param bedCareInfo 床位和护工关联信息
     * @return 结果
     */
    public int insertBedCareInfo(BedCareInfo bedCareInfo);

    /**
     * 修改床位和护工关联信息
     *
     * @param bedCareInfo 床位和护工关联信息
     * @return 结果
     */
    public int updateBedCareInfo(BedCareInfo bedCareInfo);

    /**
     * 删除床位和护工关联信息
     *
     * @param id 床位和护工关联信息主键
     * @return 结果
     */
    public int deleteBedCareInfoById(String id);

    /**
     * 批量删除床位和护工关联信息
     *
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteBedCareInfoByIds(String[] ids);


    /**
     * 根据护工id获取床位
     *
     * @param careWorkerId
     * @return
     */
    String getGroupCareBed(String careWorkerId);


    /**
     * 护工和床位绑定
     *
     * @param bedIds
     * @param careWorkerId
     * @return
     */
    int batchBed(@Param("bedIds") String[] bedIds, @Param("careWorkerId") String careWorkerId);


    /**
     * 护工和床位解绑
     *
     * @param bedIds
     * @param careWorkerId
     * @return
     */
    int updateBed(@Param("bedIds") String[] bedIds, @Param("careWorkerId") String careWorkerId);

    /**
     * 检查床位是否被其他护工绑定
     * @param beds
     * @return
     */
    int persentCheck(String[] beds);
}
