package com.ruoyi.custom.admin.storiedBuilding.service.impl;

import java.util.List;

import com.ruoyi.common.core.utils.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.custom.admin.storiedBuilding.mapper.RoomTypeIndexInfoMapper;
import com.ruoyi.custom.admin.storiedBuilding.domain.RoomTypeIndexInfo;
import com.ruoyi.custom.admin.storiedBuilding.service.IRoomTypeIndexInfoService;

/**
 * 房间和类型的关联Service业务层处理
 *
 * <AUTHOR>
 * @date 2022-03-31
 */
@Service
public class RoomTypeIndexInfoServiceImpl implements IRoomTypeIndexInfoService {
    @Autowired
    private RoomTypeIndexInfoMapper roomTypeIndexInfoMapper;

    /**
     * 查询房间和类型的关联
     *
     * @param id 房间和类型的关联主键
     * @return 房间和类型的关联
     */
    @Override
    public RoomTypeIndexInfo selectRoomTypeIndexInfoById(Long id) {
        return roomTypeIndexInfoMapper.selectRoomTypeIndexInfoById(id);
    }

    /**
     * 查询房间和类型的关联列表
     *
     * @param RoomTypeIndexInfo 房间和类型的关联
     * @return 房间和类型的关联
     */
    @Override
    public List<RoomTypeIndexInfo> selectRoomTypeIndexInfoList(RoomTypeIndexInfo RoomTypeIndexInfo) {
        return roomTypeIndexInfoMapper.selectRoomTypeIndexInfoList(RoomTypeIndexInfo);
    }

    /**
     * 新增房间和类型的关联
     *
     * @param RoomTypeIndexInfo 房间和类型的关联
     * @return 结果
     */
    @Override
    public int insertRoomTypeIndexInfo(RoomTypeIndexInfo RoomTypeIndexInfo) {
        RoomTypeIndexInfo.setCreateTime(DateUtils.getNowDate());
        return roomTypeIndexInfoMapper.insertRoomTypeIndexInfo(RoomTypeIndexInfo);
    }

    /**
     * 修改房间和类型的关联
     *
     * @param RoomTypeIndexInfo 房间和类型的关联
     * @return 结果
     */
    @Override
    public int updateRoomTypeIndexInfo(RoomTypeIndexInfo RoomTypeIndexInfo) {
        return roomTypeIndexInfoMapper.updateRoomTypeIndexInfo(RoomTypeIndexInfo);
    }

    /**
     * 批量删除房间和类型的关联
     *
     * @param ids 需要删除的房间和类型的关联主键
     * @return 结果
     */
    @Override
    public int deleteRoomTypeIndexInfoByIds(Long[] ids) {
        return roomTypeIndexInfoMapper.deleteRoomTypeIndexInfoByIds(ids);
    }

    /**
     * 删除房间和类型的关联信息
     *
     * @param id 房间和类型的关联主键
     * @return 结果
     */
    @Override
    public int deleteRoomTypeIndexInfoById(Long id) {
        return roomTypeIndexInfoMapper.deleteRoomTypeIndexInfoById(id);
    }


    /**
     * 根据房间id批量删除房间和类型的关联
     *
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    @Override
    public int deleteByRoomId(Long[] ids) {
        return roomTypeIndexInfoMapper.deleteByRoomId(ids);
    }

    /**
     * 当前房间类型是否有在用
     *
     * @param typeId
     * @return
     */
    @Override
    public Boolean hasRoomTypeId(Long typeId) {
        int result = roomTypeIndexInfoMapper.hasRoomTypeId(typeId);
        return result > 0 ? true : false;
    }
}
