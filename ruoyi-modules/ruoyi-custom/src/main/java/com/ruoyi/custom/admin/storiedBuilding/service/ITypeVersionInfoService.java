package com.ruoyi.custom.admin.storiedBuilding.service;

import java.util.List;

import com.ruoyi.custom.admin.storiedBuilding.domain.TypeVersionInfo;

/**
 * 房间类型版本Service接口
 *
 * <AUTHOR>
 * @date 2022-04-01
 */
public interface ITypeVersionInfoService {
    /**
     * 查询房间类型版本
     *
     * @param id 房间类型版本主键
     * @return 房间类型版本
     */
    public TypeVersionInfo selectTypeVersionInfoById(Long id);

    /**
     * 查询房间类型版本列表
     *
     * @param typeVersionInfo 房间类型版本
     * @return 房间类型版本集合
     */
    public List<TypeVersionInfo> selectTypeVersionInfoList(TypeVersionInfo typeVersionInfo);

    /**
     * 新增房间类型版本
     *
     * @param typeVersionInfo 房间类型版本
     * @return 结果
     */
    public int insertTypeVersionInfo(TypeVersionInfo typeVersionInfo);

    /**
     * 修改房间类型版本
     *
     * @param typeVersionInfo 房间类型版本
     * @return 结果
     */
    public int updateTypeVersionInfo(TypeVersionInfo typeVersionInfo);

    /**
     * 批量删除房间类型版本
     *
     * @param ids 需要删除的房间类型版本主键集合
     * @return 结果
     */
    public int deleteTypeVersionInfoByIds(Long[] ids);

    /**
     * 删除房间类型版本信息
     *
     * @param id 房间类型版本主键
     * @return 结果
     */
    public int deleteTypeVersionInfoById(Long id);
}
