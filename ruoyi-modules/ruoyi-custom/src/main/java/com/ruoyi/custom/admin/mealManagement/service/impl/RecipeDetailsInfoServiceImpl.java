package com.ruoyi.custom.admin.mealManagement.service.impl;

import java.util.List;

import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONUtil;
import com.ruoyi.common.core.utils.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.custom.admin.mealManagement.mapper.RecipeDetailsInfoMapper;
import com.ruoyi.custom.admin.mealManagement.domain.RecipeDetailsInfo;
import com.ruoyi.custom.admin.mealManagement.service.IRecipeDetailsInfoService;

/**
 * 食谱详情Service业务层处理
 *
 * <AUTHOR>
 * @date 2022-04-21
 */
@Service
public class RecipeDetailsInfoServiceImpl implements IRecipeDetailsInfoService {
    @Autowired
    private RecipeDetailsInfoMapper recipeDetailsInfoMapper;

    /**
     * 查询食谱详情
     *
     * @param id 食谱详情主键
     * @return 食谱详情
     */
    @Override
    public RecipeDetailsInfo selectRecipeDetailsInfoById(Long id) {
        RecipeDetailsInfo recipeDetailsInfo = recipeDetailsInfoMapper.selectRecipeDetailsInfoById(id);
        if (null != recipeDetailsInfo.getData()) {
            JSONArray objects = JSONUtil.parseArray(recipeDetailsInfo.getData());
            recipeDetailsInfo.setArray(objects);
        }
        return recipeDetailsInfo;
    }

    /**
     * 查询食谱详情列表
     *
     * @param recipeDetailsInfo 食谱详情
     * @return 食谱详情
     */
    @Override
    public List<RecipeDetailsInfo> selectRecipeDetailsInfoList(RecipeDetailsInfo recipeDetailsInfo) {
        return recipeDetailsInfoMapper.selectRecipeDetailsInfoList(recipeDetailsInfo);
    }

    /**
     * 新增食谱详情
     *
     * @param recipeDetailsInfo 食谱详情
     * @return 结果
     */
    @Override
    public int insertRecipeDetailsInfo(RecipeDetailsInfo recipeDetailsInfo) {
        recipeDetailsInfo.setCreateTime(DateUtils.getNowDate());
        return recipeDetailsInfoMapper.insertRecipeDetailsInfo(recipeDetailsInfo);
    }

    /**
     * 修改食谱详情
     *
     * @param recipeDetailsInfo 食谱详情
     * @return 结果
     */
    @Override
    public int updateRecipeDetailsInfo(RecipeDetailsInfo recipeDetailsInfo) {
        recipeDetailsInfo.setUpdateTime(DateUtils.getNowDate());
        return recipeDetailsInfoMapper.updateRecipeDetailsInfo(recipeDetailsInfo);
    }

    /**
     * 批量删除食谱详情
     *
     * @param ids 需要删除的食谱详情主键
     * @return 结果
     */
    @Override
    public int deleteRecipeDetailsInfoByIds(Long[] ids) {
        return recipeDetailsInfoMapper.deleteRecipeDetailsInfoByIds(ids);
    }

    /**
     * 删除食谱详情信息
     *
     * @param id 食谱详情主键
     * @return 结果
     */
    @Override
    public int deleteRecipeDetailsInfoById(Long id) {
        return recipeDetailsInfoMapper.deleteRecipeDetailsInfoById(id);
    }
}
