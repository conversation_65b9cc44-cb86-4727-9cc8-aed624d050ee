package com.ruoyi.custom.admin.mealManagement.service;


import com.ruoyi.custom.admin.mealManagement.domain.MealOrderInfo;

import java.util.List;


/**
 * 点餐信息Service接口
 *
 * <AUTHOR>
 * @date 2024-11-28
 */
public interface MealOrderInfoService {
    /**
     * 查询点餐信息
     *
     * @param id 点餐信息主键
     * @return 点餐信息
     */
    public MealOrderInfo selectMealOrderInfoById(Long id);

    /**
     * 查询点餐信息列表
     *
     * @param mealOrderInfo 点餐信息
     * @return 点餐信息集合
     */
    public List<MealOrderInfo> selectMealOrderInfoList(MealOrderInfo mealOrderInfo);

    /**
     * 新增点餐信息
     *
     * @param mealOrderInfo 点餐信息
     * @return 结果
     */
    public int insertMealOrderInfo(MealOrderInfo mealOrderInfo);

    /**
     * 修改点餐信息
     *
     * @param mealOrderInfo 点餐信息
     * @return 结果
     */
    public int updateMealOrderInfo(MealOrderInfo mealOrderInfo);

    /**
     * 批量删除点餐信息
     *
     * @param ids 需要删除的点餐信息主键集合
     * @return 结果
     */
    public int deleteMealOrderInfoByIds(Long[] ids);

    /**
     * 删除点餐信息信息
     *
     * @param id 点餐信息主键
     * @return 结果
     */
    public int deleteMealOrderInfoById(Long id);
}

