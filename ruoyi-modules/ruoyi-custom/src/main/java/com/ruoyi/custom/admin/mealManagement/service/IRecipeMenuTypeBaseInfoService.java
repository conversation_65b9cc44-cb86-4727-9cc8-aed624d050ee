package com.ruoyi.custom.admin.mealManagement.service;

import java.util.List;

import cn.hutool.json.JSONObject;
import com.ruoyi.custom.admin.mealManagement.domain.RecipeMenuTypeBaseInfo;

/**
 * 食谱菜单类型基本信息Service接口
 *
 * <AUTHOR>
 * @date 2022-04-21
 */
public interface IRecipeMenuTypeBaseInfoService {
    /**
     * 查询食谱菜单类型基本信息
     *
     * @param id 食谱菜单类型基本信息主键
     * @return 食谱菜单类型基本信息
     */
    public RecipeMenuTypeBaseInfo selectRecipeMenuTypeBaseInfoById(Long id);

    /**
     * 查询食谱菜单类型基本信息列表
     *
     * @param recipeMenuTypeBaseInfo 食谱菜单类型基本信息
     * @return 食谱菜单类型基本信息集合
     */
    public List<RecipeMenuTypeBaseInfo> selectRecipeMenuTypeBaseInfoList(RecipeMenuTypeBaseInfo recipeMenuTypeBaseInfo);

    /**
     * 新增食谱菜单类型基本信息
     *
     * @param recipeMenuTypeBaseInfo 食谱菜单类型基本信息
     * @return 结果
     */
    public int insertRecipeMenuTypeBaseInfo(RecipeMenuTypeBaseInfo recipeMenuTypeBaseInfo);

    /**
     * 修改食谱菜单类型基本信息
     *
     * @param recipeMenuTypeBaseInfo 食谱菜单类型基本信息
     * @return 结果
     */
    public int updateRecipeMenuTypeBaseInfo(RecipeMenuTypeBaseInfo recipeMenuTypeBaseInfo);

    /**
     * 批量删除食谱菜单类型基本信息
     *
     * @param ids 需要删除的食谱菜单类型基本信息主键集合
     * @return 结果
     */
    public int deleteRecipeMenuTypeBaseInfoByIds(Long[] ids);

    /**
     * 删除食谱菜单类型基本信息信息
     *
     * @param id 食谱菜单类型基本信息主键
     * @return 结果
     */
    public int deleteRecipeMenuTypeBaseInfoById(Long id);


    /**
     * 获取全量的食谱菜单类型
     *
     * @return
     */
    List<JSONObject> getRecipeMenuTypeList();
}
