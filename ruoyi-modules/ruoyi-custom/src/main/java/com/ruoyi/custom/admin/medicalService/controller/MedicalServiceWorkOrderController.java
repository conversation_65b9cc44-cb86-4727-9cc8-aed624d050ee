package com.ruoyi.custom.admin.medicalService.controller;


import java.util.List;

import com.ruoyi.custom.admin.medicalService.domain.MedicalServiceWorkOrder;
import com.ruoyi.custom.admin.medicalService.service.IMedicalServiceWorkOrderService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.log.annotation.Log;
import com.ruoyi.common.log.enums.BusinessType;
import com.ruoyi.common.core.web.controller.BaseController;
import com.ruoyi.common.core.web.domain.AjaxResult;
import com.ruoyi.common.core.web.page.TableDataInfo;

/**
 * 医护服务工单Controller
 *
 * <AUTHOR>
 * @date 2025-01-14
 */
@RestController
@RequestMapping("/medicalService/workOrder")
@Api(tags = "医护服务工单")
public class MedicalServiceWorkOrderController extends BaseController {
    @Autowired
    private IMedicalServiceWorkOrderService medicalServiceWorkOrderService;

    /**
     * 查询医护服务工单列表
     */
    // @RequiresPermissions("custom:order:list")
    @GetMapping("/list")
    @ApiOperation(value = "获取列表")
    public TableDataInfo list(MedicalServiceWorkOrder medicalServiceWorkOrder) {
        startPage();
        List<MedicalServiceWorkOrder> list = medicalServiceWorkOrderService.selectMedicalServiceWorkOrderList(medicalServiceWorkOrder);
        return getDataTable(list);
    }

    /**
     * 获取医护服务工单详细信息
     */
    // @RequiresPermissions("custom:order:query")
    @GetMapping(value = "/{id}")
    @ApiOperation(value = "获取详情")
    public AjaxResult getInfo(@PathVariable("id") Long id) {
        return AjaxResult.success(medicalServiceWorkOrderService.selectMedicalServiceWorkOrderById(id));
    }

    /**
     * 新增医护服务工单
     */
    // @RequiresPermissions("custom:order:add")
    @Log(title = "医护服务工单", businessType = BusinessType.INSERT)
    @PostMapping
    @ApiOperation(value = "新增")
    public AjaxResult add(@RequestBody MedicalServiceWorkOrder medicalServiceWorkOrder) {
        return toAjax(medicalServiceWorkOrderService.insertMedicalServiceWorkOrder(medicalServiceWorkOrder));
    }

    /**
     * 修改医护服务工单
     */
    // @RequiresPermissions("custom:order:edit")
    @Log(title = "医护服务工单", businessType = BusinessType.UPDATE)
    @PutMapping
    @ApiOperation(value = "修改")
    public AjaxResult edit(@RequestBody MedicalServiceWorkOrder medicalServiceWorkOrder) {
        return toAjax(medicalServiceWorkOrderService.updateMedicalServiceWorkOrder(medicalServiceWorkOrder));
    }

    /**
     * 删除医护服务工单
     */
    // @RequiresPermissions("custom:order:remove")
    @Log(title = "医护服务工单", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    @ApiOperation(value = "删除")
    public AjaxResult remove(@PathVariable Long[] ids) {
        return toAjax(medicalServiceWorkOrderService.deleteMedicalServiceWorkOrderByIds(ids));
    }

    // /**
    //  * 导出医护服务工单列表
    //  */
    // // @RequiresPermissions("custom:order:export")
    // @Log(title = "医护服务工单", businessType = BusinessType.EXPORT)
    // @PostMapping("/export")
    // public void export(HttpServletResponse response, MedicalServiceWorkOrder medicalServiceWorkOrder) {
    //     List<MedicalServiceWorkOrder> list = medicalServiceWorkOrderService.selectMedicalServiceWorkOrderList(medicalServiceWorkOrder);
    //     ExcelUtil<MedicalServiceWorkOrder> util = new ExcelUtil<MedicalServiceWorkOrder>(MedicalServiceWorkOrder. class);
    //     util.exportExcel(response, list, "医护服务工单数据");
    // }
}

