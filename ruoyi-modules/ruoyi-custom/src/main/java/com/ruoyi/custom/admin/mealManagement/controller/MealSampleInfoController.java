package com.ruoyi.custom.admin.mealManagement.controller;


import com.ruoyi.common.core.utils.poi.ExcelUtil;
import com.ruoyi.common.core.web.controller.BaseController;
import com.ruoyi.common.core.web.domain.AjaxResult;
import com.ruoyi.common.core.web.page.TableDataInfo;
import com.ruoyi.common.log.annotation.Log;
import com.ruoyi.common.log.enums.BusinessType;
import com.ruoyi.custom.admin.mealManagement.domain.MealSampleInfo;
import com.ruoyi.custom.admin.mealManagement.service.MealSampleInfoService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 餐食留样信息Controller
 *
 * <AUTHOR>
 * @date 2024-11-25
 */
@RestController
@RequestMapping("/mealSample")
@Api(tags = "餐食留样信息管理")
public class MealSampleInfoController extends BaseController {
    @Autowired
    private MealSampleInfoService mealSampleInfoService;

    /**
     * 查询餐食留样信息列表
     */
    // @RequiresPermissions("custom:info:list")
    @GetMapping("/list")
    @ApiOperation(value = "获取餐食留样信息列表")
    public TableDataInfo list(MealSampleInfo mealSampleInfo) {
        startPage();
        List<MealSampleInfo> list = mealSampleInfoService.selectMealSampleInfoList(mealSampleInfo);
        return getDataTable(list);
    }

    /**
     * 导出餐食留样信息列表
     */
    // @RequiresPermissions("custom:info:export")
    @Log(title = "餐食留样信息", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    @ApiOperation(value = "导出餐食留样信息列表")
    public void export(HttpServletResponse response, MealSampleInfo mealSampleInfo) {
        List<MealSampleInfo> list = mealSampleInfoService.selectMealSampleInfoList(mealSampleInfo);
        ExcelUtil<MealSampleInfo> util = new ExcelUtil<MealSampleInfo>(MealSampleInfo.class);
        util.exportExcel(response, list, "餐食留样信息数据");
    }

    /**
     * 获取餐食留样信息详细信息
     */
    // @RequiresPermissions("custom:info:query")
    @GetMapping(value = "/{id}")
    @ApiOperation(value = "获取餐食留样信息详细信息")
    public AjaxResult getInfo(@PathVariable("id") Long id) {
        return AjaxResult.success(mealSampleInfoService.selectMealSampleInfoById(id));
    }

    /**
     * 新增餐食留样信息
     */
    // @RequiresPermissions("custom:info:add")
    @Log(title = "餐食留样信息", businessType = BusinessType.INSERT)
    @PostMapping
    @ApiOperation(value = "新增餐食留样信息")
    public AjaxResult add(@RequestBody MealSampleInfo mealSampleInfo) {
        return toAjax(mealSampleInfoService.insertMealSampleInfo(mealSampleInfo));
    }

    /**
     * 修改餐食留样信息
     */
    // @RequiresPermissions("custom:info:edit")
    @Log(title = "餐食留样信息", businessType = BusinessType.UPDATE)
    @PutMapping
    @ApiOperation(value = "修改餐食留样信息")
    public AjaxResult edit(@RequestBody MealSampleInfo mealSampleInfo) {
        return toAjax(mealSampleInfoService.updateMealSampleInfo(mealSampleInfo));
    }

    /**
     * 删除餐食留样信息
     */
    // @RequiresPermissions("custom:info:remove")
    @Log(title = "餐食留样信息", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    @ApiOperation(value = "删除餐食留样信息")
    public AjaxResult remove(@PathVariable Long[] ids) {
        return toAjax(mealSampleInfoService.deleteMealSampleInfoByIds(ids));
    }
}

