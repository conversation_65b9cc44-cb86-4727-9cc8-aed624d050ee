package com.ruoyi.custom.admin.storiedBuilding.mapper;

import java.util.List;
import java.util.Map;

import cn.hutool.json.JSONObject;
import com.ruoyi.custom.admin.storiedBuilding.domain.StoriedBuildingInfo;
import org.apache.ibatis.annotations.Param;

/**
 * 楼栋信息Mapper接口
 *
 * <AUTHOR>
 * @date 2022-03-29
 */
public interface StoriedBuildingInfoMapper {
    /**
     * 查询楼栋信息
     *
     * @param id 楼栋信息主键
     * @return 楼栋信息
     */
    public StoriedBuildingInfo selectStoriedBuildingInfoById(Long id);

    /**
     * 查询此楼栋是否有下级
     *
     * @param pid
     * @return
     */
    public List<StoriedBuildingInfo> getInfoByPid(Long pid);


    /**
     * 根据id修改房间的床位数
     *
     * @param id
     * @param totalBedNumber
     * @return
     */
    public int updateStoriedBedNum(@Param("id") Long id, @Param("totalBedNumber") Long totalBedNumber, @Param("occupancyNumber") Long occupancyNumber);

    /**
     * 查询楼栋信息列表
     *
     * @param storiedBuildingInfo 楼栋信息
     * @return 楼栋信息集合
     */
    public List<StoriedBuildingInfo> selectStoriedBuildingInfoList(StoriedBuildingInfo storiedBuildingInfo);

    /**
     * 新增楼栋信息
     *
     * @param storiedBuildingInfo 楼栋信息
     * @return 结果
     */
    public int insertStoriedBuildingInfo(StoriedBuildingInfo storiedBuildingInfo);

    public Map<String, Object> selectByNum(String pid);

    /**
     * 修改楼栋信息
     *
     * @param storiedBuildingInfo 楼栋信息
     * @return 结果
     */
    public int updateStoriedBuildingInfo(StoriedBuildingInfo storiedBuildingInfo);

    /**
     * 删除楼栋信息
     *
     * @param id 楼栋信息主键
     * @return 结果
     */
    public int deleteStoriedBuildingInfoById(Long id);

    /**
     * 批量删除楼栋信息
     *
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteStoriedBuildingInfoByIds(Long[] ids);

    /**
     * @param type
     * @param name
     * @param parentId
     * @return
     */
    public StoriedBuildingInfo selectInfoByName(String type, String name, String parentId);

    /**
     * 根据id获取是否有下级
     *
     * @param id
     * @return
     */
    public int hasChildById(Long id);

    /**
     * 根据id和类型返回楼栋信息
     *
     * @param type
     * @param parentId
     * @return
     */
    public List<JSONObject> getBuildingInfoList(@Param("type") String type, @Param("parentId") String parentId);
}
