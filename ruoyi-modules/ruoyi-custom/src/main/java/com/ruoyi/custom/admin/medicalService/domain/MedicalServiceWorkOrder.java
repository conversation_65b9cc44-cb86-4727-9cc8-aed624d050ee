package com.ruoyi.custom.admin.medicalService.domain;

import com.baomidou.mybatisplus.annotation.TableField;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.ruoyi.common.core.web.domain.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

@Data
@ApiModel(value = "医护服务工单表")
public class MedicalServiceWorkOrder extends BaseEntity {

    @ApiModelProperty(value = "主键")
    private Long id;

    @ApiModelProperty(value = "工单号，前缀：DSWO")
    private String workOrderNumber;

    @ApiModelProperty(value = "老人id，对应：t_elderly_people_info")
    private String elderlyId;

    @ApiModelProperty(value = "老人姓名")
    private String elderlyName;

    @ApiModelProperty(value = "服务项目，字典：custom_medical_service_work_order_service_item")
    private String serviceItem;

    @ApiModelProperty(value = "需求")
    private String requirement;

    @ApiModelProperty(value = "服务开始时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm")
    private Date serviceStartTime;

    @ApiModelProperty(value = "服务结束时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm")
    private Date serviceEndTime;

    @ApiModelProperty(value = "服务人员id，对应sys_user")
    private Long servicePersonnelId;

    @ApiModelProperty(value = "服务人员")
    private String servicePersonnelName;

    @ApiModelProperty(value = "拒绝原因")
    private String rejectReason;

    @ApiModelProperty(value = "服务进度，字典：custom_medical_service_work_order_service_progress")
    @TableField(exist = false)
    private String serviceProgress;

    @ApiModelProperty(value = "床位名称，格式：楼-层-房-床")
    @TableField(exist = false)
    private String bedName;
}

