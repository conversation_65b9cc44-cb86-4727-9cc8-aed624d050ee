package com.ruoyi.custom.admin.medicalService.service;


import com.ruoyi.custom.admin.medicalService.domain.MedicalServiceWorkOrder;

import java.util.List;

/**
 * 医护服务工单Service接口
 *
 * <AUTHOR>
 * @date 2025-01-14
 */
public interface IMedicalServiceWorkOrderService {
    /**
     * 查询医护服务工单
     *
     * @param id 医护服务工单主键
     * @return 医护服务工单
     */
    public MedicalServiceWorkOrder selectMedicalServiceWorkOrderById(Long id);

    /**
     * 查询医护服务工单列表
     *
     * @param medicalServiceWorkOrder 医护服务工单
     * @return 医护服务工单集合
     */
    public List<MedicalServiceWorkOrder> selectMedicalServiceWorkOrderList(MedicalServiceWorkOrder medicalServiceWorkOrder);

    /**
     * 新增医护服务工单
     *
     * @param medicalServiceWorkOrder 医护服务工单
     * @return 结果
     */
    public int insertMedicalServiceWorkOrder(MedicalServiceWorkOrder medicalServiceWorkOrder);

    /**
     * 修改医护服务工单
     *
     * @param medicalServiceWorkOrder 医护服务工单
     * @return 结果
     */
    public int updateMedicalServiceWorkOrder(MedicalServiceWorkOrder medicalServiceWorkOrder);

    /**
     * 批量删除医护服务工单
     *
     * @param ids 需要删除的医护服务工单主键集合
     * @return 结果
     */
    public int deleteMedicalServiceWorkOrderByIds(Long[] ids);

    /**
     * 删除医护服务工单信息
     *
     * @param id 医护服务工单主键
     * @return 结果
     */
    public int deleteMedicalServiceWorkOrderById(Long id);
}

