package com.ruoyi.custom.admin.storiedBuilding.mapper;

import java.util.List;

import com.ruoyi.custom.admin.storiedBuilding.domain.TypeVersionInfo;
import org.apache.ibatis.annotations.Param;

/**
 * 房间类型版本Mapper接口
 *
 * <AUTHOR>
 * @date 2022-04-01
 */
public interface TypeVersionInfoMapper {
    /**
     * 查询房间类型版本
     *
     * @param id 房间类型版本主键
     * @return 房间类型版本
     */
    public TypeVersionInfo selectTypeVersionInfoById(Long id);

    public TypeVersionInfo getVersionInfo(@Param(value = "typeId") String typeId, @Param(value = "status") String status);

    /**
     * 查询房间类型版本列表
     *
     * @param TypeVersionInfo 房间类型版本
     * @return 房间类型版本集合
     */
    public List<TypeVersionInfo> selectTypeVersionInfoList(TypeVersionInfo TypeVersionInfo);

    /**
     * 新增房间类型版本
     *
     * @param TypeVersionInfo 房间类型版本
     * @return 结果
     */
    public int insertTypeVersionInfo(TypeVersionInfo TypeVersionInfo);

    /**
     * 修改房间类型版本
     *
     * @param TypeVersionInfo 房间类型版本
     * @return 结果
     */
    public int updateTypeVersionInfo(TypeVersionInfo TypeVersionInfo);

    /**
     * 删除房间类型版本
     *
     * @param id 房间类型版本主键
     * @return 结果
     */
    public int deleteTypeVersionInfoById(Long id);

    /**
     * 批量删除房间类型版本
     *
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteTypeVersionInfoByIds(Long[] ids);
}
