package com.ibms.service.realty.web.controller.itemRegistration;

import com.ibms.service.realty.web.domain.ItemRegistration;
import com.ibms.service.realty.web.service.ItemRegistrationService;
import com.ruoyi.common.core.utils.poi.ExcelUtil;
import com.ruoyi.common.core.web.controller.BaseController;
import com.ruoyi.common.core.web.domain.AjaxResult;
import com.ruoyi.common.core.web.page.TableDataInfo;
import com.ruoyi.common.log.annotation.Log;
import com.ruoyi.common.log.enums.BusinessType;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.List;


/**
 * 物品出入登记Controller
 *
 * <AUTHOR>
 * @date 2023-05-29
 */
@RestController
@RequestMapping("/registration")
@Api(tags = "物品出入登记")
public class ItemRegistrationController extends BaseController {
    @Autowired
    private ItemRegistrationService itemRegistrationService;

    /**
     * 查询物品出入登记列表
     */
    @GetMapping("/list")
    @ApiOperation(value = "查询物品出入登记列表", notes = "查询物品出入登记列表")
    public TableDataInfo list(ItemRegistration itemRegistration) {
        startPage();
        List<ItemRegistration> list = itemRegistrationService.selectItemRegistrationList(itemRegistration);
        return getDataTable(list);
    }

    /**
     * 导出物品出入登记列表
     */
    @Log(title = "物品出入登记", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    @ApiOperation(value = "导出物品出入登记列表", notes = "导出物品出入登记列表")
    public void export(HttpServletResponse response, ItemRegistration itemRegistration) {
        List<ItemRegistration> list = itemRegistrationService.selectItemRegistrationList(itemRegistration);
        ExcelUtil<ItemRegistration> util = new ExcelUtil<ItemRegistration>(ItemRegistration.class);
        util.exportExcel(response, list, "物品出入登记数据");
    }

    /**
     * 获取物品出入登记详细信息
     */
    @GetMapping(value = "/{id}")
    @ApiOperation(value = "获取物品出入登记详细信息", notes = "获取物品出入登记详细信息")
    public AjaxResult getInfo(@PathVariable("id") Integer id) {
        return AjaxResult.success(itemRegistrationService.selectItemRegistrationById(id));
    }

    /**
     * 新增物品出入登记
     */
    @Log(title = "物品出入登记", businessType = BusinessType.INSERT)
    @PostMapping
    @ApiOperation(value = "新增物品出入登记", notes = "新增物品出入登记")
    public AjaxResult add(@RequestBody ItemRegistration itemRegistration) {
        return toAjax(itemRegistrationService.insertItemRegistration(itemRegistration));
    }

    /**
     * 修改物品出入登记
     */
    @Log(title = "物品出入登记", businessType = BusinessType.UPDATE)
    @PutMapping
    @ApiOperation(value = "修改物品出入登记", notes = "修改物品出入登记")
    public AjaxResult edit(@RequestBody ItemRegistration itemRegistration) {
        return toAjax(itemRegistrationService.updateItemRegistration(itemRegistration));
    }

    /**
     * 删除物品出入登记
     */
    @Log(title = "物品出入登记", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    @ApiOperation(value = "删除物品出入登记", notes = "删除物品出入登记")
    public AjaxResult remove(@PathVariable Integer[] ids) {
        return toAjax(itemRegistrationService.deleteItemRegistrationByIds(ids));
    }
}
