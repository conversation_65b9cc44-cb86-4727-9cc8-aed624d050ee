package com.ibms.service.realty.web.controller.maintenanceApplication;

import com.ibms.service.realty.web.domain.MaintenanceApplication;
import com.ibms.service.realty.web.service.MaintenanceApplicationService;
import com.ruoyi.common.core.utils.poi.ExcelUtil;
import com.ruoyi.common.core.web.controller.BaseController;
import com.ruoyi.common.core.web.domain.AjaxResult;
import com.ruoyi.common.core.web.page.TableDataInfo;
import com.ruoyi.common.log.annotation.Log;
import com.ruoyi.common.log.enums.BusinessType;
import com.ruoyi.common.security.utils.SecurityUtils;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 维保申请Controller
 *
 * <AUTHOR>
 * @date 2023-07-11
 */
@RestController
@RequestMapping("/maintenanceApplication")
@Api(tags = "维保申请")
public class MaintenanceApplicationController extends BaseController {
    @Autowired
    private MaintenanceApplicationService maintenanceApplicationService;

    /**
     * 查询维保申请列表
     */
    @GetMapping("/list")
    @ApiOperation("查询维保申请列表")
    public TableDataInfo list(MaintenanceApplication maintenanceApplication) {
        startPage();
        List<MaintenanceApplication> list = maintenanceApplicationService.selectMaintenanceApplicationList(maintenanceApplication);
        return getDataTable(list);
    }

    /**
     * 导出维保申请列表
     */
    @Log(title = "维保申请", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    @ApiOperation("导出维保申请列表")
    public void export(HttpServletResponse response, MaintenanceApplication maintenanceApplication) {
        List<MaintenanceApplication> list = maintenanceApplicationService.selectMaintenanceApplicationList(maintenanceApplication);
        ExcelUtil<MaintenanceApplication> util = new ExcelUtil<MaintenanceApplication>(MaintenanceApplication.class);
        util.exportExcel(response, list, "维保申请数据");
    }

    /**
     * 获取维保申请详细信息
     */
    @GetMapping(value = "/{id}")
    @ApiOperation("获取维保申请详细信息")
    public AjaxResult getInfo(@PathVariable("id") Integer id) {
        return AjaxResult.success(maintenanceApplicationService.selectMaintenanceApplicationById(id));
    }

    /**
     * 新增维保申请
     */
    @Log(title = "维保申请", businessType = BusinessType.INSERT)
    @PostMapping
    @ApiOperation("新增维保申请")
    public AjaxResult add(@RequestBody MaintenanceApplication maintenanceApplication) {
        return toAjax(maintenanceApplicationService.insertMaintenanceApplication(maintenanceApplication));
    }

    /**
     * 修改维保申请
     */
    @Log(title = "维保申请", businessType = BusinessType.UPDATE)
    @PutMapping
    @ApiOperation("修改维保申请")
    public AjaxResult edit(@RequestBody MaintenanceApplication maintenanceApplication) {
        return toAjax(maintenanceApplicationService.updateMaintenanceApplication(maintenanceApplication));
    }

    /**
     * 删除维保申请
     */
    @Log(title = "维保申请", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    @ApiOperation("删除维保申请")
    public AjaxResult remove(@PathVariable Integer[] ids) {
        return toAjax(maintenanceApplicationService.deleteMaintenanceApplicationByIds(ids));
    }

    /**
     * 审核，暂定超级管理员可审核
     */
    // TODO 未完成：未来需要加上专门的审核角色
    @Log(title = "审核维保申请", businessType = BusinessType.UPDATE)
    @PutMapping("/audit")
    @ApiOperation("审核维保申请")
    public AjaxResult audit(@RequestBody MaintenanceApplication maintenanceApplication) {
        // 查看当前用户是否为超级管理员
        if (!SecurityUtils.isAdmin(SecurityUtils.getUserId())) {
            return AjaxResult.error("当前用户不是超级管理员，无法审核");
        }
        return toAjax(maintenanceApplicationService.audit(maintenanceApplication));
    }

    /**
     * 还车
     */
    @Log(title = "还车", businessType = BusinessType.UPDATE)
    @PutMapping("/return")
    @ApiOperation("还车")
    public AjaxResult returnCar(@RequestBody MaintenanceApplication maintenanceApplication) {
        return toAjax(maintenanceApplicationService.returnCar(maintenanceApplication));
    }
}
