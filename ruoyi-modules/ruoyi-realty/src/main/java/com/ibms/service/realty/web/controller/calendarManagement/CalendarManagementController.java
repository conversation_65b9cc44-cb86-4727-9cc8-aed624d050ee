package com.ibms.service.realty.web.controller.calendarManagement;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.ibms.service.realty.web.domain.CalendarManagement;
import com.ibms.service.realty.web.domain.SchedulingGroup;
import com.ibms.service.realty.web.service.CalendarManagementService;
import com.ibms.service.realty.web.service.SchedulingGroupService;
import com.ruoyi.common.core.utils.poi.ExcelUtil;
import com.ruoyi.common.core.web.controller.BaseController;
import com.ruoyi.common.core.web.domain.AjaxResult;
import com.ruoyi.common.core.web.page.TableDataInfo;
import com.ruoyi.common.log.annotation.Log;
import com.ruoyi.common.log.enums.BusinessType;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 日历管理Controller
 *
 * <AUTHOR>
 * @date 2023-04-17
 */
@RestController
@RequestMapping("/holidayManagement")
@Api(tags = "日历管理")
public class CalendarManagementController extends BaseController {
    @Autowired
    private CalendarManagementService calendarManagementService;
    @Autowired
    private SchedulingGroupService schedulingGroupService;

    /**
     * 查询日历管理列表
     */
    @GetMapping("/list")
    @ApiOperation("查询日历管理列表")
    public TableDataInfo list(CalendarManagement calendarManagement) {
        startPage();
        List<CalendarManagement> list = calendarManagementService.selectCalendarManagementList(calendarManagement);
        return getDataTable(list);
    }

    /**
     * 导出日历管理列表
     */
    @Log(title = "日历管理", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    @ApiOperation("导出日历管理列表")
    public void export(HttpServletResponse response, CalendarManagement calendarManagement) {
        List<CalendarManagement> list = calendarManagementService.selectCalendarManagementList(calendarManagement);
        ExcelUtil<CalendarManagement> util = new ExcelUtil<CalendarManagement>(CalendarManagement.class);
        util.exportExcel(response, list, "日历管理数据");
    }

    /**
     * 获取日历管理详细信息
     */
    @GetMapping(value = "/{id}")
    @ApiOperation("获取日历管理详细信息")
    public AjaxResult getInfo(@PathVariable("id") Integer id) {
        return AjaxResult.success(calendarManagementService.selectCalendarManagementById(id));
    }

    /**
     * 新增日历管理
     */
    @Log(title = "日历管理", businessType = BusinessType.INSERT)
    @PostMapping
    @ApiOperation("新增日历管理")
    public AjaxResult add(@RequestBody CalendarManagement calendarManagement) {
        return toAjax(calendarManagementService.insertCalendarManagement(calendarManagement));
    }

    /**
     * 修改日历管理
     */
    @Log(title = "日历管理", businessType = BusinessType.UPDATE)
    @PutMapping
    @ApiOperation("修改日历管理")
    public AjaxResult edit(@RequestBody CalendarManagement calendarManagement) {
        return toAjax(calendarManagementService.updateCalendarManagement(calendarManagement));
    }

    /**
     * 删除日历管理
     */
    @Log(title = "日历管理", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Integer[] ids) {
        // 检查是否有关联数据
        QueryWrapper<SchedulingGroup> queryWrapper = new QueryWrapper<>();
        queryWrapper.in("calendar_id", ids);
        Long count = schedulingGroupService.count(queryWrapper);
        if (count > 0) {
            return AjaxResult.error("该日历已被排班组关联，无法删除");
        }
        return toAjax(calendarManagementService.deleteCalendarManagementByIds(ids));
    }
}
