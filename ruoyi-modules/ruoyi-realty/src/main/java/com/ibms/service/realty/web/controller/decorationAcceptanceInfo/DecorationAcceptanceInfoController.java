package com.ibms.service.realty.web.controller.decorationAcceptanceInfo;


import com.ibms.service.realty.web.domain.DecorationAcceptanceInfo;
import com.ibms.service.realty.web.service.DecorationAcceptanceInfoService;
import com.ruoyi.common.core.web.controller.BaseController;
import com.ruoyi.common.core.web.domain.AjaxResult;
import com.ruoyi.common.core.web.page.TableDataInfo;
import com.ruoyi.common.log.annotation.Log;
import com.ruoyi.common.log.enums.BusinessType;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;


/**
 * 验收申请信息Controller  注意！！！ 这里用的表是t_decoration_application_info和DecorationApplicationInfoController一样！！！！！
 *
 * <AUTHOR>
 * @date 2023-04-13
 */
@RestController
@RequestMapping("/decorationAcceptanceInfo")
@Api(tags = "验收申请信息")
public class DecorationAcceptanceInfoController extends BaseController {
    @Autowired
    private DecorationAcceptanceInfoService decorationAcceptanceInfoService;

    /**
     * 查询验收申请信息列表
     */
    @GetMapping("/list")
    @ApiOperation(value = "查询验收申请信息列表", notes = "查询验收申请信息列表", httpMethod = "GET")
    public TableDataInfo list(DecorationAcceptanceInfo decorationAcceptanceInfo) {
        startPage();
        List<DecorationAcceptanceInfo> list = decorationAcceptanceInfoService.selectDecorationAcceptanceInfoList(decorationAcceptanceInfo);
        return getDataTable(list);
    }

    /**
     * 获取验收申请信息详细信息
     */
    @GetMapping(value = "/{applyId}")
    @ApiOperation(value = "获取验收申请信息详细信息", notes = "获取验收申请信息详细信息", httpMethod = "GET")
    public AjaxResult getInfo(@PathVariable("applyId") String applyId) {
        return AjaxResult.success(decorationAcceptanceInfoService.selectDecorationAcceptanceInfoByApplyId(applyId));
    }

    /**
     * 修改验收申请信息
     */
    @Log(title = "验收申请信息", businessType = BusinessType.UPDATE)
    @PutMapping
    @ApiOperation(value = "修改验收申请信息", notes = "修改验收申请信息", httpMethod = "PUT")
    public AjaxResult edit(@RequestBody DecorationAcceptanceInfo decorationAcceptanceInfo) {
        return toAjax(decorationAcceptanceInfoService.updateDecorationAcceptanceInfo(decorationAcceptanceInfo));
    }

    /**
     * 重置不合格状态及相关数据
     */
    @PutMapping("/reset")
    @ApiOperation(value = "重置不合格状态", notes = "重置不合格状态", httpMethod = "PUT")
    public AjaxResult reset(@RequestBody DecorationAcceptanceInfo decorationAcceptanceInfo) {
        return toAjax(decorationAcceptanceInfoService.reset(decorationAcceptanceInfo));
    }


    /**
     * 删除验收申请信息
     */
    @Log(title = "验收申请信息", businessType = BusinessType.DELETE)
    @DeleteMapping("/{applyIds}")
    @ApiOperation(value = "删除验收申请信息", notes = "删除验收申请信息", httpMethod = "DELETE")
    public AjaxResult remove(@PathVariable String[] applyIds) {
        return toAjax(decorationAcceptanceInfoService.deleteDecorationAcceptanceInfoByApplyIds(applyIds));
    }
}

