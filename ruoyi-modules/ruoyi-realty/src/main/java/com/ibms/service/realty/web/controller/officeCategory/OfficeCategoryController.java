package com.ibms.service.realty.web.controller.officeCategory;

import com.ibms.service.realty.web.domain.OfficeCategory;
import com.ibms.service.realty.web.service.OfficeCategoryService;
import com.ruoyi.common.core.utils.poi.ExcelUtil;
import com.ruoyi.common.core.web.controller.BaseController;
import com.ruoyi.common.core.web.domain.AjaxResult;
import com.ruoyi.common.core.web.page.TableDataInfo;
import com.ruoyi.common.log.annotation.Log;
import com.ruoyi.common.log.enums.BusinessType;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 办公文档-会议-计划类别管理Controller
 *
 * <AUTHOR>
 * @date 2023-07-10
 */
@RestController
@RequestMapping("/officeCategory")
@Api(tags = "办公文档-会议-计划类别管理Controller")
public class OfficeCategoryController extends BaseController {
    @Autowired
    private OfficeCategoryService officeCategoryService;

    /**
     * 查询办公文档类别管理列表
     */
    @GetMapping("/list")
    @ApiOperation("查询办公文档-会议-计划档别管理列表")
    public TableDataInfo list(OfficeCategory officeCategory) {
        startPage();
        List<OfficeCategory> list = officeCategoryService.selectOfficeCategoryList(officeCategory);
        return getDataTable(list);
    }

    /**
     * 导出办公文档类别管理列表
     */
    @Log(title = "办公文档-会议-计划类别管理", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    @ApiOperation("导出办公文档-会议-计划类别管理列表")
    public void export(HttpServletResponse response, OfficeCategory officeCategory) {
        List<OfficeCategory> list = officeCategoryService.selectOfficeCategoryList(officeCategory);
        ExcelUtil<OfficeCategory> util = new ExcelUtil<OfficeCategory>(OfficeCategory.class);
        util.exportExcel(response, list, "办公文档类别管理数据");
    }

    /**
     * 获取办公文档类别管理详细信息
     */
    @GetMapping(value = "/{id}")
    @ApiOperation("获取办公文档-会议-计划类别管理详细信息")
    public AjaxResult getInfo(@PathVariable("id") Integer id) {
        return AjaxResult.success(officeCategoryService.selectOfficeCategoryById(id));
    }

    /**
     * 新增办公文档类别管理
     */
    @Log(title = "办公文档-会议-计划类别管理", businessType = BusinessType.INSERT)
    @PostMapping
    @ApiOperation("新增办公文档-会议-计划类别管理")
    public AjaxResult add(@RequestBody OfficeCategory officeCategory) {
        return toAjax(officeCategoryService.insertOfficeCategory(officeCategory));
    }

    /**
     * 修改办公文档类别管理
     */
    @Log(title = "办公文档类/会议/计划别管理", businessType = BusinessType.UPDATE)
    @PutMapping
    @ApiOperation("修改办公文档-会议-计划类别管理")
    public AjaxResult edit(@RequestBody OfficeCategory officeCategory) {
        return toAjax(officeCategoryService.updateOfficeCategory(officeCategory));
    }

    /**
     * 删除办公文档类别管理
     */
    @Log(title = "办公文档类/会议/计划别管理", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    @ApiOperation("删除办公文档-会议-计划类别管理")
    public AjaxResult remove(@PathVariable Integer[] ids) {
        return toAjax(officeCategoryService.deleteOfficeCategoryByIds(ids));
    }
}
