package com.ibms.service.realty;

import com.ruoyi.common.security.annotation.EnableCustomConfig;
import com.ruoyi.common.security.annotation.EnableRyFeignClients;
import com.ruoyi.common.swagger.annotation.EnableCustomSwagger2;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.scheduling.annotation.EnableScheduling;

@EnableCustomConfig
@EnableCustomSwagger2
@EnableRyFeignClients
@SpringBootApplication(
        scanBasePackages = {"com.ibms.service.realty", "com.ruoyi"}
)
@MapperScan({"com.ibms.service.realty.web.mapper.**"})
@EnableScheduling
public class RealtyApplication {

    public static void main(String[] args) {

        SpringApplication.run(RealtyApplication.class, args);

        System.out.println("(♥◠‿◠)ﾉﾞ  物业启动成功   ლ(´ڡ`ლ)ﾞ  \n" +
                "  _____  ____       _ \n" +
                " |  __ \\|  _ \\     | |\n" +
                " | |  | | |_) |    | |\n" +
                " | |  | |  _ < _   | |\n" +
                " | |__| | |_) | |__| |\n" +
                " |_____/|____/ \\____/ \n" +
                "                       \n"
        );
    }

}
