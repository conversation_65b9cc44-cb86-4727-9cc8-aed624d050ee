package com.ibms.service.realty.web.controller.patrolSchedule;


import com.ibms.service.realty.web.controller.patrolSchedule.param.ScheduleSignParam;
import com.ibms.service.realty.web.domain.PatrolSchedule;
import com.ibms.service.realty.web.domain.PatrolTaskOperationRecord;
import com.ibms.service.realty.web.service.PatrolScheduleService;
import com.ibms.service.realty.web.service.PatrolTaskOperationRecordService;
import com.ruoyi.common.core.constant.SecurityConstants;
import com.ruoyi.common.core.utils.poi.ExcelUtil;
import com.ruoyi.common.core.web.controller.BaseController;
import com.ruoyi.common.core.web.domain.AjaxResult;
import com.ruoyi.common.core.web.page.TableDataInfo;
import com.ruoyi.common.log.annotation.Log;
import com.ruoyi.common.log.enums.BusinessType;
import com.ruoyi.system.api.RemoteUserService;
import com.ruoyi.system.api.domain.SysUser;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.Date;
import java.util.List;

/**
 * 巡更任务Controller
 *
 * <AUTHOR>
 * @date 2023-04-11
 */
@RestController
@RequestMapping("/patrolSchedule")
@Api(tags = "巡更任务Controller")
public class PatrolScheduleController extends BaseController {
    @Autowired
    private PatrolScheduleService patrolScheduleService;

    @Autowired
    private PatrolTaskOperationRecordService patrolTaskOperationRecordService;

    @Autowired
    private RemoteUserService remoteUserService;

    /**
     * 查询巡更任务列表
     */
    @GetMapping("/list")
    @ApiOperation(value = "查询巡更任务列表")
    public TableDataInfo list(PatrolSchedule patrolSchedule) {
        startPage();
        List<PatrolSchedule> list = patrolScheduleService.selectPatrolScheduleList(patrolSchedule);
        return getDataTable(list);
    }

    /**
     * 导出巡更任务列表
     */
    @Log(title = "巡更任务", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    @ApiOperation(value = "导出巡更任务列表")
    public void export(HttpServletResponse response, PatrolSchedule patrolSchedule) {
        List<PatrolSchedule> list = patrolScheduleService.selectPatrolScheduleList(patrolSchedule);
        ExcelUtil<PatrolSchedule> util = new ExcelUtil<PatrolSchedule>(PatrolSchedule.class);
        util.exportExcel(response, list, "巡更任务数据");
    }

    /**
     * 获取巡更任务详细信息
     */
    @GetMapping(value = "/{taskId}")
    @ApiOperation(value = "获取巡更任务详细信息")
    public AjaxResult getInfo(@PathVariable("taskId") Integer taskId) {
        return AjaxResult.success(patrolScheduleService.selectPatrolScheduleByTaskId(taskId));
    }

    @PostMapping("/signImgAdd")
    @ApiOperation(value = "签到图片上传")
    public AjaxResult signImgAdd(@RequestBody ScheduleSignParam scheduleSignParam) {
        return toAjax(patrolScheduleService.signImgAdd(scheduleSignParam));
    }


    /**
     * 新增巡更任务
     */
    @Log(title = "巡更任务", businessType = BusinessType.INSERT)
    @PostMapping
    @ApiOperation(value = "新增巡更任务")
    public AjaxResult add(@RequestBody PatrolSchedule patrolSchedule) {
        patrolScheduleService.insertPatrolSchedule(patrolSchedule);
        if (patrolSchedule.getTaskId() == null) {
            return AjaxResult.error("新增巡更任务失败");
        }
        // 获取用户
        Long userid = Long.parseLong(patrolSchedule.getPatroller());
        SysUser sysUser = remoteUserService.getUserInfoById(userid, SecurityConstants.INNER);
        // 插日志：系统创建任务,将任务法分配给某人
        PatrolTaskOperationRecord patrolTaskOperationRecord = new PatrolTaskOperationRecord();
        patrolTaskOperationRecord.setTaskId(patrolSchedule.getTaskId());
        patrolTaskOperationRecord.setCreatTime(new Date());
        patrolTaskOperationRecord.setContent("系统创建任务,将任务法分配给" + sysUser.getNickName());
        patrolTaskOperationRecordService.insertPatrolTaskOperationRecord(patrolTaskOperationRecord);
        return AjaxResult.success();
    }

    /**
     * 修改巡更任务
     */
    @Log(title = "巡更任务", businessType = BusinessType.UPDATE)
    @PutMapping
    @ApiOperation(value = "修改巡更任务")
    public AjaxResult edit(@RequestBody PatrolSchedule patrolSchedule) {

        // 查询原来的任务信息
        PatrolSchedule patrolSchedule1 = patrolScheduleService.selectPatrolScheduleByTaskId(patrolSchedule.getTaskId());

        int r = patrolScheduleService.updatePatrolSchedule(patrolSchedule);
        if (r == 0) {
            return AjaxResult.error("修改巡更任务失败");
        }

//        PatrolTaskOperationRecord patrolTaskOperationRecord = new PatrolTaskOperationRecord();
//        //比较原来的任务信息和现在的任务信息，比较每一个字段是否一致，不一致根据字段名称插入相关日志
//        if (patrolSchedule1.getStatus() != patrolSchedule.getStatus()) {
//            //插日志：任务状态由xxx变更为xxx
//            patrolTaskOperationRecord.setTaskId(patrolSchedule.getTaskId());
//            patrolTaskOperationRecord.setCreatTime(new Date());
//            patrolTaskOperationRecord.setContent("任务状态变更为" + TaskStatus.valueOf(patrolSchedule.getStatus()).getDescription());
//
//        }
//        if (patrolSchedule1.getCurrentPointId() != patrolSchedule.getCurrentPointId()) {
//            //插日志：当前巡更点位是xxx
//            patrolTaskOperationRecord.setTaskId(patrolSchedule.getTaskId());
//            patrolTaskOperationRecord.setCreatTime(new Date());
//            patrolTaskOperationRecord.setContent("当前巡更点位是" + patrolSchedule.getCurrentPointId());
//        }
//        if ((patrolSchedule1.getRemark() == null && patrolSchedule.getRemark() != null)
//                || (patrolSchedule.getRemark() == null && patrolSchedule1.getRemark() != null)
//                || (patrolSchedule1.getRemark().equals(patrolSchedule.getRemark()))) {
//            //插日志：备注变更为xxx
//            patrolTaskOperationRecord.setTaskId(patrolSchedule.getTaskId());
//            patrolTaskOperationRecord.setCreatTime(new Date());
//            patrolTaskOperationRecord.setContent("备注变更为" + patrolSchedule.getRemark());
//        }
//        if (patrolTaskOperationRecord.getTaskId() != null) {
//            patrolTaskOperationRecordService.insertPatrolTaskOperationRecord(patrolTaskOperationRecord);
//        }
        return AjaxResult.success();
    }

    /**
     * 删除巡更任务
     */
    @Log(title = "巡更任务", businessType = BusinessType.DELETE)
    @DeleteMapping("/{taskIds}")
    @ApiOperation(value = "删除巡更任务")
    public AjaxResult remove(@PathVariable Integer[] taskIds) {
        return toAjax(patrolScheduleService.deletePatrolScheduleByTaskIds(taskIds));
    }

}
