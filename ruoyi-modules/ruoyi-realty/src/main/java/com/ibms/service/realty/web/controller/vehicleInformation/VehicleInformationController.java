package com.ibms.service.realty.web.controller.vehicleInformation;

import com.ibms.service.realty.web.controller.vehicleInformation.param.VehicleBrandParam;
import com.ibms.service.realty.web.controller.vehicleInformation.param.VehicleTypeParam;
import com.ibms.service.realty.web.domain.VehicleInformation;
import com.ibms.service.realty.web.service.VehicleInformationService;
import com.ruoyi.common.core.utils.poi.ExcelUtil;
import com.ruoyi.common.core.web.controller.BaseController;
import com.ruoyi.common.core.web.domain.AjaxResult;
import com.ruoyi.common.core.web.page.TableDataInfo;
import com.ruoyi.common.log.annotation.Log;
import com.ruoyi.common.log.enums.BusinessType;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 车辆信息Controller
 *
 * <AUTHOR>
 * @date 2023-07-11
 */
@RestController
@RequestMapping("/vehicleInformation")
@Api(tags = "车辆信息")
public class VehicleInformationController extends BaseController {
    @Autowired
    private VehicleInformationService vehicleInformationService;

    /**
     * 查询车辆信息列表
     */
    @GetMapping("/list")
    @ApiOperation("查询车辆信息列表")
    public TableDataInfo list(VehicleInformation vehicleInformation) {
        startPage();
        List<VehicleInformation> list = vehicleInformationService.selectVehicleInformationList(vehicleInformation);
        return getDataTable(list);
    }

    /**
     * 导出车辆信息列表
     */
    @Log(title = "车辆信息", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    @ApiOperation("导出车辆信息列表")
    public void export(HttpServletResponse response, VehicleInformation vehicleInformation) {
        List<VehicleInformation> list = vehicleInformationService.selectVehicleInformationList(vehicleInformation);
        ExcelUtil<VehicleInformation> util = new ExcelUtil<VehicleInformation>(VehicleInformation.class);
        util.exportExcel(response, list, "车辆信息数据");
    }

    /**
     * 获取车辆信息详细信息
     */
    @GetMapping(value = "/{id}")
    @ApiOperation("获取车辆信息详细信息")
    public AjaxResult getInfo(@PathVariable("id") Integer id) {
        return AjaxResult.success(vehicleInformationService.selectVehicleInformationById(id));
    }

    /**
     * 新增车辆信息
     */
    @Log(title = "车辆信息", businessType = BusinessType.INSERT)
    @PostMapping
    @ApiOperation("新增车辆信息")
    public AjaxResult add(@RequestBody VehicleInformation vehicleInformation) {
        return toAjax(vehicleInformationService.insertVehicleInformation(vehicleInformation));
    }

    /**
     * 修改车辆信息
     */
    @Log(title = "车辆信息", businessType = BusinessType.UPDATE)
    @PutMapping
    @ApiOperation("修改车辆信息")
    public AjaxResult edit(@RequestBody VehicleInformation vehicleInformation) {
        return toAjax(vehicleInformationService.updateVehicleInformation(vehicleInformation));
    }

    /**
     * 删除车辆信息
     */
    @Log(title = "车辆信息", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    @ApiOperation("删除车辆信息")
    public AjaxResult remove(@PathVariable Integer[] ids) {
        return toAjax(vehicleInformationService.deleteVehicleInformationByIds(ids));
    }

    /**
     * 获取车辆品牌列表
     */
    @GetMapping(value = "/vehicleBrand/List")
    @ApiOperation("获取车辆品牌列表")
    public AjaxResult vehicleBrandList() {
        return AjaxResult.success(vehicleInformationService.vehicleBrandList());
    }

    /**
     * 根据id获取车辆品牌
     */
    @GetMapping(value = "/vehicleBrand/{id}")
    @ApiOperation("根据id获取车辆品牌")
    public AjaxResult getVehicleBrandById(@PathVariable Integer id) {
        return AjaxResult.success(vehicleInformationService.getVehicleBrandById(id));
    }

    /**
     * 根据id删除车辆品牌
     */
    @DeleteMapping(value = "/vehicleBrand/{id}")
    @ApiOperation("根据id删除车辆品牌")
    public AjaxResult deleteVehicleBrandById(@PathVariable Integer id) {
        return toAjax(vehicleInformationService.deleteVehicleBrandById(id));
    }

    /**
     * 新增车辆品牌
     */
    @PostMapping(value = "/vehicleBrand")
    @ApiOperation("新增车辆品牌")
    public AjaxResult addVehicleBrand(@RequestBody VehicleBrandParam vehicleBrandParam) {
        return toAjax(vehicleInformationService.addVehicleBrand(vehicleBrandParam));
    }

    /**
     * 获取车辆类型列表
     */
    @GetMapping(value = "/vehicleType/List")
    @ApiOperation("获取车辆类型列表")
    public AjaxResult vehicleTypeList() {
        return AjaxResult.success(vehicleInformationService.vehicleTypeList());
    }

    /**
     * 根据id获取车辆类型
     */
    @GetMapping(value = "/vehicleType/{id}")
    @ApiOperation("根据id获取车辆类型")
    public AjaxResult getVehicleTypeById(@PathVariable Integer id) {
        return AjaxResult.success(vehicleInformationService.getVehicleTypeById(id));
    }

    /**
     * 根据id删除车辆类型
     */
    @DeleteMapping(value = "/vehicleType/{id}")
    @ApiOperation("根据id删除车辆类型")
    public AjaxResult deleteVehicleTypeById(@PathVariable Integer id) {
        return toAjax(vehicleInformationService.deleteVehicleTypeById(id));
    }

    /**
     * 新增车辆类型
     */
    @PostMapping(value = "/vehicleType")
    @ApiOperation("新增车辆类型")
    public AjaxResult addVehicleType(@RequestBody VehicleTypeParam vehicleTypeParam) {
        return toAjax(vehicleInformationService.addVehicleType(vehicleTypeParam));
    }
}
