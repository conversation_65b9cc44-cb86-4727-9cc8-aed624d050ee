package com.ibms.service.realty.web.controller.cleaningRegionInfo;


import cn.hutool.core.io.FileUtil;
import cn.hutool.core.util.RandomUtil;
import cn.hutool.extra.qrcode.QrCodeUtil;
import com.ibms.service.realty.web.domain.CleaningRegionInfo;
import com.ibms.service.realty.web.service.CleaningRegionInfoService;
import com.ruoyi.common.core.utils.poi.ExcelUtil;
import com.ruoyi.common.core.web.controller.BaseController;
import com.ruoyi.common.core.web.domain.AjaxResult;
import com.ruoyi.common.core.web.page.TableDataInfo;
import com.ruoyi.common.log.annotation.Log;
import com.ruoyi.common.log.enums.BusinessType;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.time.LocalDate;
import java.util.HashMap;
import java.util.List;
import java.util.Map;


/**
 * 保洁区域信息Controller
 *
 * <AUTHOR>
 * @date 2023-04-12
 */
@RestController
@RequestMapping("/cleanInfo")
@Api(tags = "保洁区域信息")
public class CleaningRegionInfoController extends BaseController {
    /**
     * 资源映射路径 前缀
     */
    @Value("${file.prefix}")
    public String localFilePrefix;
    /**
     * 域名或本机访问地址
     */
    @Value("${file.domain}")
    public String domain;
    @Autowired
    private CleaningRegionInfoService cleaningRegionInfoService;
    /**
     * 上传文件存储在本地的根路径
     */
    @Value("${file.path}")
    private String localFilePath;

    /**
     * 查询保洁区域信息列表
     */
    @GetMapping("/list")
    @ApiOperation("查询保洁区域信息列表")
    public TableDataInfo list(CleaningRegionInfo cleaningRegionInfo) {
        startPage();
        List<CleaningRegionInfo> list = cleaningRegionInfoService.selectCleaningRegionInfoList(cleaningRegionInfo);
        return getDataTable(list);
    }

    /**
     * 导出保洁区域信息列表
     */
    @Log(title = "保洁区域信息", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    @ApiOperation("导出保洁区域信息列表")
    public void export(HttpServletResponse response, CleaningRegionInfo cleaningRegionInfo) {
        List<CleaningRegionInfo> list = cleaningRegionInfoService.selectCleaningRegionInfoList(cleaningRegionInfo);
        ExcelUtil<CleaningRegionInfo> util = new ExcelUtil<CleaningRegionInfo>(CleaningRegionInfo.class);
        util.exportExcel(response, list, "保洁区域信息数据");
    }

    /**
     * 获取保洁区域信息详细信息
     */
    @GetMapping(value = "/{id}")
    @ApiOperation("获取保洁区域信息详细信息")
    public AjaxResult getInfo(@PathVariable("id") Integer id) {
        return AjaxResult.success(cleaningRegionInfoService.selectCleaningRegionInfoById(id));
    }

    /**
     * 新增保洁区域信息
     */
    @Log(title = "保洁区域信息", businessType = BusinessType.INSERT)
    @PostMapping
    @ApiOperation("新增保洁区域信息")
    public AjaxResult add(@RequestBody CleaningRegionInfo cleaningRegionInfo) {
        return toAjax(cleaningRegionInfoService.insertCleaningRegionInfo(cleaningRegionInfo));
    }

    /**
     * 修改保洁区域信息
     */
    @Log(title = "保洁区域信息", businessType = BusinessType.UPDATE)
    @PutMapping
    @ApiOperation("修改保洁区域信息")
    public AjaxResult edit(@RequestBody CleaningRegionInfo cleaningRegionInfo) {
        return toAjax(cleaningRegionInfoService.updateCleaningRegionInfo(cleaningRegionInfo));
    }

    /**
     * 删除保洁区域信息
     */
    @Log(title = "保洁区域信息", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    @ApiOperation("删除保洁区域信息")
    public AjaxResult remove(@PathVariable Integer[] ids) {
        return toAjax(cleaningRegionInfoService.deleteCleaningRegionInfoByIds(ids));
    }

    /**
     * 生成二维码
     */
    @ApiOperation("生成二维码")
    @GetMapping(value = "/createQRCode")
    public AjaxResult createQRCode(String id) {
        LocalDate now = LocalDate.now();
        String fileName = System.currentTimeMillis() + RandomUtil.randomInt(0, 100) + ".jpg";
        String savePath = localFilePath + "/" + now.getYear() + "/" + now.getMonthValue() + "/" + now.getDayOfMonth() + "/" + fileName;
        String returnUrl = domain + localFilePrefix + "/" + now.getYear() + "/" + now.getMonthValue() + "/" + now.getDayOfMonth() + "/" + fileName;
        FileUtil.mkdir(savePath.substring(0, savePath.lastIndexOf("/")));
        QrCodeUtil.generate(id, 300, 300, FileUtil.file(savePath));
        Map data = new HashMap();
        data.put("returnUrl", returnUrl);
        return AjaxResult.success("生成二维码成功", data);
    }
}
