package com.ibms.service.realty.web.controller.decorationProblemRecord;


import com.ibms.service.realty.web.domain.DecorationProblemRecord;
import com.ibms.service.realty.web.service.DecorationProblemRecordService;
import com.ruoyi.common.core.utils.poi.ExcelUtil;
import com.ruoyi.common.core.web.controller.BaseController;
import com.ruoyi.common.core.web.domain.AjaxResult;
import com.ruoyi.common.core.web.page.TableDataInfo;
import com.ruoyi.common.log.annotation.Log;
import com.ruoyi.common.log.enums.BusinessType;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.List;


/**
 * 装修巡检问题记录Controller
 *
 * <AUTHOR>
 * @date 2023-04-13
 */
@RestController
@RequestMapping("/decorationProblemRecord")
@Api(tags = "装修巡检问题记录")
public class DecorationProblemRecordController extends BaseController {
    @Autowired
    private DecorationProblemRecordService decorationProblemRecordService;

    /**
     * 查询装修巡检问题记录列表
     */
    @GetMapping("/list")
    @ApiOperation("查询装修巡检问题记录列表")
    public TableDataInfo list(DecorationProblemRecord decorationProblemRecord) {
        startPage();
        List<DecorationProblemRecord> list = decorationProblemRecordService.selectDecorationProblemRecordList(decorationProblemRecord);
        return getDataTable(list);
    }

    /**
     * 导出装修巡检问题记录列表
     */
    @Log(title = "装修巡检问题记录", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    @ApiOperation("导出装修巡检问题记录列表")
    public void export(HttpServletResponse response, DecorationProblemRecord decorationProblemRecord) {
        List<DecorationProblemRecord> list = decorationProblemRecordService.selectDecorationProblemRecordList(decorationProblemRecord);
        ExcelUtil<DecorationProblemRecord> util = new ExcelUtil<DecorationProblemRecord>(DecorationProblemRecord.class);
        util.exportExcel(response, list, "装修巡检问题记录数据");
    }

    /**
     * 获取装修巡检问题记录详细信息
     */
    @GetMapping(value = "/{id}")
    @ApiOperation("获取装修巡检问题记录详细信息")
    public AjaxResult getInfo(@PathVariable("id") Integer id) {
        return AjaxResult.success(decorationProblemRecordService.selectDecorationProblemRecordById(id));
    }

    /**
     * 新增装修巡检问题记录
     */
    @Log(title = "装修巡检问题记录", businessType = BusinessType.INSERT)
    @PostMapping
    @ApiOperation("新增装修巡检问题记录")
    public AjaxResult add(@RequestBody DecorationProblemRecord decorationProblemRecord) {
        return toAjax(decorationProblemRecordService.insertDecorationProblemRecord(decorationProblemRecord));
    }

    /**
     * 修改装修巡检问题记录
     */
    @Log(title = "装修巡检问题记录", businessType = BusinessType.UPDATE)
    @PutMapping
    @ApiOperation("修改装修巡检问题记录")
    public AjaxResult edit(@RequestBody DecorationProblemRecord decorationProblemRecord) {
        return toAjax(decorationProblemRecordService.updateDecorationProblemRecord(decorationProblemRecord));
    }

    /**
     * 删除装修巡检问题记录
     */
    @Log(title = "装修巡检问题记录", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    @ApiOperation("删除装修巡检问题记录")
    public AjaxResult remove(@PathVariable Integer[] ids) {
        return toAjax(decorationProblemRecordService.deleteDecorationProblemRecordByIds(ids));
    }

    /**
     * 确认
     */
    @Log(title = "确认", businessType = BusinessType.UPDATE)
    @PutMapping("/confirm")
    @ApiOperation("确认")
    public AjaxResult confirm(@RequestBody DecorationProblemRecord decorationProblemRecord) {
        return toAjax(decorationProblemRecordService.confirm(decorationProblemRecord));
    }

    @Log(title = "打印整改告知函", businessType = BusinessType.UPDATE)
    @PutMapping("/print")
    @ApiOperation("打印整改告知函")
    public AjaxResult print(@RequestBody DecorationProblemRecord decorationProblemRecord) {
        return toAjax(decorationProblemRecordService.print(decorationProblemRecord));
    }
}
