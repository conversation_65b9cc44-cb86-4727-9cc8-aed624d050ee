package com.ibms.service.realty.web.controller.publicInstrument.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
@ApiModel(value = "楼层和房间VO")
public class floorAndRoomVo {

    @ApiModelProperty(value = "楼层id")
    private Integer floorId;

    @ApiModelProperty(value = "楼层名称")
    private String floorName;

    @ApiModelProperty(value = "房间id")
    private Integer roomId;

    @ApiModelProperty(value = "房间名称")
    private String roomName;
}
