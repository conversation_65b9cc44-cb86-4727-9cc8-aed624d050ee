package com.ibms.service.realty.web.controller.decorationApplicationSettings;

import com.ibms.service.realty.web.domain.DecorationApplicationSettings;
import com.ibms.service.realty.web.service.DecorationApplicationSettingsService;
import com.ruoyi.common.core.utils.poi.ExcelUtil;
import com.ruoyi.common.core.web.controller.BaseController;
import com.ruoyi.common.core.web.domain.AjaxResult;
import com.ruoyi.common.core.web.page.TableDataInfo;
import com.ruoyi.common.log.annotation.Log;
import com.ruoyi.common.log.enums.BusinessType;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 装修申请设置Controller
 *
 * <AUTHOR>
 * @date 2023-06-13
 */
@RestController
@RequestMapping("/decorationApplicationSettings")
@Api(tags = "装修申请设置")
public class DecorationApplicationSettingsController extends BaseController {
    @Autowired
    private DecorationApplicationSettingsService decorationApplicationSettingsService;

    /**
     * 查询装修申请设置列表
     */
    @GetMapping("/list")
    @ApiOperation(value = "查询装修申请设置列表")
    public TableDataInfo list(DecorationApplicationSettings decorationApplicationSettings) {
        startPage();
        List<DecorationApplicationSettings> list = decorationApplicationSettingsService.selectDecorationApplicationSettingsList(decorationApplicationSettings);
        return getDataTable(list);
    }

    /**
     * 导出装修申请设置列表
     */
    @Log(title = "装修申请设置", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    @ApiOperation(value = "导出装修申请设置列表")
    public void export(HttpServletResponse response, DecorationApplicationSettings decorationApplicationSettings) {
        List<DecorationApplicationSettings> list = decorationApplicationSettingsService.selectDecorationApplicationSettingsList(decorationApplicationSettings);
        ExcelUtil<DecorationApplicationSettings> util = new ExcelUtil<DecorationApplicationSettings>(DecorationApplicationSettings.class);
        util.exportExcel(response, list, "装修申请设置数据");
    }

    /**
     * 获取装修申请设置详细信息
     */
    @GetMapping(value = "/{id}")
    @ApiOperation(value = "获取装修申请设置详细信息")
    public AjaxResult getInfo(@PathVariable("id") Integer id) {
        return AjaxResult.success(decorationApplicationSettingsService.selectDecorationApplicationSettingsById(id));
    }

    /**
     * 新增装修申请设置
     */
    @Log(title = "装修申请设置", businessType = BusinessType.INSERT)
    @PostMapping
    @ApiOperation(value = "新增装修申请设置")
    public AjaxResult add(@RequestBody DecorationApplicationSettings decorationApplicationSettings) {
        return toAjax(decorationApplicationSettingsService.insertDecorationApplicationSettings(decorationApplicationSettings));
    }

    /**
     * 修改装修申请设置
     */
    @Log(title = "装修申请设置", businessType = BusinessType.UPDATE)
    @PutMapping
    @ApiOperation(value = "修改装修申请设置")
    public AjaxResult edit(@RequestBody DecorationApplicationSettings decorationApplicationSettings) {
        return toAjax(decorationApplicationSettingsService.updateDecorationApplicationSettings(decorationApplicationSettings));
    }

    /**
     * 删除装修申请设置
     */
    @Log(title = "装修申请设置", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    @ApiOperation(value = "删除装修申请设置")
    public AjaxResult remove(@PathVariable Integer[] ids) {
        return toAjax(decorationApplicationSettingsService.deleteDecorationApplicationSettingsByIds(ids));
    }
}
