package com.ibms.service.realty.web.controller.officeMeetingRecord;

import com.ibms.service.realty.web.domain.OfficeMeetingRecord;
import com.ibms.service.realty.web.service.OfficeMeetingRecordService;
import com.ruoyi.common.core.utils.poi.ExcelUtil;
import com.ruoyi.common.core.web.controller.BaseController;
import com.ruoyi.common.core.web.domain.AjaxResult;
import com.ruoyi.common.core.web.page.TableDataInfo;
import com.ruoyi.common.log.annotation.Log;
import com.ruoyi.common.log.enums.BusinessType;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 办公会议记录Controller
 *
 * <AUTHOR>
 * @date 2023-07-10
 */
@RestController
@RequestMapping("/officeMeetingRecord")
@Api(tags = "办公会议记录")
public class OfficeMeetingRecordController extends BaseController {
    @Autowired
    private OfficeMeetingRecordService officeMeetingRecordService;

    /**
     * 查询办公会议记录列表
     */
    @GetMapping("/list")
    @ApiOperation("查询办公会议记录列表")
    public TableDataInfo list(OfficeMeetingRecord officeMeetingRecord) {
        startPage();
        List<OfficeMeetingRecord> list = officeMeetingRecordService.selectOfficeMeetingRecordList(officeMeetingRecord);
        return getDataTable(list);
    }

    /**
     * 导出办公会议记录列表
     */
    @Log(title = "办公会议记录", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    @ApiOperation("导出办公会议记录列表")
    public void export(HttpServletResponse response, OfficeMeetingRecord officeMeetingRecord) {
        List<OfficeMeetingRecord> list = officeMeetingRecordService.selectOfficeMeetingRecordList(officeMeetingRecord);
        ExcelUtil<OfficeMeetingRecord> util = new ExcelUtil<OfficeMeetingRecord>(OfficeMeetingRecord.class);
        util.exportExcel(response, list, "办公会议记录数据");
    }

    /**
     * 获取办公会议记录详细信息
     */
    @GetMapping(value = "/{id}")
    @ApiOperation("获取办公会议记录详细信息")
    public AjaxResult getInfo(@PathVariable("id") Integer id) {
        return AjaxResult.success(officeMeetingRecordService.selectOfficeMeetingRecordById(id));
    }

    /**
     * 新增办公会议记录
     */
    @Log(title = "办公会议记录", businessType = BusinessType.INSERT)
    @PostMapping
    @ApiOperation("新增办公会议记录")
    public AjaxResult add(@RequestBody OfficeMeetingRecord officeMeetingRecord) {
        return toAjax(officeMeetingRecordService.insertOfficeMeetingRecord(officeMeetingRecord));
    }

    /**
     * 修改办公会议记录
     */
    @Log(title = "办公会议记录", businessType = BusinessType.UPDATE)
    @PutMapping
    @ApiOperation("修改办公会议记录")
    public AjaxResult edit(@RequestBody OfficeMeetingRecord officeMeetingRecord) {
        return toAjax(officeMeetingRecordService.updateOfficeMeetingRecord(officeMeetingRecord));
    }

    /**
     * 删除办公会议记录
     */
    @Log(title = "办公会议记录", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    @ApiOperation("删除办公会议记录")
    public AjaxResult remove(@PathVariable Integer[] ids) {
        return toAjax(officeMeetingRecordService.deleteOfficeMeetingRecordByIds(ids));
    }
}
