package com.ibms.service.realty.web.controller.decorationApplicationInfo.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 装修申请和验收项目关系Vo
 *
 * <AUTHOR>
 * @date 2023-04-13
 */
@Data
@ApiModel(value = "装修申请和验收项目关系Vo", description = "装修申请和验收项目关系Vo")
public class DecorationApplicationRelationVo {

    @ApiModelProperty(value = "装修申请和验收项目关系id")
    private Integer id;

    @ApiModelProperty(value = "装修申请id")
    private Integer applicationId;

    @ApiModelProperty(value = "装修验收项目id")
    private Integer acceptId;

    @ApiModelProperty(value = "装修验收项目名称")
    private String acceptName;

    @ApiModelProperty(value = "状态，0：合格，1：不合格")
    private Integer status;
}
