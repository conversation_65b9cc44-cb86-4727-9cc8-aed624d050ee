package com.ibms.service.realty.web.controller.patrolProblemRecord;


import com.ibms.service.realty.web.domain.PatrolProblemRecord;
import com.ibms.service.realty.web.service.PatrolProblemRecordService;
import com.ruoyi.common.core.utils.poi.ExcelUtil;
import com.ruoyi.common.core.web.controller.BaseController;
import com.ruoyi.common.core.web.domain.AjaxResult;
import com.ruoyi.common.core.web.page.TableDataInfo;
import com.ruoyi.common.log.annotation.Log;
import com.ruoyi.common.log.enums.BusinessType;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 巡更问题记录Controller
 *
 * <AUTHOR>
 * @date 2023-04-12
 */
@RestController
@RequestMapping("/patrolProblemRecord")
@Api(tags = "巡更问题记录")
public class PatrolProblemRecordController extends BaseController {
    @Autowired
    private PatrolProblemRecordService patrolProblemRecordService;

    /**
     * 查询巡更问题记录列表
     */
    @GetMapping("/list")
    @ApiOperation("查询巡更问题记录列表")
    public TableDataInfo list(PatrolProblemRecord patrolProblemRecord) {
        startPage();
        List<PatrolProblemRecord> list = patrolProblemRecordService.selectPatrolProblemRecordList(patrolProblemRecord);
        return getDataTable(list);
    }

    /**
     * 导出巡更问题记录列表
     */
    @Log(title = "巡更问题记录", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    @ApiOperation("导出巡更问题记录列表")
    public void export(HttpServletResponse response, PatrolProblemRecord patrolProblemRecord) {
        List<PatrolProblemRecord> list = patrolProblemRecordService.selectPatrolProblemRecordList(patrolProblemRecord);
        ExcelUtil<PatrolProblemRecord> util = new ExcelUtil<PatrolProblemRecord>(PatrolProblemRecord.class);
        util.exportExcel(response, list, "巡更问题记录数据");
    }

    /**
     * 获取巡更问题记录详细信息
     */
    @GetMapping(value = "/{id}")
    @ApiOperation("获取巡更问题记录详细信息")
    public AjaxResult getInfo(@PathVariable("id") Integer id) {
        return AjaxResult.success(patrolProblemRecordService.selectPatrolProblemRecordById(id));
    }

    /**
     * 新增巡更问题记录
     */
    @Log(title = "巡更问题记录", businessType = BusinessType.INSERT)
    @PostMapping
    @ApiOperation("新增巡更问题记录")
    public AjaxResult add(@RequestBody PatrolProblemRecord patrolProblemRecord) {
        return toAjax(patrolProblemRecordService.insertPatrolProblemRecord(patrolProblemRecord));
    }

    /**
     * 修改巡更问题记录
     */
    @Log(title = "巡更问题记录", businessType = BusinessType.UPDATE)
    @PutMapping
    @ApiOperation("修改巡更问题记录")
    public AjaxResult edit(@RequestBody PatrolProblemRecord patrolProblemRecord) {
        return toAjax(patrolProblemRecordService.updatePatrolProblemRecord(patrolProblemRecord));
    }

    /**
     * 删除巡更问题记录
     */
    @Log(title = "巡更问题记录", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    @ApiOperation("删除巡更问题记录")
    public AjaxResult remove(@PathVariable Integer[] ids) {
        return toAjax(patrolProblemRecordService.deletePatrolProblemRecordByIds(ids));
    }
}

