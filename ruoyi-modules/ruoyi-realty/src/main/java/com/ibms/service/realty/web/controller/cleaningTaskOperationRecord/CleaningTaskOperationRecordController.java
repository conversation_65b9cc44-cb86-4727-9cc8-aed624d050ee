package com.ibms.service.realty.web.controller.cleaningTaskOperationRecord;


import com.ibms.service.realty.web.domain.CleaningTaskOperationRecord;
import com.ibms.service.realty.web.service.CleaningTaskOperationRecordService;
import com.ruoyi.common.core.utils.poi.ExcelUtil;
import com.ruoyi.common.core.web.controller.BaseController;
import com.ruoyi.common.core.web.domain.AjaxResult;
import com.ruoyi.common.core.web.page.TableDataInfo;
import com.ruoyi.common.log.annotation.Log;
import com.ruoyi.common.log.enums.BusinessType;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 保洁巡查任务操作记录Controller
 *
 * <AUTHOR>
 * @date 2023-04-12
 */
@RestController
@RequestMapping("/cleaningTaskOperationRecord")
@Api(tags = "保洁巡查任务操作记录")
public class CleaningTaskOperationRecordController extends BaseController {
    @Autowired
    private CleaningTaskOperationRecordService cleaningTaskOperationRecordService;

    /**
     * 查询保洁巡查任务操作记录列表
     */
    @GetMapping("/list")
    @ApiOperation("查询保洁巡查任务操作记录列表")
    public TableDataInfo list(CleaningTaskOperationRecord cleaningTaskOperationRecord) {
        startPage();
        List<CleaningTaskOperationRecord> list = cleaningTaskOperationRecordService.selectCleaningTaskOperationRecordList(cleaningTaskOperationRecord);
        return getDataTable(list);
    }

    /**
     * 导出保洁巡查任务操作记录列表
     */
    @Log(title = "保洁巡查任务操作记录", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    @ApiOperation("导出保洁巡查任务操作记录列表")
    public void export(HttpServletResponse response, CleaningTaskOperationRecord cleaningTaskOperationRecord) {
        List<CleaningTaskOperationRecord> list = cleaningTaskOperationRecordService.selectCleaningTaskOperationRecordList(cleaningTaskOperationRecord);
        ExcelUtil<CleaningTaskOperationRecord> util = new ExcelUtil<CleaningTaskOperationRecord>(CleaningTaskOperationRecord.class);
        util.exportExcel(response, list, "保洁巡查任务操作记录数据");
    }

    /**
     * 获取保洁巡查任务操作记录详细信息
     */
    @GetMapping(value = "/{id}")
    @ApiOperation("获取保洁巡查任务操作记录详细信息")
    public AjaxResult getInfo(@PathVariable("id") Integer id) {
        return AjaxResult.success(cleaningTaskOperationRecordService.selectCleaningTaskOperationRecordById(id));
    }

    /**
     * 新增保洁巡查任务操作记录
     */
    @Log(title = "保洁巡查任务操作记录", businessType = BusinessType.INSERT)
    @PostMapping
    @ApiOperation("新增保洁巡查任务操作记录")
    public AjaxResult add(@RequestBody CleaningTaskOperationRecord cleaningTaskOperationRecord) {
        return toAjax(cleaningTaskOperationRecordService.insertCleaningTaskOperationRecord(cleaningTaskOperationRecord));
    }

    /**
     * 修改保洁巡查任务操作记录
     */
    @Log(title = "保洁巡查任务操作记录", businessType = BusinessType.UPDATE)
    @PutMapping
    @ApiOperation("修改保洁巡查任务操作记录")
    public AjaxResult edit(@RequestBody CleaningTaskOperationRecord cleaningTaskOperationRecord) {
        return toAjax(cleaningTaskOperationRecordService.updateCleaningTaskOperationRecord(cleaningTaskOperationRecord));
    }

    /**
     * 删除保洁巡查任务操作记录
     */
    @Log(title = "保洁巡查任务操作记录", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    @ApiOperation("删除保洁巡查任务操作记录")
    public AjaxResult remove(@PathVariable Integer[] ids) {
        return toAjax(cleaningTaskOperationRecordService.deleteCleaningTaskOperationRecordByIds(ids));
    }
}
