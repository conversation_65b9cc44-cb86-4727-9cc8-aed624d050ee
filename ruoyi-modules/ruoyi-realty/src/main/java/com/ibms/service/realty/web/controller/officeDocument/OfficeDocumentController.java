package com.ibms.service.realty.web.controller.officeDocument;

import com.ibms.service.realty.web.domain.OfficeDocument;
import com.ibms.service.realty.web.service.OfficeDocumentService;
import com.ruoyi.common.core.utils.poi.ExcelUtil;
import com.ruoyi.common.core.web.controller.BaseController;
import com.ruoyi.common.core.web.domain.AjaxResult;
import com.ruoyi.common.core.web.page.TableDataInfo;
import com.ruoyi.common.log.annotation.Log;
import com.ruoyi.common.log.enums.BusinessType;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 办公文档资料管理Controller
 *
 * <AUTHOR>
 * @date 2023-07-10
 */
@RestController
@RequestMapping("/officeDocument")
@Api(tags = "办公文档资料管理Controller")
public class OfficeDocumentController extends BaseController {
    @Autowired
    private OfficeDocumentService officeDocumentService;

    /**
     * 查询办公文档资料管理列表
     */
    @GetMapping("/list")
    @ApiOperation("查询办公文档资料管理列表")
    public TableDataInfo list(OfficeDocument officeDocument) {
        startPage();
        List<OfficeDocument> list = officeDocumentService.selectOfficeDocumentList(officeDocument);
        return getDataTable(list);
    }

    /**
     * 导出办公文档资料管理列表
     */
    @Log(title = "办公文档资料管理", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    @ApiOperation("导出办公文档资料管理列表")
    public void export(HttpServletResponse response, OfficeDocument officeDocument) {
        List<OfficeDocument> list = officeDocumentService.selectOfficeDocumentList(officeDocument);
        ExcelUtil<OfficeDocument> util = new ExcelUtil<OfficeDocument>(OfficeDocument.class);
        util.exportExcel(response, list, "办公文档资料管理数据");
    }

    /**
     * 获取办公文档资料管理详细信息
     */
    @GetMapping(value = "/{id}")
    @ApiOperation("获取办公文档资料管理详细信息")
    public AjaxResult getInfo(@PathVariable("id") Integer id) {
        return AjaxResult.success(officeDocumentService.selectOfficeDocumentById(id));
    }

    /**
     * 新增办公文档资料管理
     */
    @Log(title = "办公文档资料管理", businessType = BusinessType.INSERT)
    @PostMapping
    @ApiOperation("新增办公文档资料管理")
    public AjaxResult add(@RequestBody OfficeDocument officeDocument) {
        return toAjax(officeDocumentService.insertOfficeDocument(officeDocument));
    }

    /**
     * 修改办公文档资料管理
     */
    @Log(title = "办公文档资料管理", businessType = BusinessType.UPDATE)
    @PutMapping
    @ApiOperation("修改办公文档资料管理")
    public AjaxResult edit(@RequestBody OfficeDocument officeDocument) {
        return toAjax(officeDocumentService.updateOfficeDocument(officeDocument));
    }

    /**
     * 删除办公文档资料管理
     */
    @Log(title = "办公文档资料管理", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    @ApiOperation("删除办公文档资料管理")
    public AjaxResult remove(@PathVariable Integer[] ids) {
        return toAjax(officeDocumentService.deleteOfficeDocumentByIds(ids));
    }

    /**
     * 审核/撤销审核
     */
    @Log(title = "审核/撤销审核", businessType = BusinessType.UPDATE)
    @PutMapping("/audit")
    @ApiOperation("审核/撤销审核")
    public AjaxResult audit(@RequestBody OfficeDocument officeDocument) {
        return toAjax(officeDocumentService.audit(officeDocument));
    }

    /**
     * 添加附件
     */
    @Log(title = "添加附件", businessType = BusinessType.UPDATE)
    @PutMapping("/addFile")
    @ApiOperation("添加附件")
    public AjaxResult addFile(@RequestBody OfficeDocument officeDocument) {
        return toAjax(officeDocumentService.addFile(officeDocument));
    }

    /**
     * 删除附件
     */
    @Log(title = "删除附件", businessType = BusinessType.UPDATE)
    @PutMapping("/deleteFile")
    @ApiOperation("删除附件")
    public AjaxResult deleteFile(@RequestBody OfficeDocument officeDocument) {
        return toAjax(officeDocumentService.deleteFile(officeDocument));
    }
}

