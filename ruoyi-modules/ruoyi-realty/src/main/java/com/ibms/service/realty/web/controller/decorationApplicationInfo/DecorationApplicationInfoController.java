package com.ibms.service.realty.web.controller.decorationApplicationInfo;

import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.ibms.service.realty.web.controller.decorationApplicationInfo.vo.DecorationApplicationProblemVo;
import com.ibms.service.realty.web.controller.decorationApplicationInfo.vo.DecorationApplicationRelationVo;
import com.ibms.service.realty.web.domain.DecorationApplicationInfo;
import com.ibms.service.realty.web.mapper.DecorationApplicationInfoMapper;
import com.ibms.service.realty.web.service.DecorationApplicationInfoService;
import com.ruoyi.common.core.utils.poi.ExcelUtil;
import com.ruoyi.common.core.web.controller.BaseController;
import com.ruoyi.common.core.web.domain.AjaxResult;
import com.ruoyi.common.core.web.page.TableDataInfo;
import com.ruoyi.common.log.annotation.Log;
import com.ruoyi.common.log.enums.BusinessType;
import com.ruoyi.common.security.utils.SecurityUtils;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.Date;
import java.util.List;


/**
 * 装修申请信息Controller
 *
 * <AUTHOR>
 * @date 2023-04-13
 */
@RestController
@RequestMapping("/applicationInfo")
@Api
public class DecorationApplicationInfoController extends BaseController {
    @Autowired
    private DecorationApplicationInfoService decorationApplicationInfoService;

    @Autowired
    private DecorationApplicationInfoMapper decorationApplicationInfoMapper;

    /**
     * 查询装修申请信息列表
     */
    @PostMapping("/list")
    @ApiOperation(value = "查询装修申请信息列表")
    public TableDataInfo list(@RequestBody DecorationApplicationInfo decorationApplicationInfo) {
        startPage();
        List<DecorationApplicationInfo> list = decorationApplicationInfoService.selectDecorationApplicationInfoList(decorationApplicationInfo);
        return getDataTable(list);
    }

    /**
     * 导出装修申请信息列表
     */
    @Log(title = "装修申请信息", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    @ApiOperation(value = "导出装修申请信息列表")
    public void export(HttpServletResponse response, DecorationApplicationInfo decorationApplicationInfo) {
        List<DecorationApplicationInfo> list = decorationApplicationInfoService.selectDecorationApplicationInfoList(decorationApplicationInfo);
        ExcelUtil<DecorationApplicationInfo> util = new ExcelUtil<DecorationApplicationInfo>(DecorationApplicationInfo.class);
        util.exportExcel(response, list, "装修申请信息数据");
    }

    /**
     * 获取装修申请信息详细信息
     */
    @GetMapping(value = "/{id}")
    @ApiOperation(value = "获取装修申请信息详细信息")
    public AjaxResult getInfo(@PathVariable("id") Integer id) {
        return AjaxResult.success(decorationApplicationInfoService.selectDecorationApplicationInfoById(id));
    }

    /**
     * 开始处理
     */
    @Log(title = "开始处理", businessType = BusinessType.UPDATE)
    @PutMapping("/start")
    @ApiOperation("开始处理")
    public AjaxResult sign(@RequestBody DecorationApplicationInfo decorationApplicationInfo) {
        decorationApplicationInfo.setStatus("3");
        decorationApplicationInfoService.updateDecorationApplicationInfo(decorationApplicationInfo);
        return AjaxResult.success();
    }


    /**
     * 修改验收项目状态
     */
    @Log(title = "修改验收项目状态", businessType = BusinessType.UPDATE)
    @PutMapping("/updateRelationStatus")
    @ApiOperation("修改验收项目状态")
    public AjaxResult updateRelationStatus(@RequestBody DecorationApplicationRelationVo decorationApplicationRelationVo) {
        decorationApplicationInfoService.updateDecorationApplicationRelation(decorationApplicationRelationVo);
        return AjaxResult.success();
    }

    /**
     * 结束
     */
    @Log(title = "结束", businessType = BusinessType.UPDATE)
    @PutMapping("/end")
    @ApiOperation("结束")
    public AjaxResult end(@RequestBody DecorationApplicationInfo decorationApplicationInfo) {
        List<DecorationApplicationRelationVo> decorationApplicationRelationList = decorationApplicationInfoMapper.selectDecorationApplicationRelation(decorationApplicationInfo.getId());

        for (DecorationApplicationRelationVo decorationApplicationRelationVo : decorationApplicationRelationList) {
            if (decorationApplicationRelationVo.getStatus() == null) {
                return AjaxResult.error("还有未处理的验收项目");
            }
            if (decorationApplicationRelationVo.getStatus() == 1) {
                UpdateWrapper<DecorationApplicationInfo> updateWrapper = new UpdateWrapper<>();
                updateWrapper.set("status", "5");
                updateWrapper.eq("id", decorationApplicationInfo.getId());
                decorationApplicationInfoService.update(updateWrapper);
                return AjaxResult.success();
            }
        }

        UpdateWrapper<DecorationApplicationInfo> updateWrapper = new UpdateWrapper<>();
        updateWrapper.set("status", "4");
        updateWrapper.eq("id", decorationApplicationInfo.getId());
        decorationApplicationInfoService.update(updateWrapper);
        return AjaxResult.success();
    }

    /**
     * 新增违规记录
     */
    @Log(title = "新增违规记录", businessType = BusinessType.INSERT)
    @PostMapping("/addApplicationProblem")
    @ApiOperation("新增违规记录")
    public AjaxResult addApplicationProblem(@RequestBody DecorationApplicationProblemVo decorationApplicationProblemVo) {
        decorationApplicationProblemVo.setCreateTime(new Date());
        decorationApplicationProblemVo.setStatus(0);
        decorationApplicationProblemVo.setSubmiter(SecurityUtils.getUserId());
        decorationApplicationProblemVo.setSubmiterName(SecurityUtils.getUsername());
        return toAjax(decorationApplicationInfoService.insertDecorationApplicationRelation(decorationApplicationProblemVo));
    }

//    /**
//     * 申请验收
//     */
//    @Log(title = "申请验收", businessType = BusinessType.UPDATE)
//    @PutMapping("/apply")
//    @ApiOperation("申请验收")
//    public AjaxResult apply(@RequestBody DecorationApplicationInfo decorationApplicationInfo) {
//        //修改状态为验收中
//        decorationApplicationInfo.setStatus("3");
//        decorationApplicationInfoService.updateDecorationApplicationInfo(decorationApplicationInfo);
//        return AjaxResult.success();
//    }

    /**
     * 新增装修申请信息
     */
    @Log(title = "装修申请信息", businessType = BusinessType.INSERT)
    @PostMapping
    @ApiOperation(value = "新增装修申请信息")
    public AjaxResult add(@RequestBody DecorationApplicationInfo decorationApplicationInfo) {
        decorationApplicationInfo.setStatus("0");
        decorationApplicationInfo.setApplyer(SecurityUtils.getUserId());
        decorationApplicationInfo.setApplyerName(SecurityUtils.getUsername());
        decorationApplicationInfo.setPhone(SecurityUtils.getLoginUser().getSysUser().getPhonenumber());
        return toAjax(decorationApplicationInfoService.insertDecorationApplicationInfo(decorationApplicationInfo));
    }

    /**
     * 修改装修申请信息
     */
    @Log(title = "装修申请信息", businessType = BusinessType.UPDATE)
    @PutMapping
    @ApiOperation(value = "修改装修申请信息")
    public AjaxResult edit(@RequestBody DecorationApplicationInfo decorationApplicationInfo) {
        return toAjax(decorationApplicationInfoService.updateDecorationApplicationInfo(decorationApplicationInfo));
    }

    /**
     * 删除装修申请信息
     */
    @Log(title = "装修申请信息", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    @ApiOperation(value = "删除装修申请信息")
    public AjaxResult remove(@PathVariable Integer[] ids) {
        return toAjax(decorationApplicationInfoService.deleteDecorationApplicationInfoByIds(ids));
    }


}

