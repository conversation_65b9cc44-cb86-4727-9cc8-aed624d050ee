package com.ibms.service.realty.web.controller.roomInstrument;

import com.ibms.service.realty.web.domain.RoomInstrument;
import com.ibms.service.realty.web.service.RoomInstrumentService;
import com.ruoyi.common.core.utils.poi.ExcelUtil;
import com.ruoyi.common.core.web.controller.BaseController;
import com.ruoyi.common.core.web.domain.AjaxResult;
import com.ruoyi.common.core.web.page.TableDataInfo;
import com.ruoyi.common.log.annotation.Log;
import com.ruoyi.common.log.enums.BusinessType;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.List;


/**
 * 房间仪Controller
 *
 * <AUTHOR>
 * @date 2023-04-14
 */
@RestController
@RequestMapping("/roomInstrument")
@Api(tags = "房间仪")
public class RoomInstrumentController extends BaseController {
    @Autowired
    private RoomInstrumentService roomInstrumentService;

    /**
     * 查询房间仪列表
     */
    @GetMapping("/list")
    @ApiOperation(value = "查询房间仪列表", notes = "查询房间仪列表", httpMethod = "GET")
    public TableDataInfo list(RoomInstrument roomInstrument) {
        startPage();
        List<RoomInstrument> list = roomInstrumentService.selectRoomInstrumentList(roomInstrument);
        return getDataTable(list);
    }

    /**
     * 导出房间仪列表
     */
    @Log(title = "房间仪", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    @ApiOperation(value = "导出房间仪列表", notes = "导出房间仪列表", httpMethod = "POST")
    public void export(HttpServletResponse response, RoomInstrument roomInstrument) {
        List<RoomInstrument> list = roomInstrumentService.selectRoomInstrumentList(roomInstrument);
        ExcelUtil<RoomInstrument> util = new ExcelUtil<RoomInstrument>(RoomInstrument.class);
        util.exportExcel(response, list, "房间仪数据");
    }

    /**
     * 获取房间仪详细信息
     */
    @GetMapping(value = "/{id}")
    @ApiOperation(value = "获取房间仪详细信息", notes = "获取房间仪详细信息", httpMethod = "GET")
    public AjaxResult getInfo(@PathVariable("id") Integer id) {
        return AjaxResult.success(roomInstrumentService.selectRoomInstrumentById(id));
    }

    /**
     * 新增房间仪
     */
    @Log(title = "房间仪", businessType = BusinessType.INSERT)
    @PostMapping
    @ApiOperation(value = "新增房间仪", notes = "新增房间仪", httpMethod = "POST")
    public AjaxResult add(@RequestBody RoomInstrument roomInstrument) {
        return toAjax(roomInstrumentService.insertRoomInstrument(roomInstrument));
    }

    /**
     * 修改房间仪
     */
    @Log(title = "房间仪", businessType = BusinessType.UPDATE)
    @PutMapping
    @ApiOperation(value = "修改房间仪", notes = "修改房间仪", httpMethod = "PUT")
    public AjaxResult edit(@RequestBody RoomInstrument roomInstrument) {
        return toAjax(roomInstrumentService.updateRoomInstrument(roomInstrument));
    }

    /**
     * 删除房间仪
     */
    @Log(title = "房间仪", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    @ApiOperation(value = "删除房间仪", notes = "删除房间仪", httpMethod = "DELETE")
    public AjaxResult remove(@PathVariable Integer[] ids) {
        return toAjax(roomInstrumentService.deleteRoomInstrumentByIds(ids));
    }
}
