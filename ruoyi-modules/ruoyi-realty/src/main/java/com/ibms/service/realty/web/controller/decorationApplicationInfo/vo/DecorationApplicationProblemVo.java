package com.ibms.service.realty.web.controller.decorationApplicationInfo.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.ruoyi.common.core.annotation.Excel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * 装修申请违规记录表Vo
 *
 * <AUTHOR>
 * @date 2023-04-13
 */
@Data
@ApiModel(value = "装修申请违规记录表Vo", description = "装修申请违规记录表Vo")
public class DecorationApplicationProblemVo {

    @ApiModelProperty(value = "装修申请违规记录表id")
    private Integer id;

    @ApiModelProperty(value = "装修申请id")
    private Integer applicationId;

    /**
     * 状态，0:未解决，1：解决
     */
    @Excel(name = "状态，0:未解决，1：解决")
    @ApiModelProperty(value = "状态，0:未解决，1：解决")
    private Integer status;

    /**
     * 限制天数
     */
    @Excel(name = "限制天数")
    @ApiModelProperty(value = "限制天数")
    private Integer correctiveTime;

    /**
     * 问题描述
     */
    @Excel(name = "问题描述")
    @ApiModelProperty(value = "问题描述")
    private String description;

    /**
     * 整改措施
     */
    @Excel(name = "整改措施")
    @ApiModelProperty(value = "整改措施")
    private String correctiveAction;

    /**
     * 提交时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "提交时间", width = 30, dateFormat = "yyyy-MM-dd")
    @ApiModelProperty(value = "提交时间")
    private Date createTime;

    /**
     * 解决时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "解决时间", width = 30, dateFormat = "yyyy-MM-dd")
    @ApiModelProperty(value = "解决时间")
    private Date solvingTime;

    /**
     * 图片地址，多个用逗号隔开
     */
    @Excel(name = "图片地址，多个用逗号隔开")
    @ApiModelProperty(value = "图片地址，多个用逗号隔开")
    private String img;

    /**
     * 提交人id，关联系统用户表
     */
    @Excel(name = "提交人id，关联系统用户表")
    @ApiModelProperty("提交人id，关联系统用户表")
    private Long submiter;

    /**
     * 提交人
     */
    @Excel(name = "提交人")
    @ApiModelProperty("提交人")
    private String submiterName;

    /**
     * 解决描述
     */
    @Excel(name = "解决描述")
    @ApiModelProperty("解决描述")
    private String solvingDescription;

    /**
     * 解决图片
     */
    @Excel(name = "解决图片")
    @ApiModelProperty("解决图片")
    private String solvingImg;
}
