package com.ibms.service.realty.web.controller.cleaningInspection;

import com.ibms.service.realty.web.domain.CleaningInspection;
import com.ibms.service.realty.web.service.CleaningInspectionService;
import com.ruoyi.common.core.utils.poi.ExcelUtil;
import com.ruoyi.common.core.web.controller.BaseController;
import com.ruoyi.common.core.web.domain.AjaxResult;
import com.ruoyi.common.core.web.page.TableDataInfo;
import com.ruoyi.common.log.annotation.Log;
import com.ruoyi.common.log.enums.BusinessType;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 保洁检查Controller
 *
 * <AUTHOR>
 * @date 2023-07-04
 */
@RestController
@RequestMapping("/cleaningInspection")
@Api(tags = "保洁检查")
public class CleaningInspectionController extends BaseController {
    @Autowired
    private CleaningInspectionService cleaningInspectionService;

    /**
     * 查询保洁检查列表
     */
    @GetMapping("/list")
    @ApiOperation("查询保洁检查列表")
    public TableDataInfo list(CleaningInspection cleaningInspection) {
        startPage();
        List<CleaningInspection> list = cleaningInspectionService.selectCleaningInspectionList(cleaningInspection);
        return getDataTable(list);
    }

    /**
     * 导出保洁检查列表
     */
    @Log(title = "保洁检查", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    @ApiOperation("导出保洁检查列表")
    public void export(HttpServletResponse response, CleaningInspection cleaningInspection) {
        List<CleaningInspection> list = cleaningInspectionService.selectCleaningInspectionList(cleaningInspection);
        ExcelUtil<CleaningInspection> util = new ExcelUtil<CleaningInspection>(CleaningInspection.class);
        util.exportExcel(response, list, "保洁检查数据");
    }

    /**
     * 获取保洁检查详细信息
     */
    @GetMapping(value = "/{id}")
    @ApiOperation("获取保洁检查详细信息")
    public AjaxResult getInfo(@PathVariable("id") Integer id) {
        return AjaxResult.success(cleaningInspectionService.selectCleaningInspectionById(id));
    }

    /**
     * 新增保洁检查
     */
    @Log(title = "保洁检查", businessType = BusinessType.INSERT)
    @PostMapping
    @ApiOperation("新增保洁检查")
    public AjaxResult add(@RequestBody CleaningInspection cleaningInspection) {
        return toAjax(cleaningInspectionService.insertCleaningInspection(cleaningInspection));
    }

    /**
     * 修改保洁检查
     */
    @Log(title = "保洁检查", businessType = BusinessType.UPDATE)
    @PutMapping
    @ApiOperation("修改保洁检查")
    public AjaxResult edit(@RequestBody CleaningInspection cleaningInspection) {
        return toAjax(cleaningInspectionService.updateCleaningInspection(cleaningInspection));
    }

    /**
     * 删除保洁检查
     */
    @Log(title = "保洁检查", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    @ApiOperation("删除保洁检查")
    public AjaxResult remove(@PathVariable Integer[] ids) {
        return toAjax(cleaningInspectionService.deleteCleaningInspectionByIds(ids));
    }
}

