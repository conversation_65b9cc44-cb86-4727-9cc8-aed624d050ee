package com.ibms.service.realty.web.controller.propertyResourceInfo;

import com.ibms.service.realty.web.domain.PropertyResourceInfo;
import com.ibms.service.realty.web.service.PropertyResourceInfoService;
import com.ruoyi.common.core.web.controller.BaseController;
import com.ruoyi.common.core.web.domain.AjaxResult;
import com.ruoyi.common.core.web.page.TableDataInfo;
import com.ruoyi.common.log.annotation.Log;
import com.ruoyi.common.log.enums.BusinessType;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 房间信息Controller
 *
 * <AUTHOR>
 * @date 2023-04-11
 */
@RestController
@RequestMapping("/room")
public class RoomController extends BaseController {

    @Autowired
    private PropertyResourceInfoService propertyResourceInfoService;

    /**
     * 查询房间信息
     */
    @GetMapping("/list")
    public TableDataInfo<PropertyResourceInfo> list(PropertyResourceInfo propertyResourceInfo) {
        startPage();
        List<PropertyResourceInfo> list = propertyResourceInfoService.selectRoomList(propertyResourceInfo);
        return getDataTable(list);
    }

    /**
     * 查询房间详情
     */
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Integer id) {
        return AjaxResult.success(propertyResourceInfoService.selectRoomInfo(id));
    }

    /**
     * 修改房间详情
     */
    @Log(title = "修改房间详情", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody PropertyResourceInfo propertyResourceInfo) {
        int r = propertyResourceInfoService.updateRoomInfo(propertyResourceInfo);
        return toAjax(r);
    }
}
