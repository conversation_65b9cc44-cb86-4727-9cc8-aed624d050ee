package com.ibms.service.realty.web.controller.patrolSchedule.param;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
@ApiModel(value = "巡更任务签到图片参数", description = "巡更任务签到图片参数")
public class ScheduleSignParam {

    @ApiModelProperty(value = "关联任务，关联t_patrol_schedule表")
    private Integer relationTaskId;

    @ApiModelProperty(value = "巡更点id，关联t_patrol_point")
    private Integer patrolPointId;

    @ApiModelProperty(value = "签到图片")
    private String img;
}
