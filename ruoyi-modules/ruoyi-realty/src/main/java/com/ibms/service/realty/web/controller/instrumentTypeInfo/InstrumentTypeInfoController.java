package com.ibms.service.realty.web.controller.instrumentTypeInfo;


import com.ibms.service.realty.web.domain.InstrumentTypeInfo;
import com.ibms.service.realty.web.service.InstrumentTypeInfoService;
import com.ruoyi.common.core.utils.poi.ExcelUtil;
import com.ruoyi.common.core.web.controller.BaseController;
import com.ruoyi.common.core.web.domain.AjaxResult;
import com.ruoyi.common.core.web.page.TableDataInfo;
import com.ruoyi.common.log.annotation.Log;
import com.ruoyi.common.log.enums.BusinessType;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 仪器类型信息Controller
 *
 * <AUTHOR>
 * @date 2023-04-13
 */
@RestController
@RequestMapping("/instrumentTypeInfo")
@Api(tags = "仪器类型信息")
public class InstrumentTypeInfoController extends BaseController {
    @Autowired
    private InstrumentTypeInfoService instrumentTypeInfoService;

    /**
     * 查询仪器类型信息列表
     */
    @GetMapping("/list")
    @ApiOperation(value = "查询仪器类型信息列表", notes = "查询仪器类型信息列表", httpMethod = "GET")
    public TableDataInfo list(InstrumentTypeInfo instrumentTypeInfo) {
        startPage();
        List<InstrumentTypeInfo> list = instrumentTypeInfoService.selectInstrumentTypeInfoList(instrumentTypeInfo);
        return getDataTable(list);
    }

    /**
     * 导出仪器类型信息列表
     */
    @Log(title = "仪器类型信息", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    @ApiOperation(value = "导出仪器类型信息列表", notes = "导出仪器类型信息列表", httpMethod = "POST")
    public void export(HttpServletResponse response, InstrumentTypeInfo instrumentTypeInfo) {
        List<InstrumentTypeInfo> list = instrumentTypeInfoService.selectInstrumentTypeInfoList(instrumentTypeInfo);
        ExcelUtil<InstrumentTypeInfo> util = new ExcelUtil<InstrumentTypeInfo>(InstrumentTypeInfo.class);
        util.exportExcel(response, list, "仪器类型信息数据");
    }

    /**
     * 获取仪器类型信息详细信息
     */
    @GetMapping(value = "/{id}")
    @ApiOperation(value = "获取仪器类型信息详细信息", notes = "获取仪器类型信息详细信息", httpMethod = "GET")
    public AjaxResult getInfo(@PathVariable("id") Integer id) {
        return AjaxResult.success(instrumentTypeInfoService.selectInstrumentTypeInfoById(id));
    }

    /**
     * 新增仪器类型信息
     */
    @Log(title = "仪器类型信息", businessType = BusinessType.INSERT)
    @PostMapping
    @ApiOperation(value = "新增仪器类型信息", notes = "新增仪器类型信息", httpMethod = "POST")
    public AjaxResult add(@RequestBody InstrumentTypeInfo instrumentTypeInfo) {
        return toAjax(instrumentTypeInfoService.insertInstrumentTypeInfo(instrumentTypeInfo));
    }

    /**
     * 修改仪器类型信息
     */
    @Log(title = "仪器类型信息", businessType = BusinessType.UPDATE)
    @PutMapping
    @ApiOperation(value = "修改仪器类型信息", notes = "修改仪器类型信息", httpMethod = "PUT")
    public AjaxResult edit(@RequestBody InstrumentTypeInfo instrumentTypeInfo) {
        return toAjax(instrumentTypeInfoService.updateInstrumentTypeInfo(instrumentTypeInfo));
    }

    /**
     * 删除仪器类型信息
     */
    @Log(title = "仪器类型信息", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    @ApiOperation(value = "删除仪器类型信息", notes = "删除仪器类型信息", httpMethod = "DELETE")
    public AjaxResult remove(@PathVariable Integer[] ids) {
        return toAjax(instrumentTypeInfoService.deleteInstrumentTypeInfoByIds(ids));
    }
}
