package com.ibms.service.realty.web.controller.decorationPlan;


import com.ibms.service.realty.web.domain.DecorationPlan;
import com.ibms.service.realty.web.service.DecorationPlanService;
import com.ruoyi.common.core.utils.poi.ExcelUtil;
import com.ruoyi.common.core.web.controller.BaseController;
import com.ruoyi.common.core.web.domain.AjaxResult;
import com.ruoyi.common.core.web.page.TableDataInfo;
import com.ruoyi.common.log.annotation.Log;
import com.ruoyi.common.log.enums.BusinessType;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 装修巡检计划Controller
 *
 * <AUTHOR>
 * @date 2023-04-13
 */
@RestController
@RequestMapping("/decorationplan")
@Api(tags = "装修巡检计划")
public class DecorationPlanController extends BaseController {
    @Autowired
    private DecorationPlanService decorationPlanService;

    /**
     * 查询装修巡检计划列表
     */
    @GetMapping("/list")
    @ApiOperation(value = "查询装修巡检计划列表", notes = "查询装修巡检计划列表", httpMethod = "GET")
    public TableDataInfo list(DecorationPlan decorationPlan) {
        startPage();
        List<DecorationPlan> list = decorationPlanService.selectDecorationPlanList(decorationPlan);
        return getDataTable(list);
    }

    /**
     * 导出装修巡检计划列表
     */
    @Log(title = "装修巡检计划", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    @ApiOperation(value = "导出装修巡检计划列表", notes = "导出装修巡检计划列表", httpMethod = "POST")
    public void export(HttpServletResponse response, DecorationPlan decorationPlan) {
        List<DecorationPlan> list = decorationPlanService.selectDecorationPlanList(decorationPlan);
        ExcelUtil<DecorationPlan> util = new ExcelUtil<DecorationPlan>(DecorationPlan.class);
        util.exportExcel(response, list, "装修巡检计划数据");
    }

    /**
     * 获取装修巡检计划详细信息
     */
    @GetMapping(value = "/{taskId}")
    @ApiOperation(value = "获取装修巡检计划详细信息", notes = "获取装修巡检计划详细信息", httpMethod = "GET")
    public AjaxResult getInfo(@PathVariable("taskId") String taskId) {
        return AjaxResult.success(decorationPlanService.selectDecorationPlanByTaskId(taskId));
    }

    /**
     * 新增装修巡检计划
     */
    @Log(title = "装修巡检计划", businessType = BusinessType.INSERT)
    @PostMapping
    @ApiOperation(value = "新增装修巡检计划", notes = "新增装修巡检计划", httpMethod = "POST")
    public AjaxResult add(@RequestBody DecorationPlan decorationPlan) {
        return toAjax(decorationPlanService.insertDecorationPlan(decorationPlan));
    }

    /**
     * 修改装修巡检计划
     */
    @Log(title = "装修巡检计划", businessType = BusinessType.UPDATE)
    @PutMapping
    @ApiOperation(value = "修改装修巡检计划", notes = "修改装修巡检计划", httpMethod = "PUT")
    public AjaxResult edit(@RequestBody DecorationPlan decorationPlan) {
        return toAjax(decorationPlanService.updateDecorationPlan(decorationPlan));
    }

    /**
     * 启用/停用
     */
    @Log(title = "启用/停用", businessType = BusinessType.UPDATE)
    @PutMapping("/openOrOff")
    @ApiOperation(value = "启用/停用", notes = "启用/停用", httpMethod = "PUT")
    public AjaxResult openOrOff(@RequestBody DecorationPlan decorationPlan) {
        return toAjax(decorationPlanService.openOrOff(decorationPlan));
    }

    /**
     * 删除装修巡检计划
     */
    @Log(title = "装修巡检计划", businessType = BusinessType.DELETE)
    @DeleteMapping("/{taskIds}")
    @ApiOperation(value = "删除装修巡检计划", notes = "删除装修巡检计划", httpMethod = "DELETE")
    public AjaxResult remove(@PathVariable String[] taskIds) {
        return toAjax(decorationPlanService.deleteDecorationPlanByTaskIds(taskIds));
    }
}

