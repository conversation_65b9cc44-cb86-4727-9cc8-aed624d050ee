package com.ibms.service.realty.task;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateUtil;
import com.alibaba.fastjson.JSONArray;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.ibms.service.realty.web.domain.CleaningPlan;
import com.ibms.service.realty.web.domain.CleaningSchedule;
import com.ibms.service.realty.web.service.CleaningPlanService;
import com.ibms.service.realty.web.service.CleaningScheduleService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 保洁巡查计划表生成保洁巡查任务
 */
@Component
@Slf4j
public class CleanPlanTask {

    @Autowired
    private CleaningPlanService cleaningPlanService;

    @Autowired
    private CleaningScheduleService cleaningScheduleService;

    /**
     * 生成每日任务记录
     */
    @Scheduled(cron = "0 0 0 * * ?") // 每天凌晨扫描
    @Transactional
    public void execute() {
        Date now = new Date();// 当前时间
        List<CleaningSchedule> cleaningSchedules = new ArrayList<>();// 保洁任务集合

        /**
         *  一.执行“日”
         */
        // 1.查询所有状态为“启用”、频率为“日”、有效期开始时间小于当前时间、有效期结束时间大于当前时间、执行日期不等于当前日期的保洁计划
        QueryWrapper<CleaningPlan> queryWrapper = new QueryWrapper();
        queryWrapper.eq("status", "0");// 状态已启用
        queryWrapper.eq("patrol_frequency", "0");// 频率为“日”
        queryWrapper.le("validity_start", now); // 有效期开始时间小于当前时间
        queryWrapper.ge("validity_end", now);// 有效期结束时间大于当前时间
        queryWrapper.and(wrapper -> wrapper.ne("DATE_FORMAT(excute_time,'%Y-%m-%d')", DateUtil.format(now, "yyyy-MM-dd")).or(
                wrapper2 -> wrapper2.isNull("excute_time")));// 执行日期不等于当前日期
        // 2.遍历保洁计划，生成保洁任务
        List<CleaningPlan> cleaningPlanDays = cleaningPlanService.list(queryWrapper);
        for (CleaningPlan cleaningPlan : cleaningPlanDays) {
            String patrolTime = cleaningPlan.getPatrolTimes(); // 巡更时间'日'处理，格式如9:00,12:00;14:00,16:00
            String[] patrolTimeArr = patrolTime.split(";");
            for (String time : patrolTimeArr) {
                String[] timeArr = time.split(",");
                String startTime = timeArr[0];
                String endTime = timeArr[1];
                // 生成保洁任务
                CleaningSchedule cleaningSchedule = new CleaningSchedule();
                cleaningSchedule.setTaskName(cleaningPlan.getPlanName() + System.currentTimeMillis());
                cleaningSchedule.setCleanPlanId(cleaningPlan.getTaskId());
                cleaningSchedule.setCleanPlanName(cleaningPlan.getPlanName());
                cleaningSchedule.setStatus(0);
                cleaningSchedule.setPatroller(cleaningPlan.getTaskPerformer());
                cleaningSchedule.setPatrollerName(cleaningPlan.getTaskPerformerName());
                cleaningSchedule.setRegionId(cleaningPlan.getRegionId());
                cleaningSchedule.setRegionName(cleaningPlan.getRegionName());
                cleaningSchedule.setPlannedStartTime(DateUtil.parse(DateUtil.format(now, "yyyy-MM-dd") + " " + startTime));
                cleaningSchedule.setPlannedEndTime(DateUtil.parse(DateUtil.format(now, "yyyy-MM-dd") + " " + endTime));
                cleaningSchedules.add(cleaningSchedule);
            }
        }

        /**
         *  二.执行“周”，每周一执行，新添加的计划需要等到下周一才会执行
         */
        // 1.检查当前日期是否为周一，如果是周一则执行，否则不执行
        List<CleaningPlan> cleaningPlanWeeks = null;
        if (DateUtil.dayOfWeek(now) == 2) {
            // 2.查询所有状态为“启用”、频率为“周”、有效期开始时间小于当前时间、有效期结束时间大于当前时间、执行日期不等于当前日期的保洁计划
            QueryWrapper<CleaningPlan> queryWrapperWeek = new QueryWrapper();
            queryWrapperWeek.eq("status", "0");// 状态已启用
            queryWrapperWeek.eq("patrol_frequency", "1");// 频率为“周”
            queryWrapperWeek.le("validity_start", now); // 有效期开始时间小于当前时间
            queryWrapperWeek.ge("validity_end", now);// 有效期结束时间大于当前时间
            queryWrapperWeek.and(wrapper -> wrapper.ne("DATE_FORMAT(excute_time,'%Y-%m-%d')", DateUtil.format(now, "yyyy-MM-dd")).or(
                    wrapper2 -> wrapper2.isNull("excute_time")));// 执行日期不等于当前日期

            // 3.遍历保洁计划，生成保洁任务
            cleaningPlanWeeks = cleaningPlanService.list(queryWrapperWeek);
            for (CleaningPlan cleaningPlan : cleaningPlanWeeks) {
                String patrolTime = cleaningPlan.getPatrolTimes(); // 巡更时间'周'处理，格式如1:9:00,3:12:00;5:14:00,7:16:00,第一位为星期几，第二位为时间
                String[] patrolTimeArr = patrolTime.split(";");
                for (String time : patrolTimeArr) {
                    String[] timeArr = time.split(",");
                    String startWeek = timeArr[0].split(":")[0];
                    String startTime = timeArr[0].split(":")[1] + ":" + timeArr[0].split(":")[2];
                    String endWeek = timeArr[1].split(":")[0];
                    String endTime = timeArr[1].split(":")[1] + ":" + timeArr[1].split(":")[2];
                    // 生成保洁任务
                    CleaningSchedule cleaningSchedule = new CleaningSchedule();
                    cleaningSchedule.setTaskName(cleaningPlan.getPlanName() + System.currentTimeMillis());
                    cleaningSchedule.setCleanPlanId(cleaningPlan.getTaskId());
                    cleaningSchedule.setCleanPlanName(cleaningPlan.getPlanName());
                    cleaningSchedule.setStatus(0);
                    cleaningSchedule.setPatroller(cleaningPlan.getTaskPerformer());
                    cleaningSchedule.setPatrollerName(cleaningPlan.getTaskPerformerName());
                    cleaningSchedule.setRegionId(cleaningPlan.getRegionId());
                    cleaningSchedule.setRegionName(cleaningPlan.getRegionName());
                    cleaningSchedule.setPlannedStartTime(DateUtil.parse(
                            DateUtil.format(DateUtil.offsetDay(now, Integer.parseInt(startWeek) - 1), "yyyy-MM-dd") + " " + startTime));
                    cleaningSchedule.setPlannedEndTime(DateUtil.parse(
                            DateUtil.format(DateUtil.offsetDay(now, Integer.parseInt(endWeek) - 1), "yyyy-MM-dd") + " " + endTime));
                    cleaningSchedules.add(cleaningSchedule);

                }
            }
        }

        log.info("保洁任务成功，共生成" + cleaningSchedules.size() + "条任务");
        log.info("数据：" + JSONArray.toJSONString(cleaningSchedules));
        // 批量添加保洁任务
        if (cleaningSchedules.size() > 0) {
            cleaningScheduleService.saveBatch(cleaningSchedules);
        }

        /**
         *  三.批量更新计划执行时间
         */
        List<CleaningPlan> updateExcuteTimeList = new ArrayList<>();
        updateExcuteTimeList.addAll(CollectionUtil.isEmpty(cleaningPlanDays) ? Collections.emptyList() : cleaningPlanDays);
        updateExcuteTimeList.addAll(CollectionUtil.isEmpty(cleaningPlanWeeks) ? Collections.emptyList() : cleaningPlanWeeks);
        if (updateExcuteTimeList.size() < 1) {
            log.info("没有可执行的数据，巡更任务结束");
            return;
        }
        UpdateWrapper<CleaningPlan> updateWrapper = new UpdateWrapper();
        updateWrapper.set("excute_time", now);
        updateWrapper.in("task_id", updateExcuteTimeList.stream().map(CleaningPlan::getTaskId).collect(Collectors.toList()));
        cleaningPlanService.update(updateWrapper);

        log.info("保洁任务结束");
    }

}
