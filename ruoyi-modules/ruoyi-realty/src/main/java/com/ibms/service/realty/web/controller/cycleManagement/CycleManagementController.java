package com.ibms.service.realty.web.controller.cycleManagement;

import com.ibms.service.realty.web.domain.CycleManagement;
import com.ibms.service.realty.web.service.CycleManagementService;
import com.ruoyi.common.core.utils.poi.ExcelUtil;
import com.ruoyi.common.core.web.controller.BaseController;
import com.ruoyi.common.core.web.domain.AjaxResult;
import com.ruoyi.common.core.web.page.TableDataInfo;
import com.ruoyi.common.log.annotation.Log;
import com.ruoyi.common.log.enums.BusinessType;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 周期管理Controller
 *
 * <AUTHOR>
 * @date 2023-04-17
 */
@RestController
@RequestMapping("/cycleManagement")
@Api(tags = "周期管理")
public class CycleManagementController extends BaseController {
    @Autowired
    private CycleManagementService cycleManagementService;

    /**
     * 查询周期管理列表
     */
    @GetMapping("/list")
    @ApiOperation("查询周期管理列表")
    public TableDataInfo list(CycleManagement cycleManagement) {
        startPage();
        List<CycleManagement> list = cycleManagementService.selectCycleManagementList(cycleManagement);
        return getDataTable(list);
    }

    /**
     * 导出周期管理列表
     */
    @Log(title = "周期管理", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    @ApiOperation("导出周期管理列表")
    public void export(HttpServletResponse response, CycleManagement cycleManagement) {
        List<CycleManagement> list = cycleManagementService.selectCycleManagementList(cycleManagement);
        ExcelUtil<CycleManagement> util = new ExcelUtil<CycleManagement>(CycleManagement.class);
        util.exportExcel(response, list, "周期管理数据");
    }

    /**
     * 获取周期管理详细信息
     */
    @GetMapping(value = "/{id}")
    @ApiOperation("获取周期管理详细信息")
    public AjaxResult getInfo(@PathVariable("id") Integer id) {
        return AjaxResult.success(cycleManagementService.selectCycleManagementById(id));
    }

    /**
     * 新增周期管理
     */
    @Log(title = "周期管理", businessType = BusinessType.INSERT)
    @PostMapping
    @ApiOperation("新增周期管理")
    public AjaxResult add(@RequestBody CycleManagement cycleManagement) {
        return toAjax(cycleManagementService.insertCycleManagement(cycleManagement));
    }

    /**
     * 修改周期管理
     */
    @Log(title = "周期管理", businessType = BusinessType.UPDATE)
    @PutMapping
    @ApiOperation("修改周期管理")
    public AjaxResult edit(@RequestBody CycleManagement cycleManagement) {
        return toAjax(cycleManagementService.updateCycleManagement(cycleManagement));
    }

    /**
     * 删除周期管理
     */
    @Log(title = "周期管理", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    @ApiOperation("删除周期管理")
    public AjaxResult remove(@PathVariable Integer[] ids) {
        return toAjax(cycleManagementService.deleteCycleManagementByIds(ids));
    }
}
