package com.ibms.service.realty.web.controller.repairApplication;


import com.ibms.service.realty.web.domain.RepairApplication;
import com.ibms.service.realty.web.service.RepairApplicationService;
import com.ruoyi.common.core.utils.poi.ExcelUtil;
import com.ruoyi.common.core.web.controller.BaseController;
import com.ruoyi.common.core.web.domain.AjaxResult;
import com.ruoyi.common.core.web.page.TableDataInfo;
import com.ruoyi.common.log.annotation.Log;
import com.ruoyi.common.log.enums.BusinessType;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.List;


/**
 * 报修申请Controller
 *
 * <AUTHOR>
 * @date 2023-04-14
 */
@RestController
@RequestMapping("/application")
@Api(tags = "报修申请")
public class RepairApplicationController extends BaseController {
    @Autowired
    private RepairApplicationService repairApplicationService;

    /**
     * 查询报修申请列表
     */
    @GetMapping("/list")
    @ApiOperation("查询报修申请列表")
    public TableDataInfo list(RepairApplication repairApplication) {
        startPage();
        List<RepairApplication> list = repairApplicationService.selectRepairApplicationList(repairApplication);
        return getDataTable(list);
    }

    /**
     * 导出报修申请列表
     */
    @Log(title = "报修申请", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    @ApiOperation("导出报修申请列表")
    public void export(HttpServletResponse response, RepairApplication repairApplication) {
        List<RepairApplication> list = repairApplicationService.selectRepairApplicationList(repairApplication);
        ExcelUtil<RepairApplication> util = new ExcelUtil<RepairApplication>(RepairApplication.class);
        util.exportExcel(response, list, "报修申请数据");
    }

    /**
     * 获取报修申请详细信息
     */
    @GetMapping(value = "/{id}")
    @ApiOperation("获取报修申请详细信息")
    public AjaxResult getInfo(@PathVariable("id") String id) {
        return AjaxResult.success(repairApplicationService.selectRepairApplicationById(id));
    }

    /**
     * 新增报修申请
     */
    @Log(title = "报修申请", businessType = BusinessType.INSERT)
    @PostMapping
    @ApiOperation("新增报修申请")
    public AjaxResult add(@RequestBody RepairApplication repairApplication) {
        return toAjax(repairApplicationService.insertRepairApplication(repairApplication));
    }

    /**
     * 修改报修申请
     */
    @Log(title = "报修申请", businessType = BusinessType.UPDATE)
    @PutMapping
    @ApiOperation("修改报修申请")
    public AjaxResult edit(@RequestBody RepairApplication repairApplication) {
        return toAjax(repairApplicationService.updateRepairApplication(repairApplication));
    }

    /**
     * 删除报修申请
     */
    @Log(title = "报修申请", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    @ApiOperation("删除报修申请")
    public AjaxResult remove(@PathVariable String[] ids) {
        return toAjax(repairApplicationService.deleteRepairApplicationByIds(ids));
    }
}
