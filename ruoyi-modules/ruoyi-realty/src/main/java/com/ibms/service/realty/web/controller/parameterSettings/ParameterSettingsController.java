package com.ibms.service.realty.web.controller.parameterSettings;

import com.ibms.service.realty.web.domain.ParameterSettings;
import com.ibms.service.realty.web.service.ParameterSettingsService;
import com.ruoyi.common.core.utils.poi.ExcelUtil;
import com.ruoyi.common.core.web.controller.BaseController;
import com.ruoyi.common.core.web.domain.AjaxResult;
import com.ruoyi.common.core.web.page.TableDataInfo;
import com.ruoyi.common.log.annotation.Log;
import com.ruoyi.common.log.enums.BusinessType;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 参数设置Controller
 *
 * <AUTHOR>
 * @date 2023-04-13
 */
@RestController
@RequestMapping("/settings")
@Api(tags = "参数设置")
public class ParameterSettingsController extends BaseController {
    @Autowired
    private ParameterSettingsService parameterSettingsService;

    /**
     * 查询参数设置列表
     */
    @GetMapping("/list")
    @ApiOperation(value = "查询参数设置列表", notes = "查询参数设置列表", httpMethod = "GET")
    public TableDataInfo list(ParameterSettings parameterSettings) {
        startPage();
        List<ParameterSettings> list = parameterSettingsService.selectParameterSettingsList(parameterSettings);
        return getDataTable(list);
    }

    /**
     * 导出参数设置列表
     */
    @Log(title = "参数设置", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    @ApiOperation(value = "导出参数设置列表", notes = "导出参数设置列表", httpMethod = "POST")
    public void export(HttpServletResponse response, ParameterSettings parameterSettings) {
        List<ParameterSettings> list = parameterSettingsService.selectParameterSettingsList(parameterSettings);
        ExcelUtil<ParameterSettings> util = new ExcelUtil<ParameterSettings>(ParameterSettings.class);
        util.exportExcel(response, list, "参数设置数据");
    }

    /**
     * 获取参数设置详细信息
     */
    @GetMapping(value = "/{id}")
    @ApiOperation(value = "获取参数设置详细信息", notes = "获取参数设置详细信息", httpMethod = "GET")
    public AjaxResult getInfo(@PathVariable("id") Integer id) {
        return AjaxResult.success(parameterSettingsService.selectParameterSettingsById(id));
    }

    /**
     * 新增参数设置
     */
    @Log(title = "参数设置", businessType = BusinessType.INSERT)
    @PostMapping
    @ApiOperation(value = "新增参数设置", notes = "新增参数设置", httpMethod = "POST")
    public AjaxResult add(@RequestBody ParameterSettings parameterSettings) {
        return toAjax(parameterSettingsService.insertParameterSettings(parameterSettings));
    }

    /**
     * 修改参数设置
     */
    @Log(title = "参数设置", businessType = BusinessType.UPDATE)
    @PutMapping
    @ApiOperation(value = "修改参数设置", notes = "修改参数设置", httpMethod = "PUT")
    public AjaxResult edit(@RequestBody ParameterSettings parameterSettings) {
        return toAjax(parameterSettingsService.updateParameterSettings(parameterSettings));
    }

    /**
     * 删除参数设置
     */
    @Log(title = "参数设置", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    @ApiOperation(value = "删除参数设置", notes = "删除参数设置", httpMethod = "DELETE")
    public AjaxResult remove(@PathVariable Integer[] ids) {
        return toAjax(parameterSettingsService.deleteParameterSettingsByIds(ids));
    }
}

