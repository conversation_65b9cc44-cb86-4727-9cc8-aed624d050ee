package com.ibms.service.realty.web.controller.decorationTaskOperationRecord;


import com.ibms.service.realty.web.domain.DecorationTaskOperationRecord;
import com.ibms.service.realty.web.service.DecorationTaskOperationRecordService;
import com.ruoyi.common.core.utils.poi.ExcelUtil;
import com.ruoyi.common.core.web.controller.BaseController;
import com.ruoyi.common.core.web.domain.AjaxResult;
import com.ruoyi.common.core.web.page.TableDataInfo;
import com.ruoyi.common.log.annotation.Log;
import com.ruoyi.common.log.enums.BusinessType;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 装修巡检任务操作记录Controller
 *
 * <AUTHOR>
 * @date 2023-04-13
 */
@RestController
@RequestMapping("/decorationTaskOperationRecord")
@Api(tags = "装修巡检任务操作记录")
public class DecorationTaskOperationRecordController extends BaseController {
    @Autowired
    private DecorationTaskOperationRecordService decorationTaskOperationRecordService;

    /**
     * 查询装修巡检任务操作记录列表
     */
    @GetMapping("/list")
    @ApiOperation("查询装修巡检任务操作记录列表")
    public TableDataInfo list(DecorationTaskOperationRecord decorationTaskOperationRecord) {
        startPage();
        List<DecorationTaskOperationRecord> list = decorationTaskOperationRecordService.selectDecorationTaskOperationRecordList(decorationTaskOperationRecord);
        return getDataTable(list);
    }

    /**
     * 导出装修巡检任务操作记录列表
     */
    @Log(title = "装修巡检任务操作记录", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    @ApiOperation("导出装修巡检任务操作记录列表")
    public void export(HttpServletResponse response, DecorationTaskOperationRecord decorationTaskOperationRecord) {
        List<DecorationTaskOperationRecord> list = decorationTaskOperationRecordService.selectDecorationTaskOperationRecordList(decorationTaskOperationRecord);
        ExcelUtil<DecorationTaskOperationRecord> util = new ExcelUtil<DecorationTaskOperationRecord>(DecorationTaskOperationRecord.class);
        util.exportExcel(response, list, "装修巡检任务操作记录数据");
    }

    /**
     * 获取装修巡检任务操作记录详细信息
     */
    @GetMapping(value = "/{id}")
    @ApiOperation("获取装修巡检任务操作记录详细信息")
    public AjaxResult getInfo(@PathVariable("id") Integer id) {
        return AjaxResult.success(decorationTaskOperationRecordService.selectDecorationTaskOperationRecordById(id));
    }

    /**
     * 新增装修巡检任务操作记录
     */
    @Log(title = "装修巡检任务操作记录", businessType = BusinessType.INSERT)
    @PostMapping
    @ApiOperation("新增装修巡检任务操作记录")
    public AjaxResult add(@RequestBody DecorationTaskOperationRecord decorationTaskOperationRecord) {
        return toAjax(decorationTaskOperationRecordService.insertDecorationTaskOperationRecord(decorationTaskOperationRecord));
    }

    /**
     * 修改装修巡检任务操作记录
     */
    @Log(title = "装修巡检任务操作记录", businessType = BusinessType.UPDATE)
    @PutMapping
    @ApiOperation("修改装修巡检任务操作记录")
    public AjaxResult edit(@RequestBody DecorationTaskOperationRecord decorationTaskOperationRecord) {
        return toAjax(decorationTaskOperationRecordService.updateDecorationTaskOperationRecord(decorationTaskOperationRecord));
    }

    /**
     * 删除装修巡检任务操作记录
     */
    @Log(title = "装修巡检任务操作记录", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    @ApiOperation("删除装修巡检任务操作记录")
    public AjaxResult remove(@PathVariable Integer[] ids) {
        return toAjax(decorationTaskOperationRecordService.deleteDecorationTaskOperationRecordByIds(ids));
    }
}
