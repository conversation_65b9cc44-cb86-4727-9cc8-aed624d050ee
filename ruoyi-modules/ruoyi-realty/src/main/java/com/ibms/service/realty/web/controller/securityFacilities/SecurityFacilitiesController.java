package com.ibms.service.realty.web.controller.securityFacilities;

import com.ibms.service.realty.web.domain.SecurityFacilities;
import com.ibms.service.realty.web.service.SecurityFacilitiesService;
import com.ruoyi.common.core.utils.poi.ExcelUtil;
import com.ruoyi.common.core.web.controller.BaseController;
import com.ruoyi.common.core.web.domain.AjaxResult;
import com.ruoyi.common.core.web.page.TableDataInfo;
import com.ruoyi.common.log.annotation.Log;
import com.ruoyi.common.log.enums.BusinessType;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 安防设施管理Controller
 *
 * <AUTHOR>
 * @date 2023-06-13
 */
@RestController
@RequestMapping("/securityFacilities")
@Api(tags = "安防设施管理Controller")
public class SecurityFacilitiesController extends BaseController {
    @Autowired
    private SecurityFacilitiesService securityFacilitiesService;

    /**
     * 查询安防设施管理列表
     */
    @GetMapping("/list")
    @ApiOperation(value = "安防设施管理列表")
    public TableDataInfo list(SecurityFacilities securityFacilities) {
        startPage();
        List<SecurityFacilities> list = securityFacilitiesService.selectSecurityFacilitiesList(securityFacilities);
        return getDataTable(list);
    }

    /**
     * 导出安防设施管理列表
     */
    @Log(title = "安防设施管理", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    @ApiOperation(value = "导出安防设施管理列表")
    public void export(HttpServletResponse response, SecurityFacilities securityFacilities) {
        List<SecurityFacilities> list = securityFacilitiesService.selectSecurityFacilitiesList(securityFacilities);
        ExcelUtil<SecurityFacilities> util = new ExcelUtil<SecurityFacilities>(SecurityFacilities.class);
        util.exportExcel(response, list, "安防设施管理数据");
    }

    /**
     * 获取安防设施管理详细信息
     */
    @GetMapping(value = "/{id}")
    @ApiOperation(value = "获取安防设施管理详细信息")
    public AjaxResult getInfo(@PathVariable("id") Integer id) {
        return AjaxResult.success(securityFacilitiesService.selectSecurityFacilitiesById(id));
    }

    /**
     * 新增安防设施管理
     */
    @Log(title = "安防设施管理", businessType = BusinessType.INSERT)
    @PostMapping
    @ApiOperation(value = "新增安防设施管理")
    public AjaxResult add(@RequestBody SecurityFacilities securityFacilities) {
        return toAjax(securityFacilitiesService.insertSecurityFacilities(securityFacilities));
    }

    /**
     * 修改安防设施管理
     */
    @Log(title = "安防设施管理", businessType = BusinessType.UPDATE)
    @PutMapping
    @ApiOperation(value = "修改安防设施管理")
    public AjaxResult edit(@RequestBody SecurityFacilities securityFacilities) {
        return toAjax(securityFacilitiesService.updateSecurityFacilities(securityFacilities));
    }

    /**
     * 删除安防设施管理
     */
    @Log(title = "安防设施管理", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    @ApiOperation(value = "删除安防设施管理")
    public AjaxResult remove(@PathVariable Integer[] ids) {
        return toAjax(securityFacilitiesService.deleteSecurityFacilitiesByIds(ids));
    }
}
