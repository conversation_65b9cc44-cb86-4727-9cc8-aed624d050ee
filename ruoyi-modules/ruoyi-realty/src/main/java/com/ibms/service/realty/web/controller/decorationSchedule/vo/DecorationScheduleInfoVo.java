package com.ibms.service.realty.web.controller.decorationSchedule.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 装修巡查项目Vo
 *
 * <AUTHOR>
 * @date 2023-04-13
 */
@Data
@ApiModel(value = "装修巡查项目Vo", description = "装修巡查项目Vo")
public class DecorationScheduleInfoVo {

    @ApiModelProperty(value = "任务与项目id")
    private Integer id;

    @ApiModelProperty(value = "任务id")
    private String scheduleInfoId;

    @ApiModelProperty(value = "巡检项目id")
    private Integer infoId;

    @ApiModelProperty(value = "巡检项目名称")
    private String infoName;

    @ApiModelProperty(value = "任务巡检项目状态")
    private Integer scheduleInfoStatus;

}
