package com.ibms.service.realty.task;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateUtil;
import com.alibaba.fastjson.JSONArray;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.ibms.service.realty.web.domain.PatrolPlan;
import com.ibms.service.realty.web.domain.PatrolRoute;
import com.ibms.service.realty.web.domain.PatrolSchedule;
import com.ibms.service.realty.web.service.PatrolPlanService;
import com.ibms.service.realty.web.service.PatrolRouteService;
import com.ibms.service.realty.web.service.PatrolScheduleService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 巡更计划表生成巡更任务
 */
@Component
@Slf4j
public class PatrolPlanTask {

    @Autowired
    private PatrolPlanService patrolPlanService;

    @Autowired
    private PatrolScheduleService patrolScheduleService;

    @Autowired
    private PatrolRouteService patrolRouteService;

    /**
     * 生成每日任务记录
     */
    @Scheduled(cron = "0 0 0 * * ?") // 每天凌晨扫描
    @Transactional
    public void execute() {
        Date now = new Date();// 当前时间
        List<PatrolSchedule> patrolSchedules = new ArrayList<>();// 巡更任务集合

        /**
         *  一.执行“日”
         */
        // 1.查询所有状态为“启用”、频率为“日”、有效期开始时间小于当前时间、有效期结束时间大于当前时间、执行日期不等于当前日期的巡更计划
        QueryWrapper<PatrolPlan> queryWrapper = new QueryWrapper();
        queryWrapper.eq("status", "0");// 状态已启用
        queryWrapper.eq("patrol_frequency", "0");// 频率为“日”
        queryWrapper.le("validity_start", now); // 有效期开始时间小于当前时间
        queryWrapper.ge("validity_end", now);// 有效期结束时间大于当前时间
        queryWrapper.and(wrapper -> wrapper.ne("DATE_FORMAT(excute_time,'%Y-%m-%d')", DateUtil.format(now, "yyyy-MM-dd")).or(
                wrapper2 -> wrapper2.isNull("excute_time")));// 执行日期不等于当前日期

        // 2.遍历巡更计划，生成巡更任务
        List<PatrolPlan> patrolPlanDays = patrolPlanService.list(queryWrapper);
        for (PatrolPlan patrolPlan : patrolPlanDays) {
            String patrolTime = patrolPlan.getPatrolTimes(); // 巡更时间'日'处理，格式如9:00,12:00;14:00,16:00
            String[] patrolTimeArr = patrolTime.split(";");
            for (String time : patrolTimeArr) {
                String[] timeArr = time.split(",");
                String startTime = timeArr[0];
                String endTime = timeArr[1];
                // 生成巡更任务
                PatrolSchedule patrolSchedule = new PatrolSchedule();
                patrolSchedule.setTaskName(patrolPlan.getPlanName() + System.currentTimeMillis());
                patrolSchedule.setPatrolPlanId(patrolPlan.getTaskId());
                patrolSchedule.setPatrolPlanName(patrolPlan.getPlanName());
                patrolSchedule.setPatrolRouteId(patrolPlan.getPatrolRouteId());
                PatrolRoute patrolRoute = patrolRouteService.getById(patrolPlan.getPatrolRouteId()); // 查询巡更路线名字
                patrolSchedule.setPatrolRouteName(patrolRoute == null ? null : patrolRoute.getName());
                patrolSchedule.setStatus("0");
                patrolSchedule.setPatroller(patrolPlan.getTaskPerformerName());
                patrolSchedule.setPlannedStartTime(DateUtil.parse(DateUtil.format(now, "yyyy-MM-dd") + " " + startTime));
                patrolSchedule.setPlannedEndTime(DateUtil.parse(DateUtil.format(now, "yyyy-MM-dd") + " " + endTime));
                patrolSchedules.add(patrolSchedule);
            }
        }

        /**
         *  二.执行“周”，每周一执行，新添加的计划需要等到下周一才会执行
         */
        List<PatrolPlan> patrolPlanWeeks = null;
        // 1.检查当前日期是否为周一，如果是周一则执行，否则不执行
        if (DateUtil.dayOfWeek(now) == 2) {
            // 2.查询所有状态为“启用”、频率为“周”、有效期开始时间小于当前时间、有效期结束时间大于当前时间、执行日期不等于当前日期的巡更计划
            QueryWrapper<PatrolPlan> queryWrapperWeek = new QueryWrapper();
            queryWrapperWeek.eq("status", "0");// 状态已启用
            queryWrapperWeek.eq("patrol_frequency", "1");// 频率为“周”
            queryWrapperWeek.le("validity_start", now); // 有效期开始时间小于当前时间
            queryWrapperWeek.ge("validity_end", now);// 有效期结束时间大于当前时间
            queryWrapperWeek.and(wrapper -> wrapper.ne("DATE_FORMAT(excute_time,'%Y-%m-%d')", DateUtil.format(now, "yyyy-MM-dd")).or(
                    wrapper2 -> wrapper2.isNull("excute_time")));// 执行日期不等于当前日期

            // 3.遍历巡更计划，生成巡更任务
            patrolPlanWeeks = patrolPlanService.list(queryWrapperWeek);
            for (PatrolPlan patrolPlan : patrolPlanWeeks) {
                String patrolTime = patrolPlan.getPatrolTimes(); // 巡更时间'周'处理，格式如1:9:00,3:12:00;5:14:00,7:16:00,第一位为星期几，第二位为时间
                String[] patrolTimeArr = patrolTime.split(";");
                for (String time : patrolTimeArr) {
                    String[] timeArr = time.split(",");
                    String startWeek = timeArr[0].split(":")[0];
                    String startTime = timeArr[0].split(":")[1] + ":" + timeArr[0].split(":")[2];
                    String endWeek = timeArr[1].split(":")[0];
                    String endTime = timeArr[1].split(":")[1] + ":" + timeArr[1].split(":")[2];
                    // 生成巡更任务
                    PatrolSchedule patrolSchedule = new PatrolSchedule();
                    patrolSchedule.setTaskName(patrolPlan.getPlanName() + System.currentTimeMillis());
                    patrolSchedule.setPatrolPlanId(patrolPlan.getTaskId());
                    patrolSchedule.setPatrolPlanName(patrolPlan.getPlanName());
                    patrolSchedule.setPatrolRouteId(patrolPlan.getPatrolRouteId());
                    PatrolRoute patrolRoute = patrolRouteService.getById(patrolPlan.getPatrolRouteId()); // 查询巡更路线名字
                    patrolSchedule.setPatrolRouteName(patrolRoute == null ? null : patrolRoute.getName());
                    patrolSchedule.setStatus("0");
                    patrolSchedule.setPatroller(patrolPlan.getTaskPerformerName());
                    patrolSchedule.setPlannedStartTime(DateUtil.parse(
                            DateUtil.format(DateUtil.offsetDay(now, Integer.parseInt(startWeek) - 1), "yyyy-MM-dd") + " " + startTime));
                    patrolSchedule.setPlannedEndTime(DateUtil.parse(
                            DateUtil.format(DateUtil.offsetDay(now, Integer.parseInt(endWeek) - 1), "yyyy-MM-dd") + " " + endTime));
                    patrolSchedules.add(patrolSchedule);

                }
            }
        }

        log.info("巡更任务成功，共生成" + patrolSchedules.size() + "条任务");
        log.info("数据：" + JSONArray.toJSONString(patrolSchedules));
        // 批量添加巡更任务
        if (patrolSchedules.size() > 0) {
            patrolScheduleService.saveBatch(patrolSchedules);
        }

        /**
         *  三.批量更新计划执行时间
         */
        List<PatrolPlan> updateExcuteTimeList = new ArrayList<>();
        updateExcuteTimeList.addAll(CollectionUtil.isEmpty(patrolPlanDays) ? Collections.emptyList() : patrolPlanDays);
        updateExcuteTimeList.addAll(CollectionUtil.isEmpty(patrolPlanWeeks) ? Collections.emptyList() : patrolPlanWeeks);
        if (updateExcuteTimeList.size() < 1) {
            log.info("没有可执行的数据，巡更任务结束");
            return;
        }
        UpdateWrapper<PatrolPlan> updateWrapper = new UpdateWrapper();
        updateWrapper.set("excute_time", now);
        updateWrapper.in("task_id", updateExcuteTimeList.stream().map(PatrolPlan::getTaskId).collect(Collectors.toList()));
        patrolPlanService.update(updateWrapper);

        log.info("巡更任务结束");
    }
}
