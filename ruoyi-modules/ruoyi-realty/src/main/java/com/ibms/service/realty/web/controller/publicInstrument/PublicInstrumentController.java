package com.ibms.service.realty.web.controller.publicInstrument;


import com.ibms.service.realty.web.domain.PublicInstrument;
import com.ibms.service.realty.web.service.PublicInstrumentService;
import com.ruoyi.common.core.utils.poi.ExcelUtil;
import com.ruoyi.common.core.web.controller.BaseController;
import com.ruoyi.common.core.web.domain.AjaxResult;
import com.ruoyi.common.core.web.page.TableDataInfo;
import com.ruoyi.common.log.annotation.Log;
import com.ruoyi.common.log.enums.BusinessType;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 公摊仪表Controller
 *
 * <AUTHOR>
 * @date 2023-04-14
 */
@RestController
@RequestMapping("/publicInstrument")
@Api(tags = "公摊仪表")
public class PublicInstrumentController extends BaseController {
    @Autowired
    private PublicInstrumentService publicInstrumentService;

    /**
     * 查询公摊仪表列表
     */
    @GetMapping("/list")
    @ApiOperation("查询公摊仪表列表")
    public TableDataInfo list(PublicInstrument publicInstrument) {
        startPage();
        List<PublicInstrument> list = publicInstrumentService.selectPublicInstrumentList(publicInstrument);
        return getDataTable(list);
    }

    /**
     * 导出公摊仪表列表
     */
    @Log(title = "公摊仪表", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    @ApiOperation("导出公摊仪表列表")
    public void export(HttpServletResponse response, PublicInstrument publicInstrument) {
        List<PublicInstrument> list = publicInstrumentService.selectPublicInstrumentList(publicInstrument);
        ExcelUtil<PublicInstrument> util = new ExcelUtil<PublicInstrument>(PublicInstrument.class);
        util.exportExcel(response, list, "公摊仪表数据");
    }

    /**
     * 获取公摊仪表详细信息
     */
    @GetMapping(value = "/{id}")
    @ApiOperation("获取公摊仪表详细信息")
    public AjaxResult getInfo(@PathVariable("id") Integer id) {
        return AjaxResult.success(publicInstrumentService.selectPublicInstrumentById(id));
    }

    /**
     * 新增公摊仪表
     */
    @Log(title = "公摊仪表", businessType = BusinessType.INSERT)
    @PostMapping
    @ApiOperation("新增公摊仪表")
    public AjaxResult add(@RequestBody PublicInstrument publicInstrument) {
        return toAjax(publicInstrumentService.insertPublicInstrument(publicInstrument));
    }

    /**
     * 修改公摊仪表
     */
    @Log(title = "公摊仪表", businessType = BusinessType.UPDATE)
    @PutMapping
    @ApiOperation("修改公摊仪表")
    public AjaxResult edit(@RequestBody PublicInstrument publicInstrument) {
        return toAjax(publicInstrumentService.updatePublicInstrument(publicInstrument));
    }

    /**
     * 删除公摊仪表
     */
    @Log(title = "公摊仪表", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    @ApiOperation("删除公摊仪表")
    public AjaxResult remove(@PathVariable Integer[] ids) {
        return toAjax(publicInstrumentService.deletePublicInstrumentByIds(ids));
    }
}
