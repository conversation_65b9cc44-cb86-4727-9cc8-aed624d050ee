package com.ibms.service.realty.web.controller.completionAcceptance;

import com.ibms.service.realty.web.domain.DecorationApplicationInfo;
import com.ibms.service.realty.web.service.DecorationApplicationInfoService;
import com.ruoyi.common.core.web.controller.BaseController;
import com.ruoyi.common.core.web.domain.AjaxResult;
import com.ruoyi.common.core.web.page.TableDataInfo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.Arrays;
import java.util.List;

/**
 * 竣工验收controller
 */
@RestController
@RequestMapping("/completionAcceptance")
@Api
public class CompletionAcceptanceController extends BaseController {

    @Autowired
    private DecorationApplicationInfoService decorationApplicationInfoService;

    /**
     * 查询竣工验收列表
     */
    @PostMapping("/list")
    @ApiOperation(value = "查询竣工验收列表")
    public TableDataInfo list(@RequestBody DecorationApplicationInfo decorationApplicationInfo) {
        startPage();
        Integer[] statusParams = {3, 4, 5};
        decorationApplicationInfo.setStatusParams(Arrays.asList(statusParams));
        List<DecorationApplicationInfo> list = decorationApplicationInfoService.selectDecorationApplicationInfoList(decorationApplicationInfo);
        return getDataTable(list);
    }

    /**
     * 获取竣工验收详细信息
     */
    @GetMapping(value = "/{id}")
    @ApiOperation(value = "获取竣工验收详细信息")
    public AjaxResult getInfo(@PathVariable("id") Integer id) {
        return AjaxResult.success(decorationApplicationInfoService.selectDecorationApplicationInfoById(id));
    }
}
