package com.ibms.service.realty.web.controller.attendanceRecord;

import com.ibms.service.realty.web.controller.attendanceRecord.param.AttendanceStatisticsParam;
import com.ibms.service.realty.web.controller.attendanceRecord.vo.AttendanceStatisticsVo;
import com.ibms.service.realty.web.domain.AttendanceRecord;
import com.ibms.service.realty.web.service.AttendanceRecordService;
import com.ruoyi.common.core.utils.poi.ExcelUtil;
import com.ruoyi.common.core.web.controller.BaseController;
import com.ruoyi.common.core.web.domain.AjaxResult;
import com.ruoyi.common.core.web.page.TableDataInfo;
import com.ruoyi.common.log.annotation.Log;
import com.ruoyi.common.log.enums.BusinessType;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.List;


/**
 * 打卡记录Controller
 *
 * <AUTHOR>
 * @date 2023-04-17
 */
@RestController
@RequestMapping("/attendanceRecord")
@Api(tags = "打卡记录")
public class AttendanceRecordController extends BaseController {
    @Autowired
    private AttendanceRecordService attendanceRecordService;

    /**
     * 查询打卡记录列表
     */
    @GetMapping("/list")
    @ApiOperation("查询打卡记录列表")
    public TableDataInfo list(AttendanceRecord attendanceRecord) {
        startPage();
        List<AttendanceRecord> list = attendanceRecordService.selectAttendanceRecordList(attendanceRecord);
        return getDataTable(list);
    }

    /**
     * 导出打卡记录列表
     */
    @Log(title = "打卡记录", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    @ApiOperation("导出打卡记录列表")
    public void export(HttpServletResponse response, AttendanceRecord attendanceRecord) {
        List<AttendanceRecord> list = attendanceRecordService.selectAttendanceRecordList(attendanceRecord);
        ExcelUtil<AttendanceRecord> util = new ExcelUtil<AttendanceRecord>(AttendanceRecord.class);
        util.exportExcel(response, list, "打卡记录数据");
    }

    /**
     * 获取打卡记录详细信息
     */
    @GetMapping(value = "/{id}")
    @ApiOperation("获取打卡记录详细信息")
    public AjaxResult getInfo(@PathVariable("id") Integer id) {
        return AjaxResult.success(attendanceRecordService.selectAttendanceRecordById(id));
    }

    /**
     * 新增打卡记录
     */
    @Log(title = "打卡记录", businessType = BusinessType.INSERT)
    @PostMapping
    @ApiOperation("新增打卡记录")
    public AjaxResult add(@RequestBody AttendanceRecord attendanceRecord) {
        return toAjax(attendanceRecordService.insertAttendanceRecord(attendanceRecord));
    }

    /**
     * 修改打卡记录
     */
    @Log(title = "打卡记录", businessType = BusinessType.UPDATE)
    @PutMapping
    @ApiOperation("修改打卡记录")
    public AjaxResult edit(@RequestBody AttendanceRecord attendanceRecord) {
        return toAjax(attendanceRecordService.updateAttendanceRecord(attendanceRecord));
    }

    /**
     * 删除打卡记录
     */
    @Log(title = "打卡记录", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    @ApiOperation("删除打卡记录")
    public AjaxResult remove(@PathVariable Integer[] ids) {
        return toAjax(attendanceRecordService.deleteAttendanceRecordByIds(ids));
    }

    /**
     * 考勤统计
     */
    @GetMapping("/attendanceStatistics/list")
    @ApiOperation("考勤统计")
    public TableDataInfo attendanceStatisticsList(AttendanceStatisticsParam attendanceStatisticsParam) {
        startPage();
        List<AttendanceStatisticsVo> list = attendanceRecordService.attendanceStatisticsList(attendanceStatisticsParam);
        return getDataTable(list);
    }

    /**
     * 考勤统计excel导出
     */
    @Log(title = "考勤统计", businessType = BusinessType.EXPORT)
    @PostMapping("/attendanceStatistics/export")
    @ApiOperation("考勤统计excel导出")
    public void attendanceStatisticsExport(HttpServletResponse response, AttendanceStatisticsParam attendanceStatisticsParam) {
        List<AttendanceStatisticsVo> list = attendanceRecordService.attendanceStatisticsList(attendanceStatisticsParam);
        ExcelUtil<AttendanceStatisticsVo> util = new ExcelUtil<AttendanceStatisticsVo>(AttendanceStatisticsVo.class);
        util.exportExcel(response, list, "考勤统计数据");
    }
}
