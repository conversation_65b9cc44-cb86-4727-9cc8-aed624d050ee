package com.ibms.service.realty.web.controller.decorationApplicationAccept;

import com.ibms.service.realty.web.domain.DecorationApplicationAccept;
import com.ibms.service.realty.web.service.DecorationApplicationAcceptService;
import com.ruoyi.common.core.utils.poi.ExcelUtil;
import com.ruoyi.common.core.web.controller.BaseController;
import com.ruoyi.common.core.web.domain.AjaxResult;
import com.ruoyi.common.core.web.page.TableDataInfo;
import com.ruoyi.common.log.annotation.Log;
import com.ruoyi.common.log.enums.BusinessType;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 装修验收项目信息Controller
 *
 * <AUTHOR>
 * @date 2023-06-13
 */
@RestController
@RequestMapping("/decorationApplicationAccept")
@Api(tags = "装修验收项目信息")
public class DecorationApplicationAcceptController extends BaseController {
    @Autowired
    private DecorationApplicationAcceptService decorationApplicationAcceptService;

    /**
     * 查询装修验收项目信息列表
     */
    @GetMapping("/list")
    @ApiOperation(value = "查询装修验收项目信息列表")
    public TableDataInfo list(DecorationApplicationAccept decorationApplicationAccept) {
        startPage();
        List<DecorationApplicationAccept> list = decorationApplicationAcceptService.selectDecorationApplicationAcceptList(decorationApplicationAccept);
        return getDataTable(list);
    }

    /**
     * 导出装修验收项目信息列表
     */
    @Log(title = "装修验收项目信息", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    @ApiOperation(value = "导出装修验收项目信息列表")
    public void export(HttpServletResponse response, DecorationApplicationAccept decorationApplicationAccept) {
        List<DecorationApplicationAccept> list = decorationApplicationAcceptService.selectDecorationApplicationAcceptList(decorationApplicationAccept);
        ExcelUtil<DecorationApplicationAccept> util = new ExcelUtil<DecorationApplicationAccept>(DecorationApplicationAccept.class);
        util.exportExcel(response, list, "装修验收项目信息数据");
    }

    /**
     * 获取装修验收项目信息详细信息
     */
    @GetMapping(value = "/{id}")
    @ApiOperation(value = "获取装修验收项目信息详细信息")
    public AjaxResult getInfo(@PathVariable("id") Integer id) {
        return AjaxResult.success(decorationApplicationAcceptService.selectDecorationApplicationAcceptById(id));
    }

    /**
     * 新增装修验收项目信息
     */
    @Log(title = "装修验收项目信息", businessType = BusinessType.INSERT)
    @ApiOperation(value = "新增装修验收项目信息")
    @PostMapping
    public AjaxResult add(@RequestBody DecorationApplicationAccept decorationApplicationAccept) {
        return toAjax(decorationApplicationAcceptService.insertDecorationApplicationAccept(decorationApplicationAccept));
    }

    /**
     * 修改装修验收项目信息
     */
    @Log(title = "装修验收项目信息", businessType = BusinessType.UPDATE)
    @PutMapping
    @ApiOperation(value = "修改装修验收项目信息")
    public AjaxResult edit(@RequestBody DecorationApplicationAccept decorationApplicationAccept) {
        return toAjax(decorationApplicationAcceptService.updateDecorationApplicationAccept(decorationApplicationAccept));
    }

    /**
     * 删除装修验收项目信息
     */
    @Log(title = "装修验收项目信息", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    @ApiOperation(value = "删除装修验收项目信息")
    public AjaxResult remove(@PathVariable Integer[] ids) {
        return toAjax(decorationApplicationAcceptService.deleteDecorationApplicationAcceptByIds(ids));
    }

    /**
     * 获取验收申请设置-经办人设置
     */
    @GetMapping(value = "/retrieveHandlerSetting")
    @ApiOperation(value = "获取装修验收项目信息详细信息")
    public AjaxResult retrieveHandlerSetting() {
        return AjaxResult.success(decorationApplicationAcceptService.retrieveHandlerSetting());
    }

    /**
     * 设置验收申请设置-经办人设置
     */
    @PutMapping(value = "/handlerSetting/{handlerId}")
    @ApiOperation(value = "设置验收申请设置-经办人设置")
    public AjaxResult handlerSetting(@PathVariable Long[] handlerId) {
        return toAjax(decorationApplicationAcceptService.handlerSetting(handlerId));
    }
}

