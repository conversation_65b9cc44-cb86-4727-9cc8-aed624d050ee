package com.ibms.service.realty.task;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.lang.UUID;
import com.alibaba.fastjson.JSONArray;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.ibms.service.realty.web.controller.decorationSchedule.vo.DecorationScheduleInfoVo;
import com.ibms.service.realty.web.domain.DecorationPlan;
import com.ibms.service.realty.web.domain.DecorationSchedule;
import com.ibms.service.realty.web.service.DecorationPlanService;
import com.ibms.service.realty.web.service.DecorationScheduleService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 装修巡查计划表生成装修巡查任务
 */
@Component
@Slf4j
public class DecorationPlanTask {
    @Autowired
    private DecorationPlanService decorationPlanService;
    @Autowired
    private DecorationScheduleService decorationScheduleService;

    /**
     * 生成每日任务记录
     */
    @Scheduled(cron = "0 0 0 * * ?") // 每天凌晨执行
    @Transactional
    public void createTask() {
        Date now = new Date();// 当前时间
        List<DecorationSchedule> decorationSchedules = new ArrayList<>();// 装修任务集合
        List<DecorationScheduleInfoVo> decorationScheduleInfoVos = new ArrayList<>();// 装修任务与巡查项目关联数据集合
        /**
         *  一.执行“日”
         */
        // 1.查询所有状态为“启用”、频率为“日”、有效期开始时间小于当前时间、有效期结束时间大于当前时间、执行日期不等于当前日期的装修计划
        QueryWrapper<DecorationPlan> queryWrapper = new QueryWrapper();
        queryWrapper.eq("status", "0");// 状态已启用
        queryWrapper.eq("patrol_frequency", "0");// 频率为“日”
        queryWrapper.le("validity_start", now); // 有效期开始时间小于当前时间
        queryWrapper.ge("validity_end", now);// 有效期结束时间大于当前时间
        queryWrapper.and(wrapper -> wrapper.ne("DATE_FORMAT(execute_time,'%Y-%m-%d')", DateUtil.format(now, "yyyy-MM-dd")).or(
                wrapper2 -> wrapper2.isNull("execute_time")));// 执行日期不等于当前日期
        // 2.遍历装修计划，生成装修任务
        List<DecorationPlan> decorationPlanDays = decorationPlanService.list(queryWrapper);
        for (DecorationPlan decorationPlan : decorationPlanDays) {
            String patrolTime = decorationPlan.getPatrolTimes(); // 巡更时间'日'处理，格式如9:00,12:00;14:00,16:00
            String[] patrolTimeArr = patrolTime.split(";");
            for (String time : patrolTimeArr) {
                String[] timeArr = time.split(",");
                String startTime = timeArr[0];
                String endTime = timeArr[1];
                // 生成装修任务
                DecorationSchedule decorationSchedule = new DecorationSchedule();
                decorationSchedule.setTaskId("ZK" + DateUtil.format(now, "yyyyMMdd") + UUID.randomUUID().toString().substring(0, 8));
                decorationSchedule.setTaskName(decorationPlan.getPlanName() + System.currentTimeMillis());
                decorationSchedule.setDecorationPlanId(decorationPlan.getTaskId());
                decorationSchedule.setDecorationPlanName(decorationPlan.getPlanName());
                decorationSchedule.setStatus("0");
                decorationSchedule.setPatrollerId(decorationPlan.getPerformerId());
                decorationSchedule.setPatrollerName(decorationPlan.getPerformerName());
                decorationSchedule.setRoomCode(decorationPlan.getRoomCode());
                decorationSchedule.setRoomName(decorationPlan.getRoomName());
                decorationSchedule.setPlannedStartTime(DateUtil.parse(DateUtil.format(now, "yyyy-MM-dd") + " " + startTime));
                decorationSchedule.setPlannedEndTime(DateUtil.parse(DateUtil.format(now, "yyyy-MM-dd") + " " + endTime));
                decorationSchedules.add(decorationSchedule);

                // 生成装修任务与巡查项目关联
                Arrays.stream(decorationPlan.getDecorationInspectionIds().split(",")).forEach(a -> {
                    DecorationScheduleInfoVo decorationScheduleInfo = new DecorationScheduleInfoVo();
                    decorationScheduleInfo.setInfoId(Integer.valueOf(a));
                    decorationScheduleInfo.setScheduleInfoId(decorationSchedule.getTaskId());
                    decorationScheduleInfo.setScheduleInfoStatus(null);
                    decorationScheduleInfoVos.add(decorationScheduleInfo);
                });


            }
        }

        /**
         *  二.执行“周”，每周一执行，新添加的计划需要等到下周一才会执行
         */
        // 1.检查当前日期是否为周一，如果是周一则执行，否则不执行
        List<DecorationPlan> decorationPlanWeeks = null;
        if (DateUtil.dayOfWeek(now) == 2) {
            // 2.查询所有状态为“启用”、频率为“周”、有效期开始时间小于当前时间、有效期结束时间大于当前时间、执行日期不等于当前日期的装修计划
            QueryWrapper<DecorationPlan> queryWrapperWeek = new QueryWrapper();
            queryWrapperWeek.eq("status", "0");// 状态已启用
            queryWrapperWeek.eq("patrol_frequency", "1");// 频率为“周”
            queryWrapperWeek.le("validity_start", now); // 有效期开始时间小于当前时间
            queryWrapperWeek.ge("validity_end", now);// 有效期结束时间大于当前时间
            queryWrapperWeek.and(wrapper -> wrapper.ne("DATE_FORMAT(execute_time,'%Y-%m-%d')", DateUtil.format(now, "yyyy-MM-dd")).or(
                    wrapper2 -> wrapper2.isNull("execute_time")));// 执行日期不等于当前日期

            // 3.遍历装修计划，生成装修任务
            decorationPlanWeeks = decorationPlanService.list(queryWrapperWeek);
            for (DecorationPlan decorationPlan : decorationPlanWeeks) {
                String patrolTime = decorationPlan.getPatrolTimes(); // 巡更时间'周'处理，格式如1:9:00,3:12:00;5:14:00,7:16:00,第一位为星期几，第二位为时间
                String[] patrolTimeArr = patrolTime.split(";");
                for (String time : patrolTimeArr) {
                    String[] timeArr = time.split(",");
                    String startWeek = timeArr[0].split(":")[0];
                    String startTime = timeArr[0].split(":")[1] + ":" + timeArr[0].split(":")[2];
                    String endWeek = timeArr[1].split(":")[0];
                    String endTime = timeArr[1].split(":")[1] + ":" + timeArr[1].split(":")[2];
                    // 生成装修任务
                    DecorationSchedule decorationSchedule = new DecorationSchedule();
                    decorationSchedule.setTaskId("ZK" + DateUtil.format(now, "yyyyMMdd") + UUID.randomUUID().toString().substring(0, 8));
                    decorationSchedule.setTaskName(decorationPlan.getPlanName() + System.currentTimeMillis());
                    decorationSchedule.setDecorationPlanId(decorationPlan.getTaskId());
                    decorationSchedule.setDecorationPlanName(decorationPlan.getPlanName());
                    decorationSchedule.setStatus("0");
                    decorationSchedule.setPatrollerId(decorationPlan.getPerformerId());
                    decorationSchedule.setPatrollerName(decorationPlan.getPerformerName());
                    decorationSchedule.setRoomCode(decorationPlan.getRoomCode());
                    decorationSchedule.setRoomName(decorationPlan.getRoomName());
                    decorationSchedule.setPlannedStartTime(DateUtil.parse(
                            DateUtil.format(DateUtil.offsetDay(now, Integer.parseInt(startWeek) - 1), "yyyy-MM-dd") + " " + startTime));
                    decorationSchedule.setPlannedEndTime(DateUtil.parse(
                            DateUtil.format(DateUtil.offsetDay(now, Integer.parseInt(endWeek) - 1), "yyyy-MM-dd") + " " + endTime));
                    decorationSchedules.add(decorationSchedule);

                    // 生成装修任务与巡查项目关联
                    Arrays.stream(decorationPlan.getDecorationInspectionIds().split(",")).forEach(a -> {
                        DecorationScheduleInfoVo decorationScheduleInfo = new DecorationScheduleInfoVo();
                        decorationScheduleInfo.setInfoId(Integer.valueOf(a));
                        decorationScheduleInfo.setScheduleInfoId(decorationSchedule.getTaskId());
                        decorationScheduleInfo.setScheduleInfoStatus(null);
                        decorationScheduleInfoVos.add(decorationScheduleInfo);
                    });
                }
            }
        }

        log.info("装修任务成功，共生成" + decorationSchedules.size() + "条任务");
        log.info("数据：" + JSONArray.toJSONString(decorationSchedules));
        // 批量添加装修任务
        if (decorationSchedules.size() > 0) {
            decorationScheduleService.saveBatch(decorationSchedules);
        }
        // 批量添加装修任务与巡查项目关联
        if (decorationScheduleInfoVos.size() > 0) {
            decorationScheduleService.insertBatchInfoVos(decorationScheduleInfoVos);
        }


        /**
         *  四.批量更新计划执行时间
         */
        List<DecorationPlan> updateExcuteTimeList = new ArrayList<>();
        updateExcuteTimeList.addAll(CollectionUtil.isEmpty(decorationPlanDays) ? Collections.emptyList() : decorationPlanDays);
        updateExcuteTimeList.addAll(CollectionUtil.isEmpty(decorationPlanWeeks) ? Collections.emptyList() : decorationPlanWeeks);
        if (updateExcuteTimeList.size() < 1) {
            log.info("没有可执行的数据，巡更任务结束");
            return;
        }
        UpdateWrapper<DecorationPlan> updateWrapper = new UpdateWrapper();
        updateWrapper.set("execute_time", now);
        updateWrapper.in("task_id", updateExcuteTimeList.stream().map(DecorationPlan::getTaskId).collect(Collectors.toList()));
        decorationPlanService.update(updateWrapper);

        log.info("装修任务结束");
    }

}
