package com.ibms.service.realty.web.controller.securitySchedule;

import com.ibms.service.realty.web.domain.SecuritySchedule;
import com.ibms.service.realty.web.service.SecurityScheduleService;
import com.ruoyi.common.core.utils.poi.ExcelUtil;
import com.ruoyi.common.core.web.controller.BaseController;
import com.ruoyi.common.core.web.domain.AjaxResult;
import com.ruoyi.common.core.web.page.TableDataInfo;
import com.ruoyi.common.log.annotation.Log;
import com.ruoyi.common.log.enums.BusinessType;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 保安排班Controller
 *
 * <AUTHOR>
 * @date 2023-06-14
 */
@RestController
@RequestMapping("/securitySchedule")
@Api(tags = "保安排班")
public class SecurityScheduleController extends BaseController {
    @Autowired
    private SecurityScheduleService securityScheduleService;

    /**
     * 查询保安排班列表
     */
    @GetMapping("/list")
    @ApiOperation(value = "保安排班列表", notes = "保安排班列表")
    public TableDataInfo list(SecuritySchedule securitySchedule) {
        startPage();
        List<SecuritySchedule> list = securityScheduleService.selectSecurityScheduleList(securitySchedule);
        return getDataTable(list);
    }

    /**
     * 导出保安排班列表
     */
    @Log(title = "保安排班", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    @ApiOperation(value = "导出保安排班列表", notes = "导出保安排班列表")
    public void export(HttpServletResponse response, SecuritySchedule securitySchedule) {
        List<SecuritySchedule> list = securityScheduleService.selectSecurityScheduleList(securitySchedule);
        ExcelUtil<SecuritySchedule> util = new ExcelUtil<SecuritySchedule>(SecuritySchedule.class);
        util.exportExcel(response, list, "保安排班数据");
    }

    /**
     * 获取保安排班详细信息
     */
    @GetMapping(value = "/{id}")
    @ApiOperation(value = "获取保安排班详细信息", notes = "获取保安排班详细信息")
    public AjaxResult getInfo(@PathVariable("id") String id) {
        return AjaxResult.success(securityScheduleService.selectSecurityScheduleById(id));
    }

    /**
     * 新增保安排班
     */
    @Log(title = "保安排班", businessType = BusinessType.INSERT)
    @PostMapping
    @ApiOperation(value = "新增保安排班", notes = "新增保安排班")
    public AjaxResult add(@RequestBody SecuritySchedule securitySchedule) {
        return toAjax(securityScheduleService.insertSecuritySchedule(securitySchedule));
    }

    /**
     * 修改保安排班
     */
    @Log(title = "保安排班", businessType = BusinessType.UPDATE)
    @PutMapping
    @ApiOperation(value = "修改保安排班", notes = "修改保安排班")
    public AjaxResult edit(@RequestBody SecuritySchedule securitySchedule) {
        return toAjax(securityScheduleService.updateSecuritySchedule(securitySchedule));
    }

    /**
     * 删除保安排班
     */
    @Log(title = "保安排班", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    @ApiOperation(value = "删除保安排班", notes = "删除保安排班")
    public AjaxResult remove(@PathVariable String[] ids) {
        return toAjax(securityScheduleService.deleteSecurityScheduleByIds(ids));
    }
}
