package com.ibms.service.realty.web.controller.cleaningToolManagement;

import com.ibms.service.realty.web.domain.CleaningToolBorrowReturnRecord;
import com.ibms.service.realty.web.domain.CleaningToolManagement;
import com.ibms.service.realty.web.service.CleaningToolManagementService;
import com.ruoyi.common.core.utils.poi.ExcelUtil;
import com.ruoyi.common.core.web.controller.BaseController;
import com.ruoyi.common.core.web.domain.AjaxResult;
import com.ruoyi.common.core.web.page.TableDataInfo;
import com.ruoyi.common.log.annotation.Log;
import com.ruoyi.common.log.enums.BusinessType;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 保洁工具管理Controller
 *
 * <AUTHOR>
 * @date 2023-07-04
 */
@RestController
@RequestMapping("/cleaningToolManagement")
@Api(tags = "保洁工具管理")
public class CleaningToolManagementController extends BaseController {
    @Autowired
    private CleaningToolManagementService cleaningToolManagementService;

    /**
     * 查询保洁工具管理列表
     */
    @GetMapping("/list")
    @ApiOperation("查询保洁工具管理列表")
    public TableDataInfo list(CleaningToolManagement cleaningToolManagement) {
        startPage();
        List<CleaningToolManagement> list = cleaningToolManagementService.selectCleaningToolManagementList(cleaningToolManagement);
        return getDataTable(list);
    }

    /**
     * 导出保洁工具管理列表
     */
    @Log(title = "保洁工具管理", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    @ApiOperation("导出保洁工具管理列表")
    public void export(HttpServletResponse response, CleaningToolManagement cleaningToolManagement) {
        List<CleaningToolManagement> list = cleaningToolManagementService.selectCleaningToolManagementList(cleaningToolManagement);
        ExcelUtil<CleaningToolManagement> util = new ExcelUtil<CleaningToolManagement>(CleaningToolManagement.class);
        util.exportExcel(response, list, "保洁工具管理数据");
    }

    /**
     * 获取保洁工具管理详细信息
     */
    @GetMapping(value = "/{id}")
    @ApiOperation("获取保洁工具管理详细信息")
    public AjaxResult getInfo(@PathVariable("id") Integer id) {
        return AjaxResult.success(cleaningToolManagementService.selectCleaningToolManagementById(id));
    }

    /**
     * 根据工具id查询借还记录列表
     */
    @GetMapping("/receiveAndReturnlist")
    @ApiOperation("根据工具id查询借还记录列表")
    public TableDataInfo receiveAndReturnlist(CleaningToolBorrowReturnRecord cleaningToolBorrowReturnRecord) {
        startPage();
        List<CleaningToolBorrowReturnRecord> list = cleaningToolManagementService.receiveAndReturnlist(cleaningToolBorrowReturnRecord);
        return getDataTable(list);
    }

    /**
     * 新增保洁工具管理
     */
    @Log(title = "保洁工具管理", businessType = BusinessType.INSERT)
    @PostMapping
    @ApiOperation("新增保洁工具管理")
    public AjaxResult add(@RequestBody CleaningToolManagement cleaningToolManagement) {
        return toAjax(cleaningToolManagementService.insertCleaningToolManagement(cleaningToolManagement));
    }

    /**
     * 修改保洁工具管理
     */
    @Log(title = "保洁工具管理", businessType = BusinessType.UPDATE)
    @PutMapping
    @ApiOperation("修改保洁工具管理")
    public AjaxResult edit(@RequestBody CleaningToolManagement cleaningToolManagement) {
        return toAjax(cleaningToolManagementService.updateCleaningToolManagement(cleaningToolManagement));
    }

    /**
     * 删除保洁工具管理
     */
    @Log(title = "保洁工具管理", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    @ApiOperation("删除保洁工具管理")
    public AjaxResult remove(@PathVariable Integer[] ids) {
        return toAjax(cleaningToolManagementService.deleteCleaningToolManagementByIds(ids));
    }

    /**
     * 领用
     */
    @Log(title = "领用", businessType = BusinessType.INSERT)
    @PostMapping("/receive")
    @ApiOperation("领用")
    public AjaxResult receive(@RequestBody CleaningToolBorrowReturnRecord cleaningToolManagement) {
        return cleaningToolManagementService.receiveCleaningTool(cleaningToolManagement);
    }

    /**
     * 归还
     */
    @Log(title = "归还", businessType = BusinessType.INSERT)
    @PostMapping("/return")
    @ApiOperation("归还")
    public AjaxResult returnn(@RequestBody CleaningToolBorrowReturnRecord cleaningToolManagement) {
        return cleaningToolManagementService.returnn(cleaningToolManagement);
    }
}
