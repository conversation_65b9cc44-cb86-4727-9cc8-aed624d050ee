package com.ibms.service.realty.web.controller.schedulingGroup;

import com.ibms.service.realty.web.domain.SchedulingGroup;
import com.ibms.service.realty.web.service.SchedulingGroupService;
import com.ruoyi.common.core.utils.poi.ExcelUtil;
import com.ruoyi.common.core.web.controller.BaseController;
import com.ruoyi.common.core.web.domain.AjaxResult;
import com.ruoyi.common.core.web.page.TableDataInfo;
import com.ruoyi.common.log.annotation.Log;
import com.ruoyi.common.log.enums.BusinessType;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiModelProperty;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.Date;
import java.util.List;


/**
 * 排班分组Controller
 *
 * <AUTHOR>
 * @date 2023-04-17
 */
@RestController
@RequestMapping("/schedulingGroup")
@Api(tags = "排班分组")
public class SchedulingGroupController extends BaseController {
    @Autowired
    private SchedulingGroupService schedulingGroupService;

    /**
     * 查询排班分组列表
     */
    @GetMapping("/list")
    @ApiModelProperty("查询排班分组列表")
    public TableDataInfo list(SchedulingGroup schedulingGroup) {
        startPage();
        List<SchedulingGroup> list = schedulingGroupService.selectSchedulingGroupList(schedulingGroup);
        return getDataTable(list);
    }

    /**
     * 导出排班分组列表
     */
    @Log(title = "排班分组", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    @ApiModelProperty("导出排班分组列表")
    public void export(HttpServletResponse response, SchedulingGroup schedulingGroup) {
        List<SchedulingGroup> list = schedulingGroupService.selectSchedulingGroupList(schedulingGroup);
        ExcelUtil<SchedulingGroup> util = new ExcelUtil<SchedulingGroup>(SchedulingGroup.class);
        util.exportExcel(response, list, "排班分组数据");
    }

    /**
     * 获取排班分组详细信息
     */
    @GetMapping(value = "/{id}")
    @ApiModelProperty("获取排班分组详细信息")
    public AjaxResult getInfo(@PathVariable("id") Integer id) {
        return AjaxResult.success(schedulingGroupService.selectSchedulingGroupById(id));
    }

    /**
     * 新增排班分组
     */
    @Log(title = "排班分组", businessType = BusinessType.INSERT)
    @PostMapping
    @ApiModelProperty("新增排班分组")
    public AjaxResult add(@RequestBody SchedulingGroup schedulingGroup) {
        return toAjax(schedulingGroupService.insertSchedulingGroup(schedulingGroup));
    }

    /**
     * 修改排班分组
     */
    @Log(title = "排班分组", businessType = BusinessType.UPDATE)
    @PutMapping
    @ApiModelProperty("修改排班分组")
    public AjaxResult edit(@RequestBody SchedulingGroup schedulingGroup) {
        return toAjax(schedulingGroupService.updateSchedulingGroup(schedulingGroup));
    }

    /**
     * 删除排班分组
     */
    @Log(title = "排班分组", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    @ApiModelProperty("删除排班分组")
    public AjaxResult remove(@PathVariable Integer[] ids) {
        return toAjax(schedulingGroupService.deleteSchedulingGroupByIds(ids));
    }

    /**
     * 查询我的排班
     */
    @GetMapping("/myScheduling")
    @ApiModelProperty("查询我的排班")
    public AjaxResult myScheduling(Date datePram) {
        return AjaxResult.success(schedulingGroupService.myScheduling(datePram));
    }
}
