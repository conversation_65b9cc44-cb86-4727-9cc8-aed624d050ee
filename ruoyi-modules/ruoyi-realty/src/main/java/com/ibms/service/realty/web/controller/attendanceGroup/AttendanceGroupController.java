package com.ibms.service.realty.web.controller.attendanceGroup;


import com.ibms.service.realty.web.domain.AttendanceGroup;
import com.ibms.service.realty.web.service.AttendanceGroupService;
import com.ruoyi.common.core.utils.poi.ExcelUtil;
import com.ruoyi.common.core.web.controller.BaseController;
import com.ruoyi.common.core.web.domain.AjaxResult;
import com.ruoyi.common.core.web.page.TableDataInfo;
import com.ruoyi.common.log.annotation.Log;
import com.ruoyi.common.log.enums.BusinessType;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 考勤组管理Controller
 *
 * <AUTHOR>
 * @date 2023-04-17
 */
@RestController
@RequestMapping("/group")
@Api(tags = "考勤组管理")
public class AttendanceGroupController extends BaseController {
    @Autowired
    private AttendanceGroupService attendanceGroupService;

    /**
     * 查询考勤组管理列表
     */
    @GetMapping("/list")
    @ApiOperation("查询考勤组管理列表")
    public TableDataInfo list(AttendanceGroup attendanceGroup) {
        startPage();
        List<AttendanceGroup> list = attendanceGroupService.selectAttendanceGroupList(attendanceGroup);
        return getDataTable(list);
    }

    /**
     * 导出考勤组管理列表
     */
    @Log(title = "考勤组管理", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    @ApiOperation("导出考勤组管理列表")
    public void export(HttpServletResponse response, AttendanceGroup attendanceGroup) {
        List<AttendanceGroup> list = attendanceGroupService.selectAttendanceGroupList(attendanceGroup);
        ExcelUtil<AttendanceGroup> util = new ExcelUtil<AttendanceGroup>(AttendanceGroup.class);
        util.exportExcel(response, list, "考勤组管理数据");
    }

    /**
     * 获取考勤组管理详细信息
     */
    @GetMapping(value = "/{id}")
    @ApiOperation("获取考勤组管理详细信息")
    public AjaxResult getInfo(@PathVariable("id") Integer id) {
        return AjaxResult.success(attendanceGroupService.selectAttendanceGroupById(id));
    }

    /**
     * 新增考勤组管理
     */
    @Log(title = "考勤组管理", businessType = BusinessType.INSERT)
    @PostMapping
    @ApiOperation("新增考勤组管理")
    public AjaxResult add(@RequestBody AttendanceGroup attendanceGroup) {
        return toAjax(attendanceGroupService.insertAttendanceGroup(attendanceGroup));
    }

    /**
     * 修改考勤组管理
     */
    @Log(title = "考勤组管理", businessType = BusinessType.UPDATE)
    @PutMapping
    @ApiOperation("修改考勤组管理")
    public AjaxResult edit(@RequestBody AttendanceGroup attendanceGroup) {
        return toAjax(attendanceGroupService.updateAttendanceGroup(attendanceGroup));
    }

    /**
     * 删除考勤组管理
     */
    @Log(title = "考勤组管理", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    @ApiOperation("删除考勤组管理")
    public AjaxResult remove(@PathVariable Integer[] ids) {
        return toAjax(attendanceGroupService.deleteAttendanceGroupByIds(ids));
    }
}
