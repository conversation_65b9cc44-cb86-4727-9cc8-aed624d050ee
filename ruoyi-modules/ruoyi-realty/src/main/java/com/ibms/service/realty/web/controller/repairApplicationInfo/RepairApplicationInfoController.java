package com.ibms.service.realty.web.controller.repairApplicationInfo;

import com.ibms.service.realty.web.domain.RepairApplicationInfo;
import com.ibms.service.realty.web.service.RepairApplicationInfoService;
import com.ruoyi.common.core.utils.poi.ExcelUtil;
import com.ruoyi.common.core.web.controller.BaseController;
import com.ruoyi.common.core.web.domain.AjaxResult;
import com.ruoyi.common.core.web.page.TableDataInfo;
import com.ruoyi.common.log.annotation.Log;
import com.ruoyi.common.log.enums.BusinessType;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.Date;
import java.util.List;

/**
 * 报修申请Controller
 *
 * <AUTHOR>
 * @date 2023-05-31
 */
@RestController
@RequestMapping("/repairApplicationInfo")
@Api(tags = "报修申请")
public class RepairApplicationInfoController extends BaseController {
    @Autowired
    private RepairApplicationInfoService repairApplicationInfoService;

    /**
     * 查询报修申请列表
     */
    @GetMapping("/list")
    @ApiOperation(value = "查询报修申请列表")
    public TableDataInfo list(RepairApplicationInfo repairApplicationInfo) {
        startPage();
        List<RepairApplicationInfo> list = repairApplicationInfoService.selectRepairApplicationInfoList(repairApplicationInfo);
        return getDataTable(list);
    }

    /**
     * 导出报修申请列表
     */
    @Log(title = "报修申请", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    @ApiOperation(value = "导出报修申请列表")
    public void export(HttpServletResponse response, RepairApplicationInfo repairApplicationInfo) {
        List<RepairApplicationInfo> list = repairApplicationInfoService.selectRepairApplicationInfoList(repairApplicationInfo);
        ExcelUtil<RepairApplicationInfo> util = new ExcelUtil<RepairApplicationInfo>(RepairApplicationInfo.class);
        util.exportExcel(response, list, "报修申请数据");
    }

    /**
     * 获取报修申请详细信息
     */
    @GetMapping(value = "/{id}")
    @ApiOperation(value = "获取报修申请详细信息")
    public AjaxResult getInfo(@PathVariable("id") Integer id) {
        return AjaxResult.success(repairApplicationInfoService.selectRepairApplicationInfoById(id));
    }

    /**
     * 新增报修申请
     */
    @Log(title = "报修申请", businessType = BusinessType.INSERT)
    @PostMapping
    @ApiOperation(value = "新增报修申请")
    public AjaxResult add(@RequestBody RepairApplicationInfo repairApplicationInfo) {
        return toAjax(repairApplicationInfoService.insertRepairApplicationInfo(repairApplicationInfo));
    }

//    /**
//     * 查询已分配的报修工单
//     */
//    @GetMapping("/maintenanceList")
//    @ApiOperation(value = "查询已分配的报修工单")
//    public TableDataInfo maintenanceList(RepairApplicationInfo repairApplicationInfo) {
//        startPage();
//        List<Integer> maintenanceStatusList = new ArrayList<>();
//        maintenanceStatusList.add()
//        repairApplicationInfo.setMaintenanceStatusList();
//        List<RepairApplicationInfo> list = repairApplicationInfoService.selectRepairApplicationInfoList(repairApplicationInfo);
//        return getDataTable(list);
//    }

    /**
     * 有偿服务
     */
    @Log(title = "有偿服务", businessType = BusinessType.UPDATE)
    @PutMapping("/paidService")
    @ApiOperation(value = "有偿服务")
    public AjaxResult paidService(@RequestBody RepairApplicationInfo repairApplicationInfo) {
        RepairApplicationInfo repairApplicationInfoParam = new RepairApplicationInfo();
        repairApplicationInfoParam.setServiceType(1);
        repairApplicationInfoParam.setServiceAmount(repairApplicationInfo.getServiceAmount());
        repairApplicationInfoParam.setWorkOrderNo(repairApplicationInfo.getWorkOrderNo());
        repairApplicationInfoParam.setServiceTypeStatus(0);
        repairApplicationInfoService.updateRepairApplicationInfo(repairApplicationInfoParam);
        return AjaxResult.success();
    }

    /**
     * 支付
     */
    @Log(title = "支付", businessType = BusinessType.UPDATE)
    @PutMapping("/pay")
    @ApiOperation(value = "支付")
    public AjaxResult pay(@RequestBody RepairApplicationInfo repairApplicationInfo) {
        RepairApplicationInfo repairApplicationInfoParam = new RepairApplicationInfo();
        repairApplicationInfoParam.setServiceTypeStatus(1);
        repairApplicationInfoParam.setWorkOrderNo(repairApplicationInfo.getWorkOrderNo());
        repairApplicationInfoService.updateRepairApplicationInfo(repairApplicationInfoParam);
        return AjaxResult.success();
    }

    /**
     * 分配维修人员
     */
    @Log(title = "分配维修人员", businessType = BusinessType.UPDATE)
    @PutMapping("/assignedMaintenancePersonnel")
    @ApiOperation(value = "分配维修人员")
    public AjaxResult assignedMaintenancePersonnel(@RequestBody RepairApplicationInfo repairApplicationInfo) {
        RepairApplicationInfo repairApplicationInfoParam = new RepairApplicationInfo();
        repairApplicationInfoParam.setMaintenancePersonnelId(repairApplicationInfo.getMaintenancePersonnelId());
        repairApplicationInfoParam.setMaintenancePersonnelName(repairApplicationInfo.getMaintenancePersonnelName());
        repairApplicationInfoParam.setMaintenancePersonnelContactPhone(repairApplicationInfo.getMaintenancePersonnelContactPhone());
        repairApplicationInfoParam.setWorkOrderNo(repairApplicationInfo.getWorkOrderNo());
        repairApplicationInfoParam.setMaintenanceStatus(0);
        repairApplicationInfoParam.setStatus(1);
        repairApplicationInfoService.updateRepairApplicationInfo(repairApplicationInfoParam);
        return AjaxResult.success();
    }

    /**
     * 维修人员工单处理
     */
    @Log(title = "维修人员工单处理", businessType = BusinessType.UPDATE)
    @PutMapping("/maintenanceDeal")
    @ApiOperation(value = "维修人员工单处理")
    public AjaxResult maintenanceDeal(@RequestBody RepairApplicationInfo repairApplicationInfo) {
        repairApplicationInfo.setMaintenanceStatus(1);
        repairApplicationInfo.setHandleTime(new Date());
        repairApplicationInfoService.updateRepairApplicationInfo(repairApplicationInfo);
        return AjaxResult.success();
    }

    /**
     * 维修人员工单完成
     */
    @Log(title = "工单完成", businessType = BusinessType.UPDATE)
    @PutMapping("/maintenanceFinish")
    @ApiOperation(value = "工单完成")
    public AjaxResult maintenanceFinish(@RequestBody RepairApplicationInfo repairApplicationInfo) {
        repairApplicationInfo.setMaintenanceStatus(2);
        repairApplicationInfo.setStatus(2);
        repairApplicationInfo.setCompletionTime(new Date());
        repairApplicationInfoService.updateRepairApplicationInfo(repairApplicationInfo);
        return AjaxResult.success();
    }

    /**
     * 修改报修申请
     */
    @Log(title = "报修申请", businessType = BusinessType.UPDATE)
    @PutMapping
    @ApiOperation(value = "修改报修申请")
    public AjaxResult edit(@RequestBody RepairApplicationInfo repairApplicationInfo) {
        return toAjax(repairApplicationInfoService.updateRepairApplicationInfo(repairApplicationInfo));
    }

    /**
     * 回访
     */
    @Log(title = "回访", businessType = BusinessType.UPDATE)
    @PutMapping("/returnVisit")
    @ApiOperation(value = "回访")
    public AjaxResult returnVisit(@RequestBody RepairApplicationInfo repairApplicationInfo) {
        repairApplicationInfo.setStatus(3);
        return toAjax(repairApplicationInfoService.updateRepairApplicationInfo(repairApplicationInfo));
    }

    /**
     * 关闭报修工单，流程结束
     */
    @Log(title = "关闭报修工单，流程结束", businessType = BusinessType.UPDATE)
    @PutMapping("/close")
    @ApiOperation(value = "关闭报修工单，流程结束")
    public AjaxResult close(@RequestBody RepairApplicationInfo repairApplicationInfo) {
        repairApplicationInfo.setStatus(3);
        return toAjax(repairApplicationInfoService.updateRepairApplicationInfo(repairApplicationInfo));
    }

//    /**
//     * 分配维修人员
//     */
//    @Log(title = "分配维修人员", businessType = BusinessType.UPDATE)
//    @PutMapping("/updateMaintenancePersonnel")
//    @ApiOperation(value = "修改报修申请")
//    public AjaxResult updateMaintenancePersonnel(@RequestBody RepairApplicationInfo repairApplicationInfo) {
//
//    }

    /**
     * 删除报修申请
     */
    @Log(title = "报修申请", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    @ApiOperation(value = "删除报修申请")
    public AjaxResult remove(@PathVariable Integer[] ids) {
        return toAjax(repairApplicationInfoService.deleteRepairApplicationInfoByIds(ids));
    }
}

