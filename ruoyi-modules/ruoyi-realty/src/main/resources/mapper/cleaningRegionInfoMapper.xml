<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ibms.service.realty.web.mapper.CleaningRegionInfoMapper">

    <resultMap type="CleaningRegionInfo" id="CleaningRegionInfoResult">
        <result property="id" column="id"/>
        <result property="areaName" column="area_name"/>
        <result property="place" column="place"/>
        <result property="requirement" column="requirement"/>
        <result property="staffName" column="staff_name"/>
        <result property="staffId" column="staff_id"/>
        <result property="remark" column="remark"/>
    </resultMap>

    <sql id="selectCleaningRegionInfoVo">
        select id, area_name, place, requirement, staff_name, staff_id, remark
        from t_cleaning_region_info
    </sql>

    <select id="selectCleaningRegionInfoList" parameterType="CleaningRegionInfo" resultMap="CleaningRegionInfoResult">
        <include refid="selectCleaningRegionInfoVo"/>
        <where>
            <if test="areaName != null  and areaName != ''">and area_name like concat('%', #{areaName}, '%')</if>
            <if test="place != null  and place != ''">and place like concat('%', #{place}, '%')</if>
            <if test="requirement != null  and requirement != ''">and requirement like concat('%', #{requirement},
                '%')
            </if>
            <if test="staffName != null  and staffName != ''">and staff_name like concat('%', #{staffName}, '%')</if>
            <if test="staffId != null ">and staff_id = #{staffId}</if>
        </where>
        order by id desc
    </select>

    <select id="selectCleaningRegionInfoById" parameterType="Integer" resultMap="CleaningRegionInfoResult">
        <include refid="selectCleaningRegionInfoVo"/>
        where id = #{id}
    </select>

    <insert id="insertCleaningRegionInfo" parameterType="CleaningRegionInfo" useGeneratedKeys="true" keyProperty="id">
        insert into t_cleaning_region_info
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="areaName != null">area_name,</if>
            <if test="place != null">place,</if>
            <if test="requirement != null">requirement,</if>
            <if test="staffName != null and staffName != ''">staff_name,</if>
            <if test="staffId != null">staff_id,</if>
            <if test="remark != null">remark,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="areaName != null">#{areaName},</if>
            <if test="place != null">#{place},</if>
            <if test="requirement != null">#{requirement},</if>
            <if test="staffName != null and staffName != ''">#{staffName},</if>
            <if test="staffId != null">#{staffId},</if>
            <if test="remark != null">#{remark},</if>
        </trim>
    </insert>

    <update id="updateCleaningRegionInfo" parameterType="CleaningRegionInfo">
        update t_cleaning_region_info
        <trim prefix="SET" suffixOverrides=",">
            <if test="areaName != null">area_name = #{areaName},</if>
            <if test="place != null">place = #{place},</if>
            <if test="requirement != null">requirement = #{requirement},</if>
            <if test="staffName != null and staffName != ''">staff_name = #{staffName},</if>
            <if test="staffId != null">staff_id = #{staffId},</if>
            <if test="remark != null">remark = #{remark},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteCleaningRegionInfoById" parameterType="Integer">
        delete
        from t_cleaning_region_info
        where id = #{id}
    </delete>

    <delete id="deleteCleaningRegionInfoByIds" parameterType="String">
        delete from t_cleaning_region_info where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>