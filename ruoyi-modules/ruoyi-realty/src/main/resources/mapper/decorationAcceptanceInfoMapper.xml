<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ibms.service.realty.web.mapper.DecorationAcceptanceInfoMapper">

    <resultMap type="DecorationAcceptanceInfo" id="DecorationAcceptanceInfoResult">
        <result property="applyId" column="apply_id"/>
        <result property="roomCode" column="room_code"/>
        <result property="applicant" column="applicant"/>
        <result property="applicantName" column="applicant_name"/>
        <result property="status" column="status"/>
        <result property="phone" column="phone"/>
        <result property="appointmentTime" column="appointment_time"/>
        <result property="remark" column="remark"/>
        <result property="createTime" column="create_time"/>

        <result property="result" column="result"/>
        <result property="statusName" column="statusName"/>
        <result property="roomName" column="roomName"/>
        <result property="buildingName" column="buildingName"/>
        <result property="ownerPhone" column="ownerPhone"/>
        <result property="owner" column="owner"/>

        <collection property="violationsList" ofType="com.ibms.service.realty.web.domain.DecorationApplicationProblem">
            <result property="description" column="violationsDescription"/>
            <result property="img" column="violationsImg"/>
        </collection>
        <collection property="acceptanceList"
                    ofType="com.ibms.service.realty.web.domain.DecorationApplicateAcceptRelation">
            <result property="acceptId" column="acceptId"/>
            <result property="acceptName" column="acceptName"/>
            <result property="status" column="acceptStatus"/>
        </collection>

    </resultMap>


    <!--    申请状态，0：待处理，1：验收中，2：合格，3：不合格-->
    <sql id="selectDecorationAcceptanceInfoVo">
        SELECT tdai.id AS apply_id,
        tdai.room_code,
        tdai.applyer AS applicant,
        tdai.applyer_name AS applicant_name,
        tdai.status,
        CASE
        WHEN tdai.status = '4' THEN '合格'
        WHEN tdai.status = '5' THEN '不合格'
        ELSE ''
        END AS result,
        CASE
        WHEN tdai.status = '1' THEN '处理中'
        WHEN tdai.status = '3' THEN '验收中'
        WHEN tdai.status = '4' THEN '已完成'
        WHEN tdai.status = '5' THEN '已完成'
        ELSE ''
        END AS statusName,
        tdai.remark,
        tdai.create_time,

        tdap.description AS violationsDescription, # 违规描述
        tdap.img AS violationsImg, # 违规上传图片

        tdaa.id AS acceptId, # 验收项目id
        tdaa.name AS acceptName, # 验收项目
        tdar.status AS acceptStatus, # 验收条目状态

        tpri.name AS roomName, # 房间名称
        tpri.owner_phone AS ownerPhone, # 业主电话
        tpri.owner AS owner, # 业主姓名

        tpri3.name AS buildingName # 楼宇名称
        FROM t_decoration_application_info tdai
        LEFT JOIN t_decoration_application_problem tdap ON tdai.id = tdap.application_id # 装修申请违规记录表
        LEFT JOIN t_decoration_applicate_accept_relation tdar
        ON tdai.id = tdar.application_id # 装修申请和验收项目关系表
        LEFT JOIN t_decoration_application_accept tdaa ON tdar.accept_id = tdaa.id # 装修申请验收项目表
        LEFT JOIN t_property_resource_info tpri ON tdai.room_code = tpri.id # 房间信息
        LEFT JOIN t_property_resource_info tpri2 ON tpri.parent_id = tpri2.id # 楼层信息
        LEFT JOIN t_property_resource_info tpri3 ON tpri2.parent_id = tpri3.id # 楼宇信息

    </sql>

    <select id="selectDecorationAcceptanceInfoList" parameterType="DecorationAcceptanceInfo"
            resultMap="DecorationAcceptanceInfoResult">
        select tdai.id as apply_id,
        tdai.room_code,
        tdai.applyer as applicant,
        tdai.applyer_name as applicant_name,
        tdai.status,
        CASE
        WHEN tdai.status = '4' THEN '合格'
        WHEN tdai.status = '5' THEN '不合格'
        ELSE ''
        END AS result,
        CASE
        WHEN tdai.status = '1' THEN '处理中'
        WHEN tdai.status = '3' THEN '验收中'
        WHEN tdai.status = '4' THEN '已完成'
        WHEN tdai.status = '5' THEN '已完成'
        ELSE ''
        END AS statusName,
        tdai.remark,
        tdai.create_time,

        tpri.name as roomName, # 房间名称
        tpri3.name as buildingName # 楼宇名称

        from t_decoration_application_info tdai
        left join t_property_resource_info tpri on tdai.room_code = tpri.id # 房间信息
        left join t_property_resource_info tpri2 on tpri.parent_id = tpri2.id # 楼层信息
        left join t_property_resource_info tpri3 on tpri2.parent_id = tpri3.id # 楼宇信息
        <where>
            and tdai.status in (4,5)
            <if test="roomCode != null  and roomCode != ''">and tdai.room_code = #{roomCode}</if>
            <if test="roomName != null  and roomName != ''">and tpri.name like concat('%',#{roomName},'%')</if>
            <if test="applicant != null ">and tdai.applyer = #{applicant}</if>
            <if test="applicantName != null and applicantName != ''">and tdai.applyer_name like
                concat('%',#{applicantName},'%')
            </if>
            <if test="status != null ">and tdai.status = #{status}</if>
            <if test="phone != null  and phone != ''">and tdai.phone = #{phone}</if>
            <!--            <if test="appointmentTimeStart != null ">and DATE_FORMAT(tdai.appointment_time,'%y-%m-%d') >=-->
            <!--                DATE_FORMAT(#{appointmentTimeStart},'%y-%m-%d')-->
            <!--            </if>-->
            <!--            <if test="appointmentTimeEnd != null ">and DATE_FORMAT(tdai.appointment_time,'%y-%m-%d') &lt;=-->
            <!--                DATE_FORMAT(#{appointmentTimeEnd},'%y-%m-%d')-->
            <!--            </if>-->
        </where>
        order by tdai.create_time desc
    </select>

    <select id="selectDecorationAcceptanceInfoByApplyId" parameterType="String"
            resultMap="DecorationAcceptanceInfoResult">
        <include refid="selectDecorationAcceptanceInfoVo"/>
        where tdai.id = #{applyId}
    </select>

    <insert id="insertDecorationAcceptanceInfo" parameterType="DecorationAcceptanceInfo">
        insert into t_decoration_acceptance_info
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="applyId != null">apply_id,</if>
            <if test="roomCode != null and roomCode != ''">room_code,</if>
            <if test="applicant != null">applicant,</if>
            <if test="applicantName != null">applicant_name,</if>
            <if test="status != null">status,</if>
            <if test="phone != null and phone != ''">phone,</if>
            <if test="appointmentTime != null">appointment_time,</if>
            <if test="remark != null">remark,</if>
            <if test="createTime != null">create_time,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="applyId != null">#{applyId},</if>
            <if test="roomCode != null and roomCode != ''">#{roomCode},</if>
            <if test="applicant != null">#{applicant},</if>
            <if test="applicantName != null">#{applicantName},</if>
            <if test="status != null">#{status},</if>
            <if test="phone != null and phone != ''">#{phone},</if>
            <if test="appointmentTime != null">#{appointmentTime},</if>
            <if test="remark != null">#{remark},</if>
            <if test="createTime != null">#{createTime},</if>
        </trim>
    </insert>

    <update id="updateDecorationAcceptanceInfo" parameterType="DecorationAcceptanceInfo">
        update t_decoration_application_info
        <trim prefix="SET" suffixOverrides=",">
            <if test="roomCode != null and roomCode != ''">room_code = #{roomCode},</if>
            <if test="applicant != null">applicant = #{applicant},</if>
            <if test="applicantName != null">applicant_name = #{applicantName},</if>
            <if test="status != null">status = #{status},</if>
            <if test="phone != null and phone != ''">phone = #{phone},</if>
            <if test="appointmentTime != null">appointment_time = #{appointmentTime},</if>
            <if test="remark != null">remark = #{remark},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
        </trim>
        where apply_id = #{applyId}
    </update>
    <update id="resetDecorationApplicationInfo">
        UPDATE t_decoration_application_info
        SET status = '3'
        WHERE id = #{applyId}
    </update>

    <delete id="deleteDecorationAcceptanceInfoByApplyId" parameterType="String">
        DELETE
        FROM t_decoration_acceptance_info
        WHERE apply_id = #{applyId}
    </delete>

    <delete id="deleteDecorationAcceptanceInfoByApplyIds" parameterType="String">
        delete from t_decoration_acceptance_info where apply_id in
        <foreach item="applyId" collection="array" open="(" separator="," close=")">
            #{applyId}
        </foreach>
    </delete>
    <update id="resetDecorationApplicationRelation">
        update t_decoration_applicate_accept_relation
        set status = null
        WHERE application_id = #{applyId}
    </update>
    <delete id="deleteDecorationApplicationProblem">
        DELETE
        FROM t_decoration_application_problem
        WHERE application_id = #{applyId}
    </delete>
</mapper>
