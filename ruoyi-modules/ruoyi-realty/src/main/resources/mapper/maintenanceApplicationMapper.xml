<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ibms.service.realty.web.mapper.MaintenanceApplicationMapper">
    <!--    `auditor_id` int(11) DEFAULT NULL COMMENT '审核人id',-->
    <!--    `auditor` varchar(255) DEFAULT NULL COMMENT '审核人',-->
    <!--    `audit_time` datetime DEFAULT NULL COMMENT '审核时间',-->
    <!--    -->
    <!--    `return_time` datetime DEFAULT NULL COMMENT '还车时间',-->
    <!--    `actual_costs` decimal(10,2) DEFAULT NULL COMMENT '实际费用',-->
    <!--    `return_person_id` int(11) DEFAULT NULL COMMENT '还车人id',-->
    <!--    `return_person` varchar(255) DEFAULT NULL COMMENT '还车人',-->
    <!--    `return_remarks` varchar(255) DEFAULT NULL COMMENT '还车备注',-->
    <!--    `return_attachment` varchar(255) DEFAULT NULL COMMENT '还车附件',-->
    <resultMap type="MaintenanceApplication" id="MaintenanceApplicationResult">
        <result property="id" column="id"/>
        <result property="applicationNumber" column="application_number"/>
        <result property="vehicleId" column="vehicle_id"/>
        <result property="maintenanceType" column="maintenance_type"/>
        <result property="maintenanceDate" column="maintenance_date"/>
        <result property="maintenanceProject" column="maintenance_project"/>
        <result property="sendRepairTime" column="send_repair_time"/>
        <result property="estimatedReturnTime" column="estimated_return_time"/>
        <result property="estimatedCost" column="estimated_cost"/>
        <result property="repairShopName" column="repair_shop_name"/>
        <result property="sendRepairDriver" column="send_repair_driver"/>
        <result property="contact" column="contact"/>
        <result property="status" column="status"/>
        <result property="auditorId" column="auditor_id"/>
        <result property="auditor" column="auditor"/>
        <result property="auditTime" column="audit_time"/>
        <result property="creatorId" column="creator_id"/>
        <result property="creator" column="creator"/>
        <result property="createTime" column="create_time"/>
        <result property="remark" column="remark"/>
        <result property="attachmentUrl" column="attachment_url"/>
        <result property="returnTime" column="return_time"/>
        <result property="actualCosts" column="actual_costs"/>
        <result property="returnPersonId" column="return_person_id"/>
        <result property="returnPerson" column="return_person"/>
        <result property="returnRemarks" column="return_remarks"/>
        <result property="returnAttachment" column="return_attachment"/>


        <result property="licensePlateNumber" column="license_plate_number"/>
    </resultMap>

    <sql id="selectMaintenanceApplicationVo">
        SELECT id,
        application_number,
        vehicle_id,
        maintenance_type,
        maintenance_date,
        maintenance_project,
        send_repair_time,
        estimated_return_time,
        estimated_cost,
        repair_shop_name,
        send_repair_driver,
        contact,
        status,
        auditor_id,
        auditor,
        audit_time,
        creator_id,
        creator,
        create_time,
        remark,
        attachment_url,
        return_time,
        actual_costs,
        return_person_id,
        return_person,
        return_remarks,
        return_attachment
        FROM t_maintenance_application
    </sql>

    <select id="selectMaintenanceApplicationList" parameterType="MaintenanceApplication"
            resultMap="MaintenanceApplicationResult">
        SELECT tma.id,
        tma.application_number,
        tma.vehicle_id,
        tma.maintenance_type,
        tma.maintenance_date,
        tma.maintenance_project,
        tma.send_repair_time,
        tma.estimated_return_time,
        tma.estimated_cost,
        tma.repair_shop_name,
        tma.send_repair_driver,
        tma.contact,
        tma.status,
        tma.auditor_id,
        tma.auditor,
        tma.audit_time,
        tma.creator_id,
        tma.creator,
        tma.create_time,
        tma.remark,
        tma.attachment_url,
        tma.return_time,
        tma.actual_costs,
        tma.return_person_id,
        tma.return_person,
        tma.return_remarks,
        tma.return_attachment,

        tvi.license_plate_number
        FROM t_maintenance_application tma
        LEFT JOIN t_vehicle_information tvi ON tma.vehicle_id = tvi.id
        <where>
            <if test="applicationNumber != null  and applicationNumber != ''">and tma.application_number like
                concat('%',
                #{applicationNumber}, '%')
            </if>
            <if test="licensePlateNumber != null  and licensePlateNumber != ''">and tvi.license_plate_number like
                concat('%',
                #{licensePlateNumber}, '%')
            </if>
            <if test="vehicleId != null ">and tma.vehicle_id = #{vehicleId}</if>
            <if test="maintenanceType != null  and maintenanceType != ''">and tma.maintenance_type =
                #{maintenanceType}
            </if>
            <if test="maintenanceDate != null ">and tma.maintenance_date = #{maintenanceDate}</if>
            <if test="maintenanceProject != null  and maintenanceProject != ''">and tma.maintenance_project =
                #{maintenanceProject}
            </if>
            <if test="sendRepairTime != null ">and tma.send_repair_time = #{sendRepairTime}</if>
            <if test="estimatedReturnTime != null ">and tma.estimated_return_time = #{estimatedReturnTime}</if>
            <if test="estimatedCost != null ">and tma.estimated_cost = #{estimatedCost}</if>
            <if test="repairShopName != null  and repairShopName != ''">and tma.repair_shop_name like concat('%',
                #{repairShopName}, '%')
            </if>
            <if test="sendRepairDriver != null  and sendRepairDriver != ''">and tma.send_repair_driver =
                #{sendRepairDriver}
            </if>
            <if test="contact != null  and contact != ''">and tma.contact = #{contact}</if>
            <if test="status != null  and status != ''">and tma.status = #{status}</if>
            <if test="creatorId != null ">and tma.creator_id = #{creatorId}</if>
            <if test="creator != null  and creator != ''">and tma.creator like concat('%', #{creator}, '%')</if>
            <if test="attachmentUrl != null  and attachmentUrl != ''">and tma.attachment_url = #{attachmentUrl}</if>
        </where>
        order by tma.create_time desc
    </select>

    <select id="selectMaintenanceApplicationById" parameterType="Integer" resultMap="MaintenanceApplicationResult">
        <include refid="selectMaintenanceApplicationVo"/>
        where id = #{id}
    </select>

    <insert id="insertMaintenanceApplication" parameterType="MaintenanceApplication" useGeneratedKeys="true"
            keyProperty="id">
        insert into t_maintenance_application
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="applicationNumber != null">application_number,</if>
            <if test="vehicleId != null">vehicle_id,</if>
            <if test="maintenanceType != null">maintenance_type,</if>
            <if test="maintenanceDate != null">maintenance_date,</if>
            <if test="maintenanceProject != null">maintenance_project,</if>
            <if test="sendRepairTime != null">send_repair_time,</if>
            <if test="estimatedReturnTime != null">estimated_return_time,</if>
            <if test="estimatedCost != null">estimated_cost,</if>
            <if test="repairShopName != null">repair_shop_name,</if>
            <if test="sendRepairDriver != null">send_repair_driver,</if>
            <if test="contact != null">contact,</if>
            <if test="status != null and status != ''">status,</if>
            <if test="auditorId != null">auditor_id,</if>
            <if test="auditor != null">auditor,</if>
            <if test="auditTime != null">audit_time,</if>
            <if test="creatorId != null">creator_id,</if>
            <if test="creator != null">creator,</if>
            <if test="createTime != null">create_time,</if>
            <if test="remark != null">remark,</if>
            <if test="attachmentUrl != null">attachment_url,</if>
            <if test="returnTime != null">return_time,</if>
            <if test="actualCosts != null">actual_costs,</if>
            <if test="returnPersonId != null">return_person_id,</if>
            <if test="returnPerson != null">return_person,</if>
            <if test="returnRemarks != null">return_remarks,</if>
            <if test="returnAttachment != null">return_attachment,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="applicationNumber != null">#{applicationNumber},</if>
            <if test="vehicleId != null">#{vehicleId},</if>
            <if test="maintenanceType != null">#{maintenanceType},</if>
            <if test="maintenanceDate != null">#{maintenanceDate},</if>
            <if test="maintenanceProject != null">#{maintenanceProject},</if>
            <if test="sendRepairTime != null">#{sendRepairTime},</if>
            <if test="estimatedReturnTime != null">#{estimatedReturnTime},</if>
            <if test="estimatedCost != null">#{estimatedCost},</if>
            <if test="repairShopName != null">#{repairShopName},</if>
            <if test="sendRepairDriver != null">#{sendRepairDriver},</if>
            <if test="contact != null">#{contact},</if>
            <if test="status != null and status != ''">#{status},</if>
            <if test="auditorId != null">#{auditorId},</if>
            <if test="auditor != null">#{auditor},</if>
            <if test="auditTime != null">#{auditTime},</if>
            <if test="creatorId != null">#{creatorId},</if>
            <if test="creator != null">#{creator},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="remark != null">#{remark},</if>
            <if test="attachmentUrl != null">#{attachmentUrl},</if>
            <if test="returnTime != null">#{returnTime},</if>
            <if test="actualCosts != null">#{actualCosts},</if>
            <if test="returnPersonId != null">#{returnPersonId},</if>
            <if test="returnPerson != null">#{returnPerson},</if>
            <if test="returnRemarks != null">#{returnRemarks},</if>
            <if test="returnAttachment != null">#{returnAttachment},</if>
        </trim>
    </insert>

    <update id="updateMaintenanceApplication" parameterType="MaintenanceApplication">
        update t_maintenance_application
        <trim prefix="SET" suffixOverrides=",">
            <if test="applicationNumber != null">application_number = #{applicationNumber},</if>
            <if test="vehicleId != null">vehicle_id = #{vehicleId},</if>
            <if test="maintenanceType != null">maintenance_type = #{maintenanceType},</if>
            <if test="maintenanceDate != null">maintenance_date = #{maintenanceDate},</if>
            <if test="sendRepairTime != null">send_repair_time = #{sendRepairTime},</if>
            <if test="estimatedReturnTime != null">estimated_return_time = #{estimatedReturnTime},</if>
            <if test="status != null">status = #{status},</if>
            <if test="auditorId != null">auditor_id = #{auditorId},</if>
            <if test="auditor != null">auditor = #{auditor},</if>
            <if test="auditTime != null">audit_time = #{auditTime},</if>
            <if test="creatorId != null">creator_id = #{creatorId},</if>
            <if test="creator != null">creator = #{creator},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="returnTime != null">return_time = #{returnTime},</if>
            <if test="actualCosts != null">actual_costs = #{actualCosts},</if>
            <if test="returnPersonId != null">return_person_id = #{returnPersonId},</if>
            <if test="returnPerson != null">return_person = #{returnPerson},</if>
            <if test="returnRemarks != null">return_remarks = #{returnRemarks},</if>
            <if test="returnAttachment != null">return_attachment = #{returnAttachment},</if>
            maintenance_project = #{maintenanceProject},
            estimated_cost = #{estimatedCost},
            repair_shop_name = #{repairShopName},
            send_repair_driver = #{sendRepairDriver},
            contact = #{contact},
            remark = #{remark},
            attachment_url = #{attachmentUrl},

        </trim>
        where id = #{id}
    </update>

    <delete id="deleteMaintenanceApplicationById" parameterType="Integer">
        DELETE
        FROM t_maintenance_application
        WHERE id = #{id}
    </delete>

    <delete id="deleteMaintenanceApplicationByIds" parameterType="String">
        delete from t_maintenance_application where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>
