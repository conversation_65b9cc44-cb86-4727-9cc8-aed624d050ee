<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ibms.service.realty.web.mapper.DutyPlanMapper">

    <resultMap type="DutyPlan" id="DutyPlanResult">
        <result property="id" column="id"/>
        <result property="name" column="name"/>
        <result property="restDay" column="rest_day"/>
        <result property="startTime" column="start_time"/>
        <result property="endTime" column="end_time"/>
        <result property="remark" column="remark"/>
    </resultMap>

    <sql id="selectDutyPlanVo">
        select id, name, rest_day, start_time, end_time, remark
        from t_duty_plan
    </sql>

    <select id="selectDutyPlanList" parameterType="DutyPlan" resultMap="DutyPlanResult">
        <include refid="selectDutyPlanVo"/>
        <where>
            <if test="name != null  and name != ''">and name like concat('%', #{name}, '%')</if>
            <if test="restDay != null  and restDay != ''">and rest_day = #{restDay}</if>
            <if test="startTime != null  and startTime != ''">and start_time = #{startTime}</if>
            <if test="endTime != null  and endTime != ''">and end_time = #{endTime}</if>
        </where>
        order by id desc
    </select>

    <select id="selectDutyPlanById" parameterType="Integer" resultMap="DutyPlanResult">
        <include refid="selectDutyPlanVo"/>
        where id = #{id}
    </select>

    <insert id="insertDutyPlan" parameterType="DutyPlan" useGeneratedKeys="true" keyProperty="id">
        insert into t_duty_plan
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="name != null">name,</if>
            <if test="restDay != null">rest_day,</if>
            <if test="startTime != null">start_time,</if>
            <if test="endTime != null">end_time,</if>
            <if test="remark != null">remark,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="name != null">#{name},</if>
            <if test="restDay != null">#{restDay},</if>
            <if test="startTime != null">#{startTime},</if>
            <if test="endTime != null">#{endTime},</if>
            <if test="remark != null">#{remark},</if>
        </trim>
    </insert>

    <update id="updateDutyPlan" parameterType="DutyPlan">
        update t_duty_plan
        <trim prefix="SET" suffixOverrides=",">
            <if test="name != null">name = #{name},</if>
            <if test="restDay != null">rest_day = #{restDay},</if>
            <if test="startTime != null">start_time = #{startTime},</if>
            <if test="endTime != null">end_time = #{endTime},</if>
            <if test="remark != null">remark = #{remark},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteDutyPlanById" parameterType="Integer">
        delete from t_duty_plan where id = #{id}
    </delete>

    <delete id="deleteDutyPlanByIds" parameterType="String">
        delete from t_duty_plan where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>
