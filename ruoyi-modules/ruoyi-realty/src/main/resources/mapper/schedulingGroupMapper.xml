<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ibms.service.realty.web.mapper.SchedulingGroupMapper">

    <resultMap type="SchedulingGroup" id="SchedulingGroupResult">
        <result property="id" column="id"/>
        <result property="name" column="name"/>
        <result property="cycleId" column="cycle_id"/>
        <result property="calendarId" column="calendar_id"/>
        <result property="firstScheduleDate" column="first_schedule_date"/>

        <result property="cycleName" column="cycle_name"/>
        <result property="shiftIds" column="shift_ids"/>

        <result property="calendarName" column="calendar_name"/>
        <result property="legalHolidayAutoRest" column="legal_holiday_auto_rest"/>
        <result property="restDayMode" column="rest_day_mode"/>
        <result property="restDays" column="rest_days"/>

        <collection property="employeeGroupList" ofType="EmployeeGroup">
            <result property="id" column="employee_group_id"/>
            <result property="employeeId" column="employee_id"/>
            <result property="employee" column="employee"/>
            <result property="groupId" column="group_id"/>
        </collection>
    </resultMap>

    <sql id="selectSchedulingGroupVo">
        SELECT tsg.id,
        tsg.name,
        tsg.cycle_id,
        tsg.calendar_id,
        tsg.first_schedule_date,

        teg.id AS employee_group_id,
        teg.employee_id,
        teg.employee,
        teg.group_id,

        tcm.name AS cycle_name,
        tcm.shift_ids, #周期内班次，多个对应班次管理表id，逗号隔开

        tcam.name AS calendar_name,
        tcam.legal_holiday_auto_rest, #法定节假日自动排休，0：开，1：关
        tcam.rest_day_mode, #休息日设置，字典：realty_calendar_management_rest_day_mode；0：按周，1：按月，2：无固定休息日
        tcam.rest_days #每周/月休息日，多个逗号分割
        FROM t_scheduling_group tsg
        LEFT JOIN t_employee_group teg ON tsg.id = teg.group_id
        LEFT JOIN t_cycle_management tcm ON tsg.cycle_id = tcm.id
        LEFT JOIN t_calendar_management tcam ON tsg.calendar_id = tcam.id

    </sql>

    <select id="selectSchedulingGroupList" parameterType="SchedulingGroup" resultMap="SchedulingGroupResult">
        <include refid="selectSchedulingGroupVo"/>
        <where>
            <if test="name != null  and name != ''">and tsg.name like concat('%', #{name}, '%')</if>
            <if test="employeeNameParam != null  and employeeNameParam != ''">
                and tsg.id in (select group_id from t_employee_group where employee LIKE concat(
                '%',
                #{employeeNameParam},
                '%'))
            </if>
        </where>
        order by tsg.id desc
    </select>

    <select id="selectSchedulingGroupById" parameterType="Integer" resultMap="SchedulingGroupResult">
        <include refid="selectSchedulingGroupVo"/>
        where tsg.id = #{id}
    </select>
    <select id="selectEmployeeGroupByUserId" resultType="com.ibms.service.realty.web.domain.EmployeeGroup">
        SELECT *
        FROM t_employee_group
        WHERE employee_id = #{userId}
    </select>
    <select id="selectLegalHoliday" resultType="com.ibms.service.realty.web.domain.BaseHolidays">
        SELECT *
        FROM t_base_holidays
        WHERE status = 1
    </select>

    <insert id="insertSchedulingGroup" parameterType="SchedulingGroup" useGeneratedKeys="true" keyProperty="id">
        insert into t_scheduling_group
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="name != null and name != ''">name,</if>
            <if test="cycleId != null">cycle_id,</if>
            <if test="calendarId != null">calendar_id,</if>
            <if test="firstScheduleDate != null">first_schedule_date,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="name != null and name != ''">#{name},</if>
            <if test="cycleId != null">#{cycleId},</if>
            <if test="calendarId != null">#{calendarId},</if>
            <if test="firstScheduleDate != null">#{firstScheduleDate},</if>
        </trim>
    </insert>

    <update id="updateSchedulingGroup" parameterType="SchedulingGroup">
        update t_scheduling_group
        <trim prefix="SET" suffixOverrides=",">
            <if test="name != null and name != ''">name = #{name},</if>
            <if test="cycleId != null">cycle_id = #{cycleId},</if>
            <if test="calendarId != null">calendar_id = #{calendarId},</if>
            <if test="firstScheduleDate != null">first_schedule_date = #{firstScheduleDate},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteSchedulingGroupById" parameterType="Integer">
        DELETE
        FROM t_scheduling_group
        WHERE id = #{id}
    </delete>

    <delete id="deleteSchedulingGroupByIds" parameterType="String">
        delete from t_scheduling_group where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
    <delete id="deleteEmployeeGroupByGroupId">
        DELETE
        FROM t_employee_group
        WHERE group_id = #{groupId}
    </delete>
    <delete id="deleteEmployeeGroupByGroupIds">
        DELETE
        FROM t_employee_group
        WHERE group_id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>


    <!--    CREATE TABLE `t_employee_group` (-->
    <!--    `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键',-->
    <!--    `employee_id` int(11) DEFAULT NULL COMMENT '员工id,对应用户表id',-->
    <!--    `employee` varchar(20) DEFAULT NULL COMMENT '员工名字',-->
    <!--    `group_id` int(11) DEFAULT NULL COMMENT '排班分组id',-->
    <!--    PRIMARY KEY (`id`)-->
    <!--    ) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='员工排班分组表';-->

    <!--    批量插入员工排班分组-->
    <insert id="insertEmployeeGroupList" parameterType="java.util.List">
        insert into t_employee_group(employee_id, employee, group_id)
        values
        <foreach collection="list" item="item" index="index" separator=",">
            (#{item.employeeId}, #{item.employee}, #{item.groupId})
        </foreach>
    </insert>
</mapper>
