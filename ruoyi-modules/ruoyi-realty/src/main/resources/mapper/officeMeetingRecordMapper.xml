<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ibms.service.realty.web.mapper.OfficeMeetingRecordMapper">

    <resultMap type="OfficeMeetingRecord" id="OfficeMeetingRecordResult">
        <result property="id" column="id"/>
        <result property="sequenceNumber" column="sequence_number"/>
        <result property="meetingName" column="meeting_name"/>
        <result property="meetingCategoryId" column="meeting_category_id"/>
        <result property="meetingTheme" column="meeting_theme"/>
        <result property="meetingContent" column="meeting_content"/>
        <result property="departmentId" column="department_id"/>
        <result property="departmentName" column="department_name"/>
        <result property="location" column="location"/>
        <result property="startTime" column="start_time"/>
        <result property="endTime" column="end_time"/>
        <result property="convenerId" column="convener_id"/>
        <result property="convener" column="convener"/>
        <result property="hostId" column="host_id"/>
        <result property="host" column="host"/>
        <result property="participantsIds" column="participants_ids"/>
        <result property="participants" column="participants"/>
        <result property="remark" column="remark"/>
        <result property="attachmentUrl" column="attachment_url"/>
        <result property="creatorId" column="creator_id"/>
        <result property="creator" column="creator"/>
        <result property="createTime" column="create_time"/>

        <result property="meetingCategoryName" column="meeting_category_name"/>
    </resultMap>

    <sql id="selectOfficeMeetingRecordVo">
        SELECT id,
        sequence_number,
        meeting_name,
        meeting_category_id,
        meeting_theme,
        meeting_content,
        department_id,
        department_name,
        location,
        start_time,
        end_time,
        convener_id,
        convener,
        host_id,
        host,
        participants_ids,
        participants,
        remark,
        attachment_url,
        creator_id,
        creator,
        create_time
        FROM t_office_meeting_record
    </sql>

    <select id="selectOfficeMeetingRecordList" parameterType="OfficeMeetingRecord"
            resultMap="OfficeMeetingRecordResult">
        SELECT tomr.id,
        tomr.sequence_number,
        tomr.meeting_name,
        tomr.meeting_category_id,
        tomr.meeting_theme,
        tomr.meeting_content,
        tomr.department_id,
        tomr.department_name,
        tomr.location,
        tomr.start_time,
        tomr.end_time,
        tomr.convener_id,
        tomr.convener,
        tomr.host_id,
        tomr.host,
        tomr.participants_ids,
        tomr.participants,
        tomr.remark,
        tomr.attachment_url,
        tomr.creator_id,
        tomr.creator,
        tomr.create_time,

        toc.name as meeting_category_name
        FROM t_office_meeting_record tomr
        LEFT JOIN t_office_category toc ON tomr.meeting_category_id = toc.id

        <where>
            <if test="sequenceNumber != null  and sequenceNumber != ''">and tomr.sequence_number = #{sequenceNumber}
            </if>
            <if test="meetingName != null  and meetingName != ''">and tomr.meeting_name like concat('%', #{meetingName},
                '%')
            </if>
            <if test="meetingCategoryId != null ">and tomr.meeting_category_id = #{meetingCategoryId}</if>
            <if test="meetingTheme != null  and meetingTheme != ''">and tomr.meeting_theme = #{meetingTheme}</if>
            <if test="meetingContent != null  and meetingContent != ''">and tomr.meeting_content = #{meetingContent}
            </if>
            <if test="departmentId != null ">and tomr.department_id = #{departmentId}</if>
            <if test="departmentName != null  and departmentName != ''">and tomr.department_name like concat('%',
                #{departmentName}, '%')
            </if>
            <if test="location != null  and location != ''">and tomr.location = #{location}</if>
            <if test="startTime != null ">and DATE_FORMAT(tomr.start_time,'%y-%m-%d') >=
                DATE_FORMAT(#{startTime},'%y-%m-%d')
            </if>
            <if test="endTime != null ">and DATE_FORMAT(tomr.end_time,'%y-%m-%d')
                <![CDATA[<= DATE_FORMAT(#{endTime},'%y-%m-%d')]]></if>
            <if test="convenerId != null ">and tomr.convener_id = #{convenerId}</if>
            <if test="convener != null  and convener != ''">and tomr.convener = #{convener}</if>
            <if test="hostId != null ">and tomr.host_id = #{hostId}</if>
            <if test="host != null  and host != ''">and tomr.host = #{host}</if>
            <if test="participantsIds != null  and participantsIds != ''">and tomr.participants_ids =
                #{participantsIds}
            </if>
            <if test="participants != null  and participants != ''">and tomr.participants = #{participants}</if>
            <if test="attachmentUrl != null  and attachmentUrl != ''">and tomr.attachment_url = #{attachmentUrl}</if>
            <if test="creatorId != null ">and tomr.creator_id = #{creatorId}</if>
            <if test="creator != null  and creator != ''">and tomr.creator = #{creator}</if>
        </where>
        order by tomr.create_time desc
    </select>

    <select id="selectOfficeMeetingRecordById" parameterType="Integer" resultMap="OfficeMeetingRecordResult">
        <include refid="selectOfficeMeetingRecordVo"/>
        where id = #{id}
    </select>

    <insert id="insertOfficeMeetingRecord" parameterType="OfficeMeetingRecord" useGeneratedKeys="true" keyProperty="id">
        insert into t_office_meeting_record
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="sequenceNumber != null">sequence_number,</if>
            <if test="meetingName != null">meeting_name,</if>
            <if test="meetingCategoryId != null">meeting_category_id,</if>
            <if test="meetingTheme != null">meeting_theme,</if>
            <if test="meetingContent != null">meeting_content,</if>
            <if test="departmentId != null">department_id,</if>
            <if test="departmentName != null">department_name,</if>
            <if test="location != null">location,</if>
            <if test="startTime != null">start_time,</if>
            <if test="endTime != null">end_time,</if>
            <if test="convenerId != null">convener_id,</if>
            <if test="convener != null">convener,</if>
            <if test="hostId != null">host_id,</if>
            <if test="host != null">host,</if>
            <if test="participantsIds != null">participants_ids,</if>
            <if test="participants != null">participants,</if>
            <if test="remark != null">remark,</if>
            <if test="attachmentUrl != null">attachment_url,</if>
            <if test="creatorId != null">creator_id,</if>
            <if test="creator != null">creator,</if>
            <if test="createTime != null">create_time,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="sequenceNumber != null">#{sequenceNumber},</if>
            <if test="meetingName != null">#{meetingName},</if>
            <if test="meetingCategoryId != null">#{meetingCategoryId},</if>
            <if test="meetingTheme != null">#{meetingTheme},</if>
            <if test="meetingContent != null">#{meetingContent},</if>
            <if test="departmentId != null">#{departmentId},</if>
            <if test="departmentName != null">#{departmentName},</if>
            <if test="location != null">#{location},</if>
            <if test="startTime != null">#{startTime},</if>
            <if test="endTime != null">#{endTime},</if>
            <if test="convenerId != null">#{convenerId},</if>
            <if test="convener != null">#{convener},</if>
            <if test="hostId != null">#{hostId},</if>
            <if test="host != null">#{host},</if>
            <if test="participantsIds != null">#{participantsIds},</if>
            <if test="participants != null">#{participants},</if>
            <if test="remark != null">#{remark},</if>
            <if test="attachmentUrl != null">#{attachmentUrl},</if>
            <if test="creatorId != null">#{creatorId},</if>
            <if test="creator != null">#{creator},</if>
            <if test="createTime != null">#{createTime},</if>
        </trim>
    </insert>

    <update id="updateOfficeMeetingRecord" parameterType="OfficeMeetingRecord">
        update t_office_meeting_record
        <trim prefix="SET" suffixOverrides=",">
            <if test="sequenceNumber != null">sequence_number = #{sequenceNumber},</if>
            <if test="meetingName != null">meeting_name = #{meetingName},</if>
            <if test="meetingCategoryId != null">meeting_category_id = #{meetingCategoryId},</if>
            <if test="meetingTheme != null">meeting_theme = #{meetingTheme},</if>
            <if test="meetingContent != null">meeting_content = #{meetingContent},</if>
            <if test="departmentId != null">department_id = #{departmentId},</if>
            <if test="departmentName != null">department_name = #{departmentName},</if>
            <if test="participantsIds != null">participants_ids = #{participantsIds},</if>
            <if test="participants != null">participants = #{participants},</if>
            <if test="remark != null">remark = #{remark},</if>
            <if test="attachmentUrl != null">attachment_url = #{attachmentUrl},</if>
            <if test="creatorId != null">creator_id = #{creatorId},</if>
            <if test="creator != null">creator = #{creator},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            location = #{location},
            start_time = #{startTime},
            end_time = #{endTime},
            convener_id = #{convenerId},
            convener = #{convener},
            host_id = #{hostId},
            host = #{host},
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteOfficeMeetingRecordById" parameterType="Integer">
        DELETE
        FROM t_office_meeting_record
        WHERE id = #{id}
    </delete>

    <delete id="deleteOfficeMeetingRecordByIds" parameterType="String">
        delete from t_office_meeting_record where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>
