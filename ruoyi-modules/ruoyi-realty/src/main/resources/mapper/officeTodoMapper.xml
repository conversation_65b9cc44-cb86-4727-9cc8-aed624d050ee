<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ibms.service.realty.web.mapper.OfficeTodoMapper">

    <resultMap type="OfficeTodo" id="OfficeTodoResult">
        <result property="id" column="id"/>
        <result property="subject" column="subject"/>
        <result property="content" column="content"/>
        <result property="endTime" column="end_time"/>
        <result property="assigneeId" column="assignee_id"/>
        <result property="assigneeName" column="assignee_name"/>
        <result property="attachmentUrl" column="attachment_url"/>
        <result property="status" column="status"/>
    </resultMap>

    <sql id="selectOfficeTodoVo">
        SELECT id,
        subject,
        content,
        end_time,
        assignee_id,
        assignee_name,
        attachment_url,
        status
        FROM t_office_todo
    </sql>

    <select id="selectOfficeTodoList" parameterType="OfficeTodo" resultMap="OfficeTodoResult">
        <include refid="selectOfficeTodoVo"/>
        <where>
            <if test="subject != null  and subject != ''">and subject like concat('%', #{subject}, '%')</if>
            <if test="content != null  and content != ''">and content = #{content}</if>
            <if test="endTime != null ">and end_time = #{endTime}</if>
            <if test="assigneeId != null ">and assignee_id = #{assigneeId}</if>
            <if test="assigneeName != null  and assigneeName != ''">and assignee_name like concat('%', #{assigneeName},
                '%')
            </if>
            <if test="attachmentUrl != null  and attachmentUrl != ''">and attachment_url = #{attachmentUrl}</if>
            <if test="status != null  and status != ''">and status = #{status}</if>
        </where>
        order by id desc
    </select>

    <select id="selectOfficeTodoById" parameterType="Integer" resultMap="OfficeTodoResult">
        <include refid="selectOfficeTodoVo"/>
        where id = #{id}
    </select>

    <insert id="insertOfficeTodo" parameterType="OfficeTodo" useGeneratedKeys="true" keyProperty="id">
        insert into t_office_todo
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="subject != null">subject,</if>
            <if test="content != null">content,</if>
            <if test="endTime != null">end_time,</if>
            <if test="assigneeId != null">assignee_id,</if>
            <if test="assigneeName != null">assignee_name,</if>
            <if test="attachmentUrl != null">attachment_url,</if>
            <if test="status != null">status,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="subject != null">#{subject},</if>
            <if test="content != null">#{content},</if>
            <if test="endTime != null">#{endTime},</if>
            <if test="assigneeId != null">#{assigneeId},</if>
            <if test="assigneeName != null">#{assigneeName},</if>
            <if test="attachmentUrl != null">#{attachmentUrl},</if>
            <if test="status != null">#{status},</if>
        </trim>
    </insert>

    <update id="updateOfficeTodo" parameterType="OfficeTodo">
        update t_office_todo
        <trim prefix="SET" suffixOverrides=",">
            subject = #{subject},
            content = #{content},
            end_time = #{endTime},
            assignee_id = #{assigneeId},
            assignee_name = #{assigneeName},
            attachment_url = #{attachmentUrl},
            <if test="status != null">status = #{status},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteOfficeTodoById" parameterType="Integer">
        DELETE
        FROM t_office_todo
        WHERE id = #{id}
    </delete>

    <delete id="deleteOfficeTodoByIds" parameterType="String">
        delete from t_office_todo where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>
