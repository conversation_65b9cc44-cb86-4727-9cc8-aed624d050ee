<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ibms.service.realty.web.mapper.KnowledgeBaseMapper">

    <resultMap type="KnowledgeBase" id="KnowledgeBaseResult">
        <result property="id" column="id"/>
        <result property="title" column="title"/>
        <result property="content" column="content"/>
        <result property="parentId" column="parent_id"/>
        <result property="createDate" column="create_date"/>
    </resultMap>

    <sql id="selectKnowledgeBaseVo">
        SELECT id, title, content, parent_id, create_date
        FROM t_knowledge_base
    </sql>

    <select id="selectKnowledgeBaseList" parameterType="KnowledgeBase" resultMap="KnowledgeBaseResult">
        <include refid="selectKnowledgeBaseVo"/>
        <where>
            <if test="title != null  and title != ''">and title = #{title}</if>
            <if test="content != null  and content != ''">and content = #{content}</if>
            <if test="parentId != null ">and parent_id = #{parentId}</if>
            <if test="createDate != null ">and create_date = #{createDate}</if>
        </where>
        order by create_date desc
    </select>

    <select id="selectKnowledgeBaseById" parameterType="Integer" resultMap="KnowledgeBaseResult">
        <include refid="selectKnowledgeBaseVo"/>
        where id = #{id}
    </select>

    <insert id="insertKnowledgeBase" parameterType="KnowledgeBase" useGeneratedKeys="true" keyProperty="id">
        insert into t_knowledge_base
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="title != null">title,</if>
            <if test="content != null">content,</if>
            <if test="parentId != null">parent_id,</if>
            <if test="createDate != null">create_date,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="title != null">#{title},</if>
            <if test="content != null">#{content},</if>
            <if test="parentId != null">#{parentId},</if>
            <if test="createDate != null">#{createDate},</if>
        </trim>
    </insert>

    <update id="updateKnowledgeBase" parameterType="KnowledgeBase">
        update t_knowledge_base
        <trim prefix="SET" suffixOverrides=",">
            <if test="title != null">title = #{title},</if>
            <if test="content != null">content = #{content},</if>
            <if test="parentId != null">parent_id = #{parentId},</if>
            <if test="createDate != null">create_date = #{createDate},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteKnowledgeBaseById" parameterType="Integer">
        DELETE
        FROM t_knowledge_base
        WHERE id = #{id}
    </delete>

    <delete id="deleteKnowledgeBaseByIds" parameterType="String">
        delete from t_knowledge_base where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>