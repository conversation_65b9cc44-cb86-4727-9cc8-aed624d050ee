<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ibms.service.realty.web.mapper.ItemReleaseMapper">

    <resultMap type="ItemRelease" id="ItemReleaseResult">
        <result property="id" column="id"/>
        <result property="applyerId" column="applyer_id"/>
        <result property="applyerName" column="applyer_name"/>
        <result property="applyerPhone" column="applyer_phone"/>
        <result property="unitName" column="unit_name"/>
        <result property="itemType" column="item_type"/>
        <result property="releaseStartTime" column="release_start_time"/>
        <result property="releaseEndTime" column="release_end_time"/>
        <result property="auditStatus" column="audit_status"/>
        <result property="img" column="img"/>
        <result property="roomCode" column="room_code"/>
        <result property="description" column="description"/>
        <result property="vehicleInformation" column="vehicle_information"/>

        <result property="roomName" column="roomName"/>
    </resultMap>

    <sql id="selectItemReleaseVo">
        select id,
        applyer_id,
        applyer_name,
        applyer_phone,
        unit_name,
        item_type,
        release_start_time,
        release_end_time,
        audit_status,
        img,
        room_code,
        description,
        vehicle_information
        from t_item_release
    </sql>

    <select id="selectItemReleaseList" parameterType="ItemRelease" resultMap="ItemReleaseResult">
        <include refid="selectItemReleaseVo"/>
        <where>
            <if test="applyerId != null">and applyer_id = #{applyerId}</if>
            <if test="applyerName != null  and applyerName != ''">and applyer_name like concat('%', #{applyerName},
                '%')
            </if>
            <if test="applyerPhone != null  and applyerPhone != ''">and applyer_phone like concat('%', #{applyerPhone},
                '%')
            </if>
            <if test="unitName != null  and unitName != ''">and unit_name like concat('%', #{unitName}, '%')</if>
            <if test="itemType != null ">and item_type = #{itemType}</if>
            <if test="releaseStartTime != null ">and release_start_time <![CDATA[<= #{releaseStartTime}]]></if>
            <if test="releaseEndTime != null  and releaseEndTime != ''">and release_end_time >= #{releaseEndTime}</if>
            <if test="releaseStartTimeParam != null ">and release_start_time >= #{releaseStartTimeParam}</if>
            <if test="releaseEndTimeParam != null ">and release_end_time <![CDATA[<=  #{releaseEndTimeParam}]]></if>
            <if test="auditStatus != null ">and audit_status = #{auditStatus}</if>
            <if test="img != null  and img != ''">and img = #{img}</if>
            <if test="roomCode != null ">and room_code = #{roomCode}</if>
            <if test="description != null  and description != ''">and description = #{description}</if>
            <if test="vehicleInformation != null  and vehicleInformation != ''">and vehicle_information =
                #{vehicleInformation}
            </if>
            <if test="auditStatusList != null">
                <foreach collection="auditStatusList" item="auditStatus" open="and audit_status in (" close=")"
                         separator=",">
                    #{auditStatus}
                </foreach>
            </if>
        </where>
        order by id desc
    </select>

    <select id="selectItemReleaseById" parameterType="Integer" resultMap="ItemReleaseResult">
        select tir.id,
        tir.applyer_id,
        tir.applyer_name,
        tir.applyer_phone,
        tir.unit_name,
        tir.item_type,
        tir.release_start_time,
        tir.release_end_time,
        tir.audit_status,
        tir.img,
        tir.room_code,
        tir.description,
        tir.vehicle_information,

        tpri.name as roomName
        from t_item_release tir
        LEFT JOIN t_property_resource_info tpri on tir.room_code = tpri.id # 房间信息
        where tir.id = #{id}
    </select>

    <insert id="insertItemRelease" parameterType="ItemRelease" useGeneratedKeys="true" keyProperty="id">
        insert into t_item_release
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="applyerId != null">applyer_id,</if>
            <if test="applyerName != null">applyer_name,</if>
            <if test="applyerPhone != null">applyer_phone,</if>
            <if test="unitName != null">unit_name,</if>
            <if test="itemType != null">item_type,</if>
            <if test="releaseStartTime != null">release_start_time,</if>
            <if test="releaseEndTime != null">release_end_time,</if>
            <if test="auditStatus != null">audit_status,</if>
            <if test="img != null">img,</if>
            <if test="roomCode != null">room_code,</if>
            <if test="description != null">description,</if>
            <if test="vehicleInformation != null">vehicle_information,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="applyerId != null">#{applyerId},</if>
            <if test="applyerName != null">#{applyerName},</if>
            <if test="applyerPhone != null">#{applyerPhone},</if>
            <if test="unitName != null">#{unitName},</if>
            <if test="itemType != null">#{itemType},</if>
            <if test="releaseStartTime != null">#{releaseStartTime},</if>
            <if test="releaseEndTime != null">#{releaseEndTime},</if>
            <if test="auditStatus != null">#{auditStatus},</if>
            <if test="img != null">#{img},</if>
            <if test="roomCode != null">#{roomCode},</if>
            <if test="description != null">#{description},</if>
            <if test="vehicleInformation != null">#{vehicleInformation},</if>
        </trim>
    </insert>

    <update id="updateItemRelease" parameterType="ItemRelease">
        update t_item_release
        <trim prefix="SET" suffixOverrides=",">
            <if test="applyerId != null">applyer_id = #{applyerId},</if>
            <if test="applyerName != null">applyer_name = #{applyerName},</if>
            <if test="applyerPhone != null">applyer_phone = #{applyerPhone},</if>
            <if test="unitName != null">unit_name = #{unitName},</if>
            <if test="itemType != null">item_type = #{itemType},</if>
            <if test="releaseStartTime != null">release_start_time = #{releaseStartTime},</if>
            <if test="releaseEndTime != null">release_end_time = #{releaseEndTime},</if>
            <if test="auditStatus != null">audit_status = #{auditStatus},</if>
            <if test="img != null">img = #{img},</if>
            <if test="roomCode != null">room_code = #{roomCode},</if>
            <if test="description != null">description = #{description},</if>
            <if test="vehicleInformation != null">vehicle_information = #{vehicleInformation},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteItemReleaseById" parameterType="Integer">
        delete
        from t_item_release
        where id = #{id}
    </delete>

    <delete id="deleteItemReleaseByIds" parameterType="String">
        delete from t_item_release where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>
