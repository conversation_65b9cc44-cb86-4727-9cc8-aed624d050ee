<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ibms.service.realty.web.mapper.CleaningInspectionMapper">

    <resultMap type="CleaningInspection" id="CleaningInspectionResult">
        <result property="id" column="id"/>
        <result property="cleaningRegionId" column="cleaning_region_id"/>
        <result property="responsiblePersonId" column="responsible_person_id"/>
        <result property="responsiblePersonName" column="responsible_person_name"/>
        <result property="inspectorId" column="inspector_id"/>
        <result property="inspectorName" column="inspector_name"/>
        <result property="inspectionDate" column="inspection_date"/>
        <result property="inspectionStatus" column="inspection_status"/>
        <result property="inspectionResult" column="inspection_result"/>
        <result property="remark" column="remark"/>
        <result property="cleaningRegionName" column="cleaning_region_name"/>
    </resultMap>

    <sql id="selectCleaningInspectionVo">
        select tci.id,
        tci.cleaning_region_id,
        tci.responsible_person_id,
        tci.responsible_person_name,
        tci.inspector_id,
        tci.inspector_name,
        tci.inspection_date,
        tci.inspection_status,
        tci.inspection_result,
        tci.remark,
        tcr.area_name as cleaning_region_name
        from t_cleaning_inspection tci
        left join t_cleaning_region_info tcr on tci.cleaning_region_id = tcr.id
    </sql>

    <select id="selectCleaningInspectionList" parameterType="CleaningInspection" resultMap="CleaningInspectionResult">
        <include refid="selectCleaningInspectionVo"/>
        <where>
            <if test="cleaningRegionId != null ">and tci.cleaning_region_id = #{cleaningRegionId}</if>
            <if test="responsiblePersonId != null ">and tci.responsible_person_id = #{responsiblePersonId}</if>
            <if test="responsiblePersonName != null  and responsiblePersonName != ''">and tci.responsible_person_name
                like
                concat('%', #{responsiblePersonName}, '%')
            </if>
            <if test="inspectorId != null ">and tci.inspector_id = #{inspectorId}</if>
            <if test="inspectorName != null  and inspectorName != ''">and tci.inspector_name like concat('%',
                #{inspectorName}, '%')
            </if>
            <if test="cleaningRegionName != null  and cleaningRegionName != ''">and tcr.area_name like concat('%',
                #{cleaningRegionName}, '%')
            </if>
            <if test="inspectionDate != null ">and tci.inspection_date = #{inspectionDate}</if>
            <if test="inspectionDateStart != null ">and tci.inspection_date >= #{inspectionDateStart}</if>
            <if test="inspectionDateEnd != null ">and tci.inspection_date <![CDATA[<= #{inspectionDateEnd}]]></if>
            <if test="inspectionStatus != null  and inspectionStatus != ''">and tci.inspection_status =
                #{inspectionStatus}
            </if>
            <if test="inspectionResult != null  and inspectionResult != ''">and tci.inspection_result =
                #{inspectionResult}
            </if>
        </where>
        order by tci.id desc
    </select>

    <select id="selectCleaningInspectionById" parameterType="Integer" resultMap="CleaningInspectionResult">
        <include refid="selectCleaningInspectionVo"/>
        where tci.id = #{id}
    </select>

    <insert id="insertCleaningInspection" parameterType="CleaningInspection" useGeneratedKeys="true" keyProperty="id">
        insert into t_cleaning_inspection
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="cleaningRegionId != null">cleaning_region_id,</if>
            <if test="responsiblePersonId != null">responsible_person_id,</if>
            <if test="responsiblePersonName != null">responsible_person_name,</if>
            <if test="inspectorId != null">inspector_id,</if>
            <if test="inspectorName != null">inspector_name,</if>
            <if test="inspectionDate != null">inspection_date,</if>
            <if test="inspectionStatus != null">inspection_status,</if>
            <if test="inspectionResult != null">inspection_result,</if>
            <if test="remark != null">remark,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="cleaningRegionId != null">#{cleaningRegionId},</if>
            <if test="responsiblePersonId != null">#{responsiblePersonId},</if>
            <if test="responsiblePersonName != null">#{responsiblePersonName},</if>
            <if test="inspectorId != null">#{inspectorId},</if>
            <if test="inspectorName != null">#{inspectorName},</if>
            <if test="inspectionDate != null">#{inspectionDate},</if>
            <if test="inspectionStatus != null">#{inspectionStatus},</if>
            <if test="inspectionResult != null">#{inspectionResult},</if>
            <if test="remark != null">#{remark},</if>
        </trim>
    </insert>

    <update id="updateCleaningInspection" parameterType="CleaningInspection">
        update t_cleaning_inspection
        <trim prefix="SET" suffixOverrides=",">
            <if test="cleaningRegionId != null">cleaning_region_id = #{cleaningRegionId},</if>
            <if test="responsiblePersonId != null">responsible_person_id = #{responsiblePersonId},</if>
            <if test="responsiblePersonName != null">responsible_person_name = #{responsiblePersonName},</if>
            <if test="inspectorId != null">inspector_id = #{inspectorId},</if>
            <if test="inspectorName != null">inspector_name = #{inspectorName},</if>
            <if test="inspectionDate != null">inspection_date = #{inspectionDate},</if>
            <if test="inspectionStatus != null">inspection_status = #{inspectionStatus},</if>
            <if test="inspectionResult != null">inspection_result = #{inspectionResult},</if>
            <if test="remark != null">remark = #{remark},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteCleaningInspectionById" parameterType="Integer">
        delete
        from t_cleaning_inspection
        where id = #{id}
    </delete>

    <delete id="deleteCleaningInspectionByIds" parameterType="String">
        delete from t_cleaning_inspection where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>
