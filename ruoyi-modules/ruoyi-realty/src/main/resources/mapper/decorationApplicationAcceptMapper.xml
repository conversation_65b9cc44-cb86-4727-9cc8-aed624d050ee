<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ibms.service.realty.web.mapper.DecorationApplicationAcceptMapper">

    <resultMap type="DecorationApplicationAccept" id="DecorationApplicationAcceptResult">
        <result property="id" column="id"/>
        <result property="name" column="name"/>
        <result property="status" column="status"/>
    </resultMap>

    <sql id="selectDecorationApplicationAcceptVo">
        select id, name, status
        from t_decoration_application_accept
    </sql>

    <select id="selectDecorationApplicationAcceptList" parameterType="DecorationApplicationAccept"
            resultMap="DecorationApplicationAcceptResult">
        <include refid="selectDecorationApplicationAcceptVo"/>
        <where>
            <if test="name != null  and name != ''">and name like concat('%', #{name}, '%')</if>
            <if test="status != null ">and status = #{status}</if>
        </where>
        order by id desc
    </select>

    <select id="selectDecorationApplicationAcceptById" parameterType="Integer"
            resultMap="DecorationApplicationAcceptResult">
        <include refid="selectDecorationApplicationAcceptVo"/>
        where id = #{id}
    </select>

    <insert id="insertDecorationApplicationAccept" parameterType="DecorationApplicationAccept" useGeneratedKeys="true"
            keyProperty="id">
        insert into t_decoration_application_accept
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="name != null">name,</if>
            <if test="status != null">status,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="name != null">#{name},</if>
            <if test="status != null">#{status},</if>
        </trim>
    </insert>

    <update id="updateDecorationApplicationAccept" parameterType="DecorationApplicationAccept">
        update t_decoration_application_accept
        <trim prefix="SET" suffixOverrides=",">
            <if test="name != null">name = #{name},</if>
            <if test="status != null">status = #{status},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteDecorationApplicationAcceptById" parameterType="Integer">
        delete from t_decoration_application_accept where id = #{id}
    </delete>

    <delete id="deleteDecorationApplicationAcceptByIds" parameterType="String">
        delete from t_decoration_application_accept where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>
