<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ibms.service.realty.web.mapper.PatrolRouteMapper">

    <resultMap type="PatrolRoute" id="PatrolRouteResult">
        <result property="id" column="id"/>
        <result property="name" column="name"/>
        <result property="serialNumber" column="serial_number"/>
        <result property="remark" column="remark"/>
        <result property="status" column="status"/>
        <result property="pointCount" column="point_count"/>
        <collection property="patrolPointsList" ofType="com.ibms.service.realty.web.domain.PatrolPoint">
            <result property="id" column="patrol_point_id"/>
            <result property="name" column="patrol_point_name"/>
            <result property="code" column="patrol_point_code"/>
            <result property="remark" column="patrol_point_remark"/>
            <result property="status" column="patrol_point_status"/>
            <result property="sort" column="patrol_point_sort"/>
        </collection>
    </resultMap>

    <sql id="selectPatrolRouteVo">
        select id, name, serial_number, remark, status
        from t_patrol_route
    </sql>

    <select id="selectPatrolRouteList" parameterType="PatrolRoute" resultMap="PatrolRouteResult">
        SELECT
        pr.id,
        pr.name,
        pr.serial_number,
        pr.remark,
        pr.status,
        COUNT(pp.patrol_route_id) AS point_count
        FROM
        t_patrol_route pr
        LEFT JOIN t_patrol_route_point pp ON pr.id = pp.patrol_route_id
        <where>
            <if test="name != null  and name != ''">and pr.name like concat('%', #{name}, '%')</if>
            <if test="serialNumber != null  and serialNumber != ''">and pr.serial_number like concat('%',
                #{serialNumber},
                '%')
            </if>
            <if test="contentParam != null  and contentParam != ''">
                and (pr.serial_number like concat('%', #{contentParam},'%') or pr.name like concat('%',
                #{contentParam},'%'))
            </if>
            <if test="status != null  and status != ''">and pr.status = #{status}</if>

        </where>
        GROUP BY
        pr.id
        order by pr.id desc
    </select>

    <select id="selectPatrolRouteById" parameterType="Integer" resultMap="PatrolRouteResult">
        select pr.id,
        pr.name,
        pr.serial_number,
        pr.remark,
        pr.status,
        pp.id as patrol_point_id,
        pp.name as patrol_point_name,
        pp.code as patrol_point_code,
        pp.remark as patrol_point_remark,
        pp.status as patrol_point_status,
        prp.sort as patrol_point_sort
        from t_patrol_route pr
        left join t_patrol_route_point prp on pr.id = prp.patrol_route_id
        left join t_patrol_point pp on pp.id = prp.patrol_point_id
        where pr.id = #{id}
        order by prp.sort asc;
    </select>

    <insert id="insertPatrolRoute" parameterType="PatrolRoute" useGeneratedKeys="true" keyProperty="id">
        insert into t_patrol_route
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="name != null and name != ''">name,</if>
            <if test="serialNumber != null and serialNumber != ''">serial_number,</if>
            <if test="remark != null">remark,</if>
            <if test="status != null">status,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="name != null and name != ''">#{name},</if>
            <if test="serialNumber != null and serialNumber != ''">#{serialNumber},</if>
            <if test="remark != null">#{remark},</if>
            <if test="status != null">#{status},</if>
        </trim>
    </insert>

    <update id="updatePatrolRoute" parameterType="PatrolRoute">
        update t_patrol_route
        <trim prefix="SET" suffixOverrides=",">
            <if test="name != null and name != ''">name = #{name},</if>
            <if test="serialNumber != null and serialNumber != ''">serial_number = #{serialNumber},</if>
            <if test="remark != null">remark = #{remark},</if>
            <if test="status != null">status = #{status},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deletePatrolRouteById" parameterType="Integer">
        delete
        from t_patrol_route
        where id = #{id}
    </delete>

    <delete id="deletePatrolRouteByIds" parameterType="String">
        delete from t_patrol_route where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>
