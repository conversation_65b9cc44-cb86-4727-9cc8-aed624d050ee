<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ibms.service.realty.web.mapper.PatrolPointMapper">

    <resultMap type="PatrolPoint" id="PatrolPointResult">
        <result property="id" column="id"/>
        <result property="code" column="code"/>
        <result property="name" column="name"/>
        <result property="remark" column="remark"/>
        <result property="status" column="status"/>
    </resultMap>

    <sql id="selectPatrolPointVo">
        select id, code,name, remark,status from t_patrol_point
    </sql>

    <select id="selectPatrolPointList" parameterType="PatrolPoint" resultMap="PatrolPointResult">
        <include refid="selectPatrolPointVo"/>
        <where>
            <if test="contentParam != null  and contentParam != ''">
                and (name like concat('%', #{contentParam}, '%') or code like concat('%', #{contentParam}, '%'))
            </if>
            <if test="status != null">
                and status = #{status}
            </if>
        </where>
        order by id desc
    </select>

    <select id="selectPatrolPointById" parameterType="Integer" resultMap="PatrolPointResult">
        <include refid="selectPatrolPointVo"/>
        where id = #{id}
    </select>

    <insert id="insertPatrolPoint" parameterType="PatrolPoint" useGeneratedKeys="true" keyProperty="id">
        insert into t_patrol_point
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="code != null and code != ''">code,</if>
            <if test="name != null and name != ''">name,</if>
            <if test="remark != null">remark,</if>
            <if test="status != null">status,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="code != null and code != ''">#{code},</if>
            <if test="name != null and name != ''">#{name},</if>
            <if test="remark != null">#{remark},</if>
            <if test="status != null">#{status},</if>
        </trim>
    </insert>

    <update id="updatePatrolPoint" parameterType="PatrolPoint">
        update t_patrol_point
        <trim prefix="SET" suffixOverrides=",">
            <if test="code != null and code != ''">code = #{code},</if>
            <if test="name != null and name != ''">name = #{name},</if>
            <if test="remark != null">remark = #{remark},</if>
            <if test="status != null">status = #{status},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deletePatrolPointById" parameterType="Integer">
        delete from t_patrol_point where id = #{id}
    </delete>

    <delete id="deletePatrolPointByIds" parameterType="Integer">
        delete from t_patrol_point where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>
