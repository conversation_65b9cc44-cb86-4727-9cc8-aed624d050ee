<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ibms.service.realty.web.mapper.VenueInfoMapper">

    <resultMap type="VenueInfo" id="VenueInfoResult">
        <result property="id" column="id"/>
        <result property="venueName" column="venue_name"/>
        <result property="area" column="area"/>
        <result property="imageUrls" column="image_urls"/>
        <result property="description" column="description"/>
    </resultMap>

    <sql id="selectVenueInfoVo">
        SELECT id, venue_name, area, image_urls, description
        FROM t_venue_info
    </sql>

    <select id="selectVenueInfoList" parameterType="VenueInfo" resultMap="VenueInfoResult">
        <include refid="selectVenueInfoVo"/>
        <where>
            <if test="venueName != null  and venueName != ''">and venue_name like concat('%', #{venueName}, '%')</if>
            <if test="area != null ">and area = #{area}</if>
            <if test="imageUrls != null  and imageUrls != ''">and image_urls = #{imageUrls}</if>
            <if test="description != null  and description != ''">and description = #{description}</if>
        </where>
        order by id desc
    </select>

    <select id="selectVenueInfoById" parameterType="Integer" resultMap="VenueInfoResult">
        <include refid="selectVenueInfoVo"/>
        where id = #{id}
    </select>

    <!--    CREATE TABLE `t_venue_reservation` (-->
    <!--    `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键id',-->
    <!--    `venue_id` int(11) DEFAULT NULL COMMENT '场馆id',-->
    <!--    `reservation_date` date DEFAULT NULL COMMENT '预约日期',-->
    <!--    `time_slot` varchar(20) DEFAULT NULL COMMENT '时间段',-->
    <!--    PRIMARY KEY (`id`)-->
    <!--    ) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='场馆预约信息表';-->
    <select id="selectReservation"
            resultType="com.ibms.service.realty.web.controller.venueInfo.param.VenueResevationVo">
        select a.id, a.venue_id as venueId, a.reservation_date as reservationDate, a.time_slot as timeSlot,1 as status
        from t_venue_reservation a

        <where>
            <if test="venueId != null">
                and a.venue_id = #{venueId}
            </if>
            <if test="reservationDate != null">
                and a.reservation_date = #{reservationDate}
            </if>
            -- 7天后的预约信息，不包含今天
            <if test="isSevenDays != null and isSevenDays == true">
                and a.reservation_date &gt;= DATE_ADD(CURDATE(),INTERVAL 1 DAY)
                and a.reservation_date &lt;= DATE_ADD(CURDATE(),INTERVAL 7 DAY)
            </if>

        </where>
        order by a.reservation_date
    </select>
    <select id="selectFinReservationNum"
            resultType="com.ibms.service.realty.web.controller.venueInfo.param.VenueResevationVo">

        SELECT (24 - COUNT(1)) AS remainNum, reservation_date AS reservationDate, venue_id AS venueId
        FROM t_venue_reservation
        WHERE venue_id = #{venueId}
        AND reservation_date &gt;= DATE_ADD(CURDATE(), INTERVAL 1 DAY)
        AND reservation_date &lt;= DATE_ADD(CURDATE(), INTERVAL 7 DAY)
        GROUP BY reservation_date
    </select>

    <!--    CREATE TABLE `t_user_venue_reservation` (-->
    <!--    `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键id',-->
    <!--    `user_id` int(11) DEFAULT NULL COMMENT '用户id',-->
    <!--    `venue_id` int(11) DEFAULT NULL COMMENT '场馆id',-->
    <!--    `reservation_date` date DEFAULT NULL COMMENT '预约日期',-->
    <!--    `time_slot` varchar(20) DEFAULT NULL COMMENT '时间段',-->
    <!--    PRIMARY KEY (`id`)-->
    <!--    ) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='用户场馆预约表';-->
    <select id="selectUserReservationList"
            resultType="com.ibms.service.realty.web.controller.venueInfo.param.UserVenueResevationVo">
        select uvr.user_id as userId,
        uvr.venue_id as venueId,
        uvr.reservation_date as reservationDate,
        -- uvr.time_slot as timeSlot,
        GROUP_CONCAT(uvr.time_slot) as timeSlot,
        vi.venue_name as venueName
        from t_user_venue_reservation uvr
        left join t_venue_info vi on uvr.venue_id = vi.id
        where uvr.user_id = #{userId}
        <if test="startTime != null and endTime != null">
            and uvr.reservation_date &gt;= #{startTime}
            and uvr.reservation_date &lt;= #{endTime}
        </if>
        group by uvr.venue_id, uvr.reservation_date
        order by uvr.reservation_date, uvr.venue_id
    </select>

    <select id="selectReservationList"
            resultType="com.ibms.service.realty.web.controller.venueInfo.param.UserVenueResevationVo">
        select uvr.user_id as userId,
        uvr.venue_id as venueId,
        uvr.reservation_date as reservationDate,
        -- uvr.time_slot as timeSlot,
        GROUP_CONCAT(uvr.time_slot) as timeSlot,
        vi.venue_name as venueName
        from t_user_venue_reservation uvr
        left join t_venue_info vi on uvr.venue_id = vi.id
        <where>
            <if test="reservationDate != null">
                and uvr.reservation_date = #{reservationDate}
            </if>
        </where>
        group by uvr.user_id, uvr.venue_id, uvr.reservation_date
        order by uvr.reservation_date, uvr.venue_id
    </select>

    <!--    <resultMap id="UserVenueResevationVoMap"-->
    <!--               type="com.ibms.service.realty.web.controller.venueInfo.param.UserVenueResevationVo">-->
    <!--        <id column="userId" property="userId"/>-->
    <!--        <result column="venueId" property="venueId"/>-->
    <!--        <result column="reservationDate" property="reservationDate"/>-->
    <!--        <result column="venueName" property="venueName"/>-->
    <!--        <collection property="timeSlotList" ofType="java.lang.String"-->
    <!--                    column="timeSlot">-->
    <!--                -->
    <!--                </collection>-->
    <!--    </resultMap>-->

    <insert id="insertVenueInfo" parameterType="VenueInfo" useGeneratedKeys="true" keyProperty="id">
        insert into t_venue_info
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="venueName != null">venue_name,</if>
            <if test="area != null">area,</if>
            <if test="imageUrls != null">image_urls,</if>
            <if test="description != null">description,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="venueName != null">#{venueName},</if>
            <if test="area != null">#{area},</if>
            <if test="imageUrls != null">#{imageUrls},</if>
            <if test="description != null">#{description},</if>
        </trim>
    </insert>

    <!--    CREATE TABLE `t_user_venue_reservation` (-->
    <!--    `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键id',-->
    <!--    `user_id` int(11) DEFAULT NULL COMMENT '用户id',-->
    <!--    `venue_id` int(11) DEFAULT NULL COMMENT '场馆id',-->
    <!--    `reservation_date` date DEFAULT NULL COMMENT '预约日期',-->
    <!--    `time_slot` varchar(20) DEFAULT NULL COMMENT '时间段',-->
    <!--    PRIMARY KEY (`id`)-->
    <!--    ) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='用户场馆预约表';-->
    <insert id="insertUserVenueReservation">
        insert t_user_venue_reservation values
        <foreach collection="timeSlotList" item="item" index="index" separator=",">
            <trim prefix="(" suffix=")" suffixOverrides=",">
                null,
                <if test="userId != null">#{userId},</if>
                <if test="venueId != null">#{venueId},</if>
                <if test="reservationDate != null">#{reservationDate},</if>
                <if test="item != null">#{item},</if>
            </trim>
        </foreach>
    </insert>

    <!--    CREATE TABLE `t_venue_reservation` (-->
    <!--    `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键id',-->
    <!--    `venue_id` int(11) DEFAULT NULL COMMENT '场馆id',-->
    <!--    `reservation_date` date DEFAULT NULL COMMENT '预约日期',-->
    <!--    `time_slot` varchar(20) DEFAULT NULL COMMENT '时间段',-->
    <!--    PRIMARY KEY (`id`)-->
    <!--    ) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='场馆预约信息表';-->
    <insert id="insertVenueReservation">
        insert t_venue_reservation values
        <foreach collection="timeSlotList" item="item" index="index" separator=",">
            <trim prefix="(" suffix=")" suffixOverrides=",">
                null,
                <if test="venueId != null">#{venueId},</if>
                <if test="reservationDate != null">#{reservationDate},</if>
                <if test="item != null">#{item},</if>
            </trim>
        </foreach>
    </insert>

    <update id="updateVenueInfo" parameterType="VenueInfo">
        update t_venue_info
        <trim prefix="SET" suffixOverrides=",">
            <if test="venueName != null">venue_name = #{venueName},</if>
            <if test="area != null">area = #{area},</if>
            <if test="imageUrls != null">image_urls = #{imageUrls},</if>
            <if test="description != null">description = #{description},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteVenueInfoById" parameterType="Integer">
        DELETE
        FROM t_venue_info
        WHERE id = #{id}
    </delete>

    <delete id="deleteVenueInfoByIds" parameterType="String">
        delete from t_venue_info where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>
