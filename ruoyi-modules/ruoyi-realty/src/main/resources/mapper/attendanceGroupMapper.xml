<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ibms.service.realty.web.mapper.AttendanceGroupMapper">

    <resultMap type="AttendanceGroup" id="AttendanceGroupResult">
        <result property="id" column="id"/>
        <result property="name" column="name"/>
        <result property="type" column="type"/>
        <result property="groupIds" column="group_ids"/>
        <result property="userIds" column="user_ids"/>
        <result property="workday" column="workday"/>
        <result property="holidayRest" column="holiday_rest"/>
        <result property="workTime" column="work_time"/>
        <result property="workCheckinTimeRange" column="work_checkin_time_range"/>
        <result property="workCheckin" column="work_checkin"/>
        <result property="workLateMinutes" column="work_late_minutes"/>
        <result property="offWorkTime" column="off_work_time"/>
        <result property="offWorkCheckinTimeRange" column="off_work_checkin_time_range"/>
        <result property="offWorkCheckin" column="off_work_checkin"/>
        <result property="offWorkLateMinutes" column="off_work_late_minutes"/>
        <result property="checkinMethod" column="checkin_method"/>
        <result property="checkinLocationRange" column="checkin_location_range"/>
        <result property="checkinLocationNameAddress" column="checkin_location_name_address"/>
    </resultMap>

    <sql id="selectAttendanceGroupVo">
        select id, name, type, group_ids, user_ids, workday, holiday_rest, work_time, work_checkin_time_range,
        work_checkin, work_late_minutes, off_work_time, off_work_checkin_time_range, off_work_checkin,
        off_work_late_minutes, checkin_method, checkin_location_range, checkin_location_name_address from
        t_attendance_group
    </sql>

    <select id="selectAttendanceGroupList" parameterType="AttendanceGroup" resultMap="AttendanceGroupResult">
        <include refid="selectAttendanceGroupVo"/>
        <where>
            <if test="name != null  and name != ''">and name like concat('%', #{name}, '%')</if>
            <if test="type != null ">and type = #{type}</if>
            <if test="groupIds != null  and groupIds != ''">and group_ids = #{groupIds}</if>
            <if test="userIds != null  and userIds != ''">and user_ids = #{userIds}</if>
            <if test="workday != null ">and workday = #{workday}</if>
            <if test="holidayRest != null ">and holiday_rest = #{holidayRest}</if>
            <if test="workTime != null ">and work_time = #{workTime}</if>
            <if test="workCheckinTimeRange != null  and workCheckinTimeRange != ''">and work_checkin_time_range =
                #{workCheckinTimeRange}
            </if>
            <if test="workCheckin != null ">and work_checkin = #{workCheckin}</if>
            <if test="workLateMinutes != null ">and work_late_minutes = #{workLateMinutes}</if>
            <if test="offWorkTime != null ">and off_work_time = #{offWorkTime}</if>
            <if test="offWorkCheckinTimeRange != null  and offWorkCheckinTimeRange != ''">and
                off_work_checkin_time_range = #{offWorkCheckinTimeRange}
            </if>
            <if test="offWorkCheckin != null ">and off_work_checkin = #{offWorkCheckin}</if>
            <if test="offWorkLateMinutes != null ">and off_work_late_minutes = #{offWorkLateMinutes}</if>
            <if test="checkinMethod != null ">and checkin_method = #{checkinMethod}</if>
            <if test="checkinLocationRange != null ">and checkin_location_range = #{checkinLocationRange}</if>
            <if test="checkinLocationNameAddress != null  and checkinLocationNameAddress != ''">and
                checkin_location_name_address = #{checkinLocationNameAddress}
            </if>
        </where>
        order by id desc
    </select>

    <select id="selectAttendanceGroupById" parameterType="Integer" resultMap="AttendanceGroupResult">
        <include refid="selectAttendanceGroupVo"/>
        where id = #{id}
    </select>

    <insert id="insertAttendanceGroup" parameterType="AttendanceGroup" useGeneratedKeys="true" keyProperty="id">
        insert into t_attendance_group
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="name != null and name != ''">name,</if>
            <if test="type != null">type,</if>
            <if test="groupIds != null">group_ids,</if>
            <if test="userIds != null">user_ids,</if>
            <if test="workday != null">workday,</if>
            <if test="holidayRest != null">holiday_rest,</if>
            <if test="workTime != null">work_time,</if>
            <if test="workCheckinTimeRange != null">work_checkin_time_range,</if>
            <if test="workCheckin != null">work_checkin,</if>
            <if test="workLateMinutes != null">work_late_minutes,</if>
            <if test="offWorkTime != null">off_work_time,</if>
            <if test="offWorkCheckinTimeRange != null">off_work_checkin_time_range,</if>
            <if test="offWorkCheckin != null">off_work_checkin,</if>
            <if test="offWorkLateMinutes != null">off_work_late_minutes,</if>
            <if test="checkinMethod != null">checkin_method,</if>
            <if test="checkinLocationRange != null">checkin_location_range,</if>
            <if test="checkinLocationNameAddress != null">checkin_location_name_address,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="name != null and name != ''">#{name},</if>
            <if test="type != null">#{type},</if>
            <if test="groupIds != null">#{groupIds},</if>
            <if test="userIds != null">#{userIds},</if>
            <if test="workday != null">#{workday},</if>
            <if test="holidayRest != null">#{holidayRest},</if>
            <if test="workTime != null">#{workTime},</if>
            <if test="workCheckinTimeRange != null">#{workCheckinTimeRange},</if>
            <if test="workCheckin != null">#{workCheckin},</if>
            <if test="workLateMinutes != null">#{workLateMinutes},</if>
            <if test="offWorkTime != null">#{offWorkTime},</if>
            <if test="offWorkCheckinTimeRange != null">#{offWorkCheckinTimeRange},</if>
            <if test="offWorkCheckin != null">#{offWorkCheckin},</if>
            <if test="offWorkLateMinutes != null">#{offWorkLateMinutes},</if>
            <if test="checkinMethod != null">#{checkinMethod},</if>
            <if test="checkinLocationRange != null">#{checkinLocationRange},</if>
            <if test="checkinLocationNameAddress != null">#{checkinLocationNameAddress},</if>
        </trim>
    </insert>

    <update id="updateAttendanceGroup" parameterType="AttendanceGroup">
        update t_attendance_group
        <trim prefix="SET" suffixOverrides=",">
            <if test="name != null and name != ''">name = #{name},</if>
            <if test="type != null">type = #{type},</if>
            <if test="groupIds != null">group_ids = #{groupIds},</if>
            <if test="userIds != null">user_ids = #{userIds},</if>
            <if test="workday != null">workday = #{workday},</if>
            <if test="holidayRest != null">holiday_rest = #{holidayRest},</if>
            <if test="workTime != null">work_time = #{workTime},</if>
            <if test="workCheckinTimeRange != null">work_checkin_time_range = #{workCheckinTimeRange},</if>
            <if test="workCheckin != null">work_checkin = #{workCheckin},</if>
            <if test="workLateMinutes != null">work_late_minutes = #{workLateMinutes},</if>
            <if test="offWorkTime != null">off_work_time = #{offWorkTime},</if>
            <if test="offWorkCheckinTimeRange != null">off_work_checkin_time_range = #{offWorkCheckinTimeRange},</if>
            <if test="offWorkCheckin != null">off_work_checkin = #{offWorkCheckin},</if>
            <if test="offWorkLateMinutes != null">off_work_late_minutes = #{offWorkLateMinutes},</if>
            <if test="checkinMethod != null">checkin_method = #{checkinMethod},</if>
            <if test="checkinLocationRange != null">checkin_location_range = #{checkinLocationRange},</if>
            <if test="checkinLocationNameAddress != null">checkin_location_name_address =
                #{checkinLocationNameAddress},
            </if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteAttendanceGroupById" parameterType="Integer">
        delete from t_attendance_group where id = #{id}
    </delete>

    <delete id="deleteAttendanceGroupByIds" parameterType="String">
        delete from t_attendance_group where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>
