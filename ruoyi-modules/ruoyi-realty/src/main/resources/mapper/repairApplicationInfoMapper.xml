<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ibms.service.realty.web.mapper.RepairApplicationInfoMapper">

    <resultMap type="RepairApplicationInfo" id="RepairApplicationInfoResult">
        <result property="id" column="id"/>
        <result property="workOrderNo" column="work_order_no"/>
        <result property="repairType" column="repair_type"/>
        <result property="repairAddress" column="repair_address"/>
        <result property="repairArea" column="repair_area"/>
        <result property="description" column="description"/>
        <result property="submitId" column="submit_id"/>
        <result property="submitName" column="submit_name"/>
        <result property="contactPhone" column="contact_phone"/>
        <result property="appointmentTime" column="appointment_time"/>
        <result property="submitTime" column="submit_time"/>
        <result property="serviceType" column="service_type"/>
        <result property="serviceAmount" column="service_amount"/>
        <result property="serviceTypeStatus" column="service_type_status"/>
        <result property="status" column="status"/>
        <result property="flag" column="flag"/>
        <result property="imageUrl" column="image_url"/>
        <result property="maintenancePersonnelId" column="maintenance_personnel_id"/>
        <result property="maintenancePersonnelName" column="maintenance_personnel_name"/>
        <result property="maintenancePersonnelContactPhone" column="maintenance_personnel_contact_phone"/>
        <result property="maintenancePersonnelImageUrl" column="maintenance_personnel_image_url"/>
        <result property="maintenanceStatus" column="maintenance_status"/>
        <result property="handleTime" column="handle_time"/>
        <result property="completionTime" column="completion_time"/>
        <result property="visitType" column="visit_type"/>
        <result property="visitContent" column="visit_content"/>
        <result property="visitResult" column="visit_result"/>
    </resultMap>

    <sql id="selectRepairApplicationInfoVo">
        select id,
        work_order_no,
        repair_type,
        repair_address,
        repair_area,
        description,
        submit_id,
        submit_name,
        contact_phone,
        appointment_time,
        submit_time,
        service_type,
        service_amount,
        service_type_status,
        status,
        flag,
        image_url,
        maintenance_personnel_id,
        maintenance_personnel_name,
        maintenance_personnel_contact_phone,
        maintenance_personnel_image_url,
        maintenance_status,
        handle_time,
        completion_time,
        visit_type,
        visit_content,
        visit_result
        from t_repair_application_info
    </sql>

    <select id="selectRepairApplicationInfoList" parameterType="RepairApplicationInfo"
            resultMap="RepairApplicationInfoResult">
        <include refid="selectRepairApplicationInfoVo"/>
        <where>
            <if test="workOrderNo != null  and workOrderNo != ''">and work_order_no = #{workOrderNo}</if>
            <if test="repairType != null ">and repair_type = #{repairType}</if>
            <if test="repairAddress != null  and repairAddress != ''">and repair_address = #{repairAddress}</if>
            <if test="repairArea != null ">and repair_area = #{repairArea}</if>
            <if test="description != null  and description != ''">and description = #{description}</if>
            <if test="submitId != null ">and submit_id = #{submitId}</if>
            <if test="submitName != null  and submitName != ''">and submit_name like concat('%', #{submitName}, '%')
            </if>
            <if test="contactPhone != null  and contactPhone != ''">and contact_phone = #{contactPhone}</if>
            <if test="appointmentTime != null ">and appointment_time = #{appointmentTime}</if>
            <if test="submitTime != null ">and submit_time = #{submitTime}</if>
            <if test="serviceType != null ">and service_type = #{serviceType}</if>
            <if test="serviceAmount != null ">and service_amount = #{serviceAmount}</if>
            <if test="serviceTypeStatus != null ">and service_type_status = #{serviceTypeStatus}</if>
            <if test="status != null ">and status = #{status}</if>
            <if test="flag != null ">and flag = #{flag}</if>
            <if test="imageUrl != null  and imageUrl != ''">and image_url = #{imageUrl}</if>
            <if test="maintenancePersonnelId != null ">and maintenance_personnel_id = #{maintenancePersonnelId}</if>
            <if test="maintenancePersonnelName != null  and maintenancePersonnelName != ''">and
                maintenance_personnel_name like concat('%', #{maintenancePersonnelName}, '%')
            </if>
            <if test="maintenancePersonnelContactPhone != null  and maintenancePersonnelContactPhone != ''">and
                maintenance_personnel_contact_phone = #{maintenancePersonnelContactPhone}
            </if>
            <if test="maintenancePersonnelImageUrl != null  and maintenancePersonnelImageUrl != ''">and
                maintenance_personnel_image_url = #{maintenancePersonnelImageUrl}
            </if>
            <if test="maintenanceStatus != null ">and maintenance_status = #{maintenanceStatus}</if>
            <if test="handleTime != null ">and handle_time = #{handleTime}</if>
            <if test="completionTime != null ">and completion_time = #{completionTime}</if>
            <if test="visitType != null ">and visit_type = #{visitType}</if>
            <if test="visitContent != null  and visitContent != ''">and visit_content = #{visitContent}</if>
            <if test="visitResult != null  and visitResult != ''">and visit_result = #{visitResult}</if>
        </where>
        order by submit_time desc
    </select>

    <select id="selectRepairApplicationInfoById" parameterType="Integer" resultMap="RepairApplicationInfoResult">
        <include refid="selectRepairApplicationInfoVo"/>
        where id = #{id}
    </select>

    <insert id="insertRepairApplicationInfo" parameterType="RepairApplicationInfo" useGeneratedKeys="true"
            keyProperty="id">
        insert into t_repair_application_info
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="workOrderNo != null">work_order_no,</if>
            <if test="repairType != null">repair_type,</if>
            <if test="repairAddress != null">repair_address,</if>
            <if test="repairArea != null">repair_area,</if>
            <if test="description != null">description,</if>
            <if test="submitId != null">submit_id,</if>
            <if test="submitName != null and submitName != ''">submit_name,</if>
            <if test="contactPhone != null">contact_phone,</if>
            <if test="appointmentTime != null">appointment_time,</if>
            <if test="submitTime != null">submit_time,</if>
            <if test="serviceType != null">service_type,</if>
            <if test="serviceAmount != null">service_amount,</if>
            <if test="serviceTypeStatus != null">service_type_status,</if>
            <if test="status != null">status,</if>
            <if test="flag != null">flag,</if>
            <if test="imageUrl != null">image_url,</if>
            <if test="maintenancePersonnelId != null">maintenance_personnel_id,</if>
            <if test="maintenancePersonnelName != null">maintenance_personnel_name,</if>
            <if test="maintenancePersonnelContactPhone != null">maintenance_personnel_contact_phone,</if>
            <if test="maintenancePersonnelImageUrl != null">maintenance_personnel_image_url,</if>
            <if test="maintenanceStatus != null">maintenance_status,</if>
            <if test="handleTime != null">handle_time,</if>
            <if test="completionTime != null">completion_time,</if>
            <if test="visitType != null">visit_type,</if>
            <if test="visitContent != null">visit_content,</if>
            <if test="visitResult != null">visit_result,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="workOrderNo != null">#{workOrderNo},</if>
            <if test="repairType != null">#{repairType},</if>
            <if test="repairAddress != null">#{repairAddress},</if>
            <if test="repairArea != null">#{repair_area},</if>
            <if test="description != null">#{description},</if>
            <if test="submitId != null">#{submitId},</if>
            <if test="submitName != null and submitName != ''">#{submitName},</if>
            <if test="contactPhone != null">#{contactPhone},</if>
            <if test="appointmentTime != null">#{appointmentTime},</if>
            <if test="submitTime != null">#{submitTime},</if>
            <if test="serviceType != null">#{serviceType},</if>
            <if test="serviceAmount != null">#{serviceAmount},</if>
            <if test="serviceTypeStatus != null">#{serviceTypeStatus},</if>
            <if test="status != null">#{status},</if>
            <if test="flag != null">#{flag},</if>
            <if test="imageUrl != null">#{imageUrl},</if>
            <if test="maintenancePersonnelId != null">#{maintenancePersonnelId},</if>
            <if test="maintenancePersonnelName != null">#{maintenancePersonnelName},</if>
            <if test="maintenancePersonnelContactPhone != null">#{maintenancePersonnelContactPhone},</if>
            <if test="maintenancePersonnelImageUrl != null">#{maintenancePersonnelImageUrl},</if>
            <if test="maintenanceStatus != null">#{maintenanceStatus},</if>
            <if test="handleTime != null">#{handleTime},</if>
            <if test="completionTime != null">#{completionTime},</if>
            <if test="visitType != null">#{visitType},</if>
            <if test="visitContent != null">#{visitContent},</if>
            <if test="visitResult != null">#{visitResult},</if>
        </trim>
    </insert>

    <update id="updateRepairApplicationInfo" parameterType="RepairApplicationInfo">
        update t_repair_application_info
        <trim prefix="SET" suffixOverrides=",">
            <if test="workOrderNo != null">work_order_no = #{workOrderNo},</if>
            <if test="repairType != null">repair_type = #{repairType},</if>
            <if test="repairAddress != null">repair_address = #{repairAddress},</if>
            <if test="repairArea != null">repair_area = #{repairArea},</if>
            <if test="description != null">description = #{description},</if>
            <if test="submitId != null">submit_id = #{submitId},</if>
            <if test="submitName != null and submitName != ''">submit_name = #{submitName},</if>
            <if test="contactPhone != null">contact_phone = #{contactPhone},</if>
            <if test="appointmentTime != null">appointment_time = #{appointmentTime},</if>
            <if test="submitTime != null">submit_time = #{submitTime},</if>
            <if test="serviceType != null">service_type = #{serviceType},</if>
            <if test="serviceAmount != null">service_amount = #{serviceAmount},</if>
            <if test="serviceTypeStatus != null">service_type_status = #{serviceTypeStatus},</if>
            <if test="status != null">status = #{status},</if>
            <if test="flag != null">flag = #{flag},</if>
            <if test="imageUrl != null">image_url = #{imageUrl},</if>
            <if test="maintenancePersonnelId != null">maintenance_personnel_id = #{maintenancePersonnelId},</if>
            <if test="maintenancePersonnelName != null">maintenance_personnel_name = #{maintenancePersonnelName},</if>
            <if test="maintenancePersonnelContactPhone != null">maintenance_personnel_contact_phone =
                #{maintenancePersonnelContactPhone},
            </if>
            <if test="maintenancePersonnelImageUrl != null">maintenance_personnel_image_url =
                #{maintenancePersonnelImageUrl},
            </if>
            <if test="maintenanceStatus != null">maintenance_status = #{maintenanceStatus},</if>
            <if test="handleTime != null">handle_time = #{handleTime},</if>
            <if test="completionTime != null">completion_time = #{completionTime},</if>
            <if test="visitType != null">visit_type = #{visitType},</if>
            <if test="visitContent != null">visit_content = #{visitContent},</if>
            <if test="visitResult != null">visit_result = #{visitResult},</if>
        </trim>
        <where>
            <if test="id != null">and id = #{id}</if>
            <if test="workOrderNo != null and workOrderNo != ''">and work_order_no = #{workOrderNo}</if>
        </where>
    </update>

    <delete id="deleteRepairApplicationInfoById" parameterType="Integer">
        delete
        from t_repair_application_info
        where id = #{id}
    </delete>

    <delete id="deleteRepairApplicationInfoByIds" parameterType="String">
        delete from t_repair_application_info where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>
