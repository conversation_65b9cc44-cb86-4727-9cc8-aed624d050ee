<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ibms.service.realty.web.mapper.PublicInstrumentMapper">

    <resultMap type="PublicInstrument" id="PublicInstrumentResult">
        <id property="id" column="id"/>
        <result property="number" column="number"/>
        <result property="name" column="name"/>
        <result property="instrumentType" column="instrument_type"/>
        <result property="buildingId" column="building_id"/>
        <result property="sharedRoomIds" column="shared_room_ids"/>
        <result property="buildingName" column="buildingName"/>
        <result property="instrumentTypeName" column="instrumentTypeName"/>


        <collection property="floorAndRoomVos"
                    ofType="com.ibms.service.realty.web.controller.publicInstrument.vo.floorAndRoomVo">
            <id property="roomId" column="roomId"/>
            <result property="roomName" column="roomName"/>
            <result property="floorId" column="floorId"/>
            <result property="floorName" column="floorName"/>
        </collection>
    </resultMap>

    <select id="selectPublicInstrumentList" parameterType="PublicInstrument" resultMap="PublicInstrumentResult">
        SELECT pi.id,
        pi.number,
        pi.name,
        pi.instrument_type,
        pi.building_id,
        pi.shared_room_ids,

        pri.name AS buildingName,
        iti.name AS instrumentTypeName
        FROM t_public_instrument pi
        LEFT JOIN t_instrument_type_info iti ON pi.instrument_type = iti.id
        LEFT JOIN t_property_resource_info pri ON pi.building_id = pri.id

        <where>
            <if test="name != null and name != ''">
                AND pi.name LIKE CONCAT('%', #{name}, '%')
            </if>
            <if test="instrumentType != null">
                AND pi.instrument_type = #{instrumentType}
            </if>
            <if test="buildingId != null">
                AND pi.building_id = #{buildingId}
            </if>
        </where>
        order by pi.id desc
    </select>

    <select id="selectPublicInstrumentById" parameterType="Integer" resultMap="PublicInstrumentResult">
        SELECT pi.id,
        pi.number,
        pi.name,
        pi.instrument_type,
        pi.building_id,
        pi.shared_room_ids,

        pri.id AS roomId,
        pri.name AS roomName,
        pri2.name AS floorName,
        pri2.id AS floorId
        FROM t_public_instrument pi
        LEFT JOIN t_property_resource_info pri ON FIND_IN_SET(pri.id, pi.shared_room_ids)
        LEFT JOIN t_property_resource_info pri2 ON pri.parent_id = pri2.id
        WHERE pi.id = #{id}
    </select>

    <insert id="insertPublicInstrument" parameterType="PublicInstrument" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO t_public_instrument (number, name, instrument_type, building_id, shared_room_ids)
        VALUES (#{number}, #{name}, #{instrumentType}, #{buildingId}, #{sharedRoomIds})
    </insert>

    <update id="updatePublicInstrument" parameterType="PublicInstrument">
        UPDATE t_public_instrument
        SET number = #{number},
        name = #{name},
        instrument_type = #{instrumentType},
        building_id = #{buildingId},
        shared_room_ids = #{sharedRoomIds}
        WHERE id = #{id}
    </update>

    <delete id="deletePublicInstrumentById" parameterType="Integer">
        DELETE
        FROM t_public_instrument
        WHERE id = #{id}
    </delete>

    <delete id="deletePublicInstrumentByIds" parameterType="String">
        DELETE FROM t_public_instrument WHERE id IN
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>
