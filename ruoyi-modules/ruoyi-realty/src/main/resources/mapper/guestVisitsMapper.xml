<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ibms.service.realty.web.mapper.GuestVisitsMapper">

    <resultMap type="GuestVisits" id="GuestVisitsResult">
        <result property="id" column="id"/>
        <result property="managementAreaId" column="management_area_id"/>
        <result property="guestName" column="guest_name"/>
        <result property="idCardNum" column="id_card_num"/>
        <result property="contactNum" column="contact_num"/>
        <result property="visitedName" column="visited_name"/>
        <result property="visitTime" column="visit_time"/>
        <result property="leaveTime" column="leave_time"/>
        <result property="reason" column="reason"/>
    </resultMap>

    <sql id="selectGuestVisitsVo">
        select id,
        management_area_id,
        guest_name,
        id_card_num,
        contact_num,
        visited_name,
        visit_time,
        leave_time,
        reason
        from t_guest_visits
    </sql>

    <select id="selectGuestVisitsList" parameterType="GuestVisits" resultMap="GuestVisitsResult">
        <include refid="selectGuestVisitsVo"/>
        <where>
            <if test="managementAreaId != null ">and management_area_id = #{managementAreaId}</if>
            <if test="guestName != null  and guestName != ''">and guest_name like concat('%', #{guestName}, '%')</if>
            <if test="idCardNum != null  and idCardNum != ''">and id_card_num = #{idCardNum}</if>
            <if test="contactNum != null  and contactNum != ''">and contact_num = #{contactNum}</if>
            <if test="visitedName != null  and visitedName != ''">and visited_name like concat('%', #{visitedName},
                '%')
            </if>
            <if test="visitTime != null ">and visit_time = #{visitTime}</if>
            <if test="leaveTime != null ">and leave_time = #{leaveTime}</if>
            <if test="reason != null  and reason != ''">and reason = #{reason}</if>
        </where>
        order by id desc
    </select>

    <select id="selectGuestVisitsById" parameterType="Integer" resultMap="GuestVisitsResult">
        <include refid="selectGuestVisitsVo"/>
        where id = #{id}
    </select>

    <insert id="insertGuestVisits" parameterType="GuestVisits" useGeneratedKeys="true" keyProperty="id">
        insert into t_guest_visits
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="managementAreaId != null">management_area_id,</if>
            <if test="guestName != null">guest_name,</if>
            <if test="idCardNum != null">id_card_num,</if>
            <if test="contactNum != null">contact_num,</if>
            <if test="visitedName != null">visited_name,</if>
            <if test="visitTime != null">visit_time,</if>
            <if test="leaveTime != null">leave_time,</if>
            <if test="reason != null">reason,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="managementAreaId != null">#{managementAreaId},</if>
            <if test="guestName != null">#{guestName},</if>
            <if test="idCardNum != null">#{idCardNum},</if>
            <if test="contactNum != null">#{contactNum},</if>
            <if test="visitedName != null">#{visitedName},</if>
            <if test="visitTime != null">#{visitTime},</if>
            <if test="leaveTime != null">#{leaveTime},</if>
            <if test="reason != null">#{reason},</if>
        </trim>
    </insert>

    <update id="updateGuestVisits" parameterType="GuestVisits">
        update t_guest_visits
        <trim prefix="SET" suffixOverrides=",">
            <if test="managementAreaId != null">management_area_id = #{managementAreaId},</if>
            <if test="guestName != null">guest_name = #{guestName},</if>
            <if test="idCardNum != null">id_card_num = #{idCardNum},</if>
            <if test="contactNum != null">contact_num = #{contactNum},</if>
            <if test="visitedName != null">visited_name = #{visitedName},</if>
            <if test="visitTime != null">visit_time = #{visitTime},</if>
            <if test="leaveTime != null">leave_time = #{leaveTime},</if>
            <if test="reason != null">reason = #{reason},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteGuestVisitsById" parameterType="Integer">
        delete
        from t_guest_visits
        where id = #{id}
    </delete>

    <delete id="deleteGuestVisitsByIds" parameterType="String">
        delete from t_guest_visits where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>
