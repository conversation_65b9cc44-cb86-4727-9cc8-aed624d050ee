<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ibms.service.realty.web.mapper.DecorationPlanMapper">

    <resultMap type="DecorationPlan" id="DecorationPlanResult">
        <result property="taskId" column="task_id"/>
        <result property="roomCode" column="room_code"/>
        <result property="roomName" column="room_name"/>
        <result property="planName" column="plan_name"/>
        <result property="validityStart" column="validity_start"/>
        <result property="validityEnd" column="validity_end"/>
        <result property="patrolFrequency" column="patrol_frequency"/>
        <result property="patrolTimes" column="patrol_times"/>
        <result property="dailyPatrolTimes" column="daily_patrol_times"/>
        <result property="performerId" column="performer_id"/>
        <result property="performerName" column="performer_name"/>
        <result property="decorationInspectionIds" column="decoration_inspection_ids"/>
        <result property="executeTime" column="execute_time"/>
        <result property="status" column="status"/>
        <result property="createTime" column="create_time"/>
    </resultMap>

    <sql id="selectDecorationPlanVo">
        select task_id,
        room_code,
        room_name,
        plan_name,
        validity_start,
        validity_end,
        patrol_frequency,
        patrol_times,
        daily_patrol_times,
        performer_id,
        performer_name,
        decoration_inspection_ids,
        execute_time,
        status,
        create_time
        from t_decoration_plan
    </sql>

    <select id="selectDecorationPlanList" parameterType="DecorationPlan" resultMap="DecorationPlanResult">
        <include refid="selectDecorationPlanVo"/>
        <where>
            <if test="roomCode != null ">and room_code = #{roomCode}</if>
            <if test="roomName != null  and roomName != ''">and room_name like concat('%', #{roomName}, '%')</if>
            <if test="planName != null  and planName != ''">and plan_name like concat('%', #{planName}, '%')</if>
            <if test="validityStart != null ">and validity_start <![CDATA[<= #{validityStart}]]></if>
            <if test="validityEnd != null ">and validity_end >= #{validityEnd}</if>
            <if test="patrolFrequency != null ">and patrol_frequency = #{patrolFrequency}</if>
            <if test="dailyPatrolTimes != null ">and daily_patrol_times = #{dailyPatrolTimes}</if>
            <if test="performerId != null ">and performer_id = #{performerId}</if>
            <if test="performerName != null  and performerName != ''">and performer_name like concat('%',
                #{performerName}, '%')
            </if>
            <if test="decorationInspectionIds != null  and decorationInspectionIds != ''">and decoration_inspection_ids
                = #{decorationInspectionIds}
            </if>
            <if test="executeTime != null ">and execute_time = #{executeTime}</if>
            <if test="status != null ">and status = #{status}</if>
        </where>
        order by create_time desc
    </select>

    <select id="selectDecorationPlanByTaskId" parameterType="String" resultMap="DecorationPlanResult">
        <include refid="selectDecorationPlanVo"/>
        where task_id = #{taskId}
    </select>

    <insert id="insertDecorationPlan" parameterType="DecorationPlan" useGeneratedKeys="true" keyProperty="taskId">
        insert into t_decoration_plan
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="taskId != null and taskId != ''">task_id,</if>
            <if test="roomCode != null ">room_code,</if>
            <if test="roomName != null and roomName != ''">room_name,</if>
            <if test="planName != null and planName != ''">plan_name,</if>
            <if test="validityStart != null ">validity_start,</if>
            <if test="validityEnd != null">validity_end,</if>
            <if test="patrolFrequency != null">patrol_frequency,</if>
            <if test="patrolTimes != null">patrol_times,</if>
            <if test="dailyPatrolTimes != null">daily_patrol_times,</if>
            <if test="performerId != null">performer_id,</if>
            <if test="performerName != null and performerName != ''">performer_name,</if>
            <if test="decorationInspectionIds != null and decorationInspectionIds != ''">decoration_inspection_ids,</if>
            <if test="executeTime != null">execute_time,</if>
            <if test="status != null">status,</if>
            <if test="createTime != null">create_time,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="taskId != null and taskId != ''">#{taskId},</if>
            <if test="roomCode != null and roomCode != ''">#{roomCode},</if>
            <if test="roomName != null and roomName != ''">#{roomName},</if>
            <if test="planName != null and planName != ''">#{planName},</if>
            <if test="validityStart != null">#{validityStart},</if>
            <if test="validityEnd != null ">#{validityEnd},</if>
            <if test="patrolFrequency != null">#{patrolFrequency},</if>
            <if test="patrolTimes != null">#{patrolTimes},</if>
            <if test="dailyPatrolTimes != null">#{dailyPatrolTimes},</if>
            <if test="performerId != null">#{performerId},</if>
            <if test="performerName != null and performerName != ''">#{performerName},</if>
            <if test="decorationInspectionIds != null and decorationInspectionIds != ''">#{decorationInspectionIds},
            </if>
            <if test="executeTime != null ">#{executeTime},</if>
            <if test="status != null">#{status},</if>
            <if test="createTime != null ">#{createTime},</if>
        </trim>
    </insert>

    <update id="updateDecorationPlan" parameterType="DecorationPlan">
        update t_decoration_plan
        <set>
            <if test="roomCode != null">room_code = #{roomCode},</if>
            <if test="roomName != null and roomName != ''">room_name = #{roomName},</if>
            <if test="planName != null and planName != ''">plan_name = #{planName},</if>
            validity_start = #{validityStart},
            validity_end = #{validityEnd},
            <if test="patrolFrequency != null">patrol_frequency = #{patrolFrequency},</if>
            <if test="patrolTimes != null">patrol_times = #{patrolTimes},</if>
            <if test="dailyPatrolTimes != null">daily_patrol_times = #{dailyPatrolTimes},</if>
            <if test="performerId != null">performer_id = #{performerId},</if>
            <if test="performerName != null and performerName != ''">performer_name = #{performerName},</if>
            <if test="decorationInspectionIds != null and decorationInspectionIds != ''">decoration_inspection_ids =
                #{decorationInspectionIds},
            </if>
            <if test="executeTime != null">execute_time = #{executeTime},</if>
            <if test="status != null">status = #{status},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
        </set>
        where task_id = #{taskId}
    </update>

    <delete id="deleteDecorationPlanByTaskId" parameterType="String">
        delete
        from t_decoration_plan
        where task_id = #{taskId}
    </delete>

    <delete id="deleteDecorationPlanByTaskIds" parameterType="String">
        delete from t_decoration_plan where task_id in
        <foreach item="taskId" collection="array" open="(" separator="," close=")">
            #{taskId}
        </foreach>
    </delete>
</mapper>
