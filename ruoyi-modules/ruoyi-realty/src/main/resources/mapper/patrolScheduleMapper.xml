<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ibms.service.realty.web.mapper.PatrolScheduleMapper">

    <resultMap type="PatrolSchedule" id="PatrolScheduleResult">
        <result property="taskId" column="task_id"/>
        <result property="taskName" column="task_name"/>
        <result property="patrolPlanId" column="patrol_plan_id"/>
        <result property="patrolPlanName" column="patrol_plan_name"/>
        <result property="patrolRouteId" column="patrol_route_id"/>
        <result property="patrolRouteName" column="patrol_route_name"/>
        <result property="currentPointId" column="current_point_id"/>
        <result property="plannedStartTime" column="planned_start_time"/>
        <result property="plannedEndTime" column="planned_end_time"/>
        <result property="actualStartTime" column="actual_start_time"/>
        <result property="actualEndTime" column="actual_end_time"/>
        <result property="patroller" column="patroller"/>
        <result property="remark" column="remark"/>
        <result property="remarkImg" column="remark_img"/>
        <result property="status" column="status"/>

        <result property="patrolRouteName" column="patrolRouteName"/>
        <result property="startTime" column="start_time"/>
        <result property="endTime" column="end_time"/>
        <result property="pointName" column="point_name"/>
        <result property="pointId" column="point_id"/>
        <result property="pointSort" column="point_sort"/>
        <result property="pointSignImg" column="point_sign_img"/>

    </resultMap>

    <sql id="selectPatrolScheduleVo">
        select ps.task_id,
        ps.task_name,
        ps.patrol_plan_id,
        ps.patrol_plan_name,
        ps.patrol_route_id,
        ps.patrol_route_name,
        ps.current_point_id,
        ps.planned_start_time,
        ps.planned_end_time,
        ps.actual_start_time,
        ps.actual_end_time,
        ps.patroller,
        ps.remark,
        ps.remark_img,
        ps.status
        from t_patrol_schedule ps
    </sql>

    <select id="selectPatrolScheduleList" parameterType="PatrolSchedule" resultMap="PatrolScheduleResult">
        <include refid="selectPatrolScheduleVo"/>
        <where>
            <if test="taskName != null  and taskName != ''">and ps.task_name like concat('%', #{taskName}, '%')</if>
            <if test="patrolPlanId != null ">and ps.patrol_plan_id = #{patrolPlanId}</if>
            <if test="status != null and status != ''">and ps.status = #{status}</if>
            <if test="patrolRouteId != null ">and ps.patrol_route_id = #{patrol_route_id}</if>
            <if test="patrolRouteName != null  and patrolRouteName != ''">and ps.patrol_route_name like concat('%',
                #{patrolRouteName}, '%')
            </if>
            <if test="currentPointId != null ">and ps.current_point_id = #{currentPointId}</if>
            <if test="plannedStartTime != null ">and ps.planned_start_time >= #{plannedStartTime}</if>
            <if test="plannedEndTime != null ">and ps.planned_end_time <![CDATA[<= #{plannedEndTime}]]></if>
            <if test="patroller != null  and patroller != ''">and ps.patroller like concat('%', #{patroller}, '%')</if>
            <if test="actualStartTime != null ">and ps.actual_start_time = #{actualStartTime}</if>
            <if test="actualEndTime != null ">and ps.actual_end_time = #{actualEndTime}</if>
            <if test="remark != null ">and ps.remark = #{remark}</if>
            <if test="remarkImg != null ">and ps.remark_img = #{remarkImg}</if>
        </where>
        order by ps.planned_start_time desc
    </select>

    <select id="selectPatrolScheduleByTaskId" parameterType="Integer" resultMap="PatrolScheduleResult">
        select ps.task_id,
        ps.task_name,
        ps.patrol_plan_id,
        ps.patrol_plan_name,
        ps.patrol_route_id,
        ps.patrol_route_name,
        ps.current_point_id,
        ps.planned_start_time,
        ps.planned_end_time,
        ps.actual_start_time,
        ps.actual_end_time,
        ps.patroller,
        ps.remark,
        ps.remark_img,
        ps.status,

        pp2.name as point_name,
        pp2.id as point_id,
        prp.sort as point_sort,
        pss.img as point_sign_img
        from t_patrol_schedule ps
        left join t_patrol_plan pp on ps.patrol_plan_id = pp.task_id
        inner join t_patrol_route pr on pp.patrol_route_id = pr.id
        inner join t_patrol_route_point prp on pr.id = prp.patrol_route_id
        inner join t_patrol_point pp2 on pp2.id = prp.patrol_point_id
        left join t_patrol_schedule_sign pss
        on pss.relation_task_id = ps.task_id and pss.patrol_point_id = pp2.id
        where ps.task_id = #{taskId}
        order by prp.sort
    </select>

    <insert id="insertPatrolSchedule" parameterType="PatrolSchedule" useGeneratedKeys="true" keyProperty="taskId">
        insert into t_patrol_schedule
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="taskName != null and taskName != ''">task_name,</if>
            <if test="patrolPlanId != null">patrol_plan_id,</if>
            <if test="patrolPlanName != null and patrolPlanName != ''">patrol_plan_name,</if>
            <if test="patrolRouteId != null">patrol_route_id,</if>
            <if test="patrolRouteName != null and patrolRouteName != ''">patrol_route_name,</if>
            <if test="currentPointId != null">current_point_id,</if>
            <if test="plannedStartTime != null">planned_start_time,</if>
            <if test="plannedEndTime != null">planned_end_time,</if>
            <if test="actualStartTime != null">actual_start_time,</if>
            <if test="actualEndTime != null">actual_end_time,</if>
            <if test="patroller != null and patroller != ''">patroller,</if>
            <if test="remark != null and remark != ''">remark,</if>
            <if test="remarkImg != null and remarkImg != ''">remark_img,</if>
            <if test="status != null">status,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="taskName != null and taskName != ''">#{taskName},</if>
            <if test="patrolPlanId != null">#{patrolPlanId},</if>
            <if test="patrolPlanName != null and patrolPlanName != ''">#{patrolPlanName},</if>
            <if test="patrolRouteId != null">#{patrolRouteId},</if>
            <if test="patrolRouteName != null and patrolRouteName != ''">#{patrolRouteName},</if>
            <if test="currentPointId != null">#{currentPointId},</if>
            <if test="plannedStartTime != null">#{plannedStartTime},</if>
            <if test="plannedEndTime != null">#{plannedEndTime},</if>
            <if test="actualStartTime != null">#{actualStartTime},</if>
            <if test="actualEndTime != null">#{actualEndTime},</if>
            <if test="patroller != null and patroller != ''">#{patroller},</if>
            <if test="remark != null and remark != ''">#{remark},</if>
            <if test="remarkImg != null and remarkImg != ''">#{remarkImg},</if>
            <if test="status != null">#{status},</if>
        </trim>
    </insert>

    <insert id="signImgAdd">
        insert into t_patrol_schedule_sign (relation_task_id, patrol_point_id, img)
        values (#{relationTaskId}, #{patrolPointId}, #{img})
    </insert>

    <update id="updatePatrolSchedule" parameterType="PatrolSchedule">
        update t_patrol_schedule
        <set>
            <if test="taskName != null and taskName != ''">task_name = #{taskName},</if>
            <if test="patrolPlanId != null">patrol_plan_id = #{patrolPlanId},</if>
            <if test="patrolPlanName != null and patrolPlanName != ''">patrol_plan_name = #{patrolPlanName},</if>
            <if test="patrolRouteId != null">patrol_route_id = #{patrolRouteId},</if>
            <if test="patrolRouteName != null and patrolRouteName != ''">patrol_route_name = #{patrolRouteName},</if>
            <if test="currentPointId != null">current_point_id = #{currentPointId},</if>
            <if test="plannedStartTime != null">planned_start_time = #{plannedStartTime},</if>
            <if test="plannedEndTime != null">planned_end_time = #{plannedEndTime},</if>
            <if test="actualStartTime != null">actual_start_time = #{actualStartTime},</if>
            <if test="actualEndTime != null">actual_end_time = #{actualEndTime},</if>
            <if test="patroller != null and patroller != ''">patroller = #{patroller},</if>
            <if test="remark != null and remark != ''">remark = #{remark},</if>
            <if test="remarkImg != null and remarkImg != ''">remark_img = #{remarkImg},</if>
            <if test="status != null">status = #{status},</if>
        </set>
        where task_id = #{taskId}
    </update>

    <delete id="deletePatrolScheduleByTaskId" parameterType="Integer">
        delete
        from t_patrol_schedule
        where task_id = #{taskId}
    </delete>

    <delete id="deletePatrolScheduleByTaskIds" parameterType="String">
        delete from t_patrol_schedule where task_id in
        <foreach item="taskId" collection="array" open="(" separator="," close=")">
            #{taskId}
        </foreach>
    </delete>
</mapper>
