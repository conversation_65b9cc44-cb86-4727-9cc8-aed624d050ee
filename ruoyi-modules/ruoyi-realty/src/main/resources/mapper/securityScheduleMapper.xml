<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ibms.service.realty.web.mapper.SecurityScheduleMapper">

    <resultMap type="SecuritySchedule" id="SecurityScheduleResult">
        <result property="id" column="id"/>
        <result property="securityId" column="security_id"/>
        <result property="securityName" column="security_name"/>
        <result property="postId" column="post_id"/>
        <result property="planId" column="plan_id"/>
        <result property="resourceId" column="resource_id"/>
        <result property="resourceName" column="resource_name"/>
        <result property="remark" column="remark"/>
    </resultMap>

    <resultMap type="SecuritySchedule" id="SecurityScheduleResult2">
        <result property="id" column="id"/>
        <result property="securityId" column="security_id"/>
        <result property="securityName" column="security_name"/>
        <result property="postId" column="post_id"/>
        <result property="planId" column="plan_id"/>
        <result property="resourceId" column="resource_id"/>
        <result property="resourceName" column="resource_name"/>
        <result property="remark" column="remark"/>

        <!--        <association property="securityPost" javaType="com.ibms.service.realty.web.domain.SecurityPost">-->
        <!--            <result property="id" column="spId"/>-->
        <result property="postName" column="post_name"/>
        <result property="startDate" column="start_date"/>
        <!--        </association>-->

        <!--        <association property="dutyPlan" javaType="com.ibms.service.realty.web.domain.DutyPlan">-->
        <!--            <result property="id" column="dpId"/>-->
        <result property="planName" column="dpName"/>
        <result property="restDay" column="rest_day"/>
        <result property="startTime" column="start_time"/>
        <result property="endTime" column="end_time"/>
        <result property="planRemark" column="dpRemark"/>
        <!--        </association>-->
    </resultMap>

    <sql id="selectSecurityScheduleVo">
        select id,
        security_id,
        security_name,
        post_id,
        plan_id,
        resource_id,
        resource_name,
        remark
        from t_security_schedule
    </sql>


    <!--    CREATE TABLE `t_duty_plan` (-->
    <!--    `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键ID',-->
    <!--    `name` varchar(50) DEFAULT NULL COMMENT '名称',-->
    <!--    `rest_day` varchar(20) DEFAULT NULL COMMENT '休息时间(1到7代表周一到周日，多个逗号隔开)',-->
    <!--    `start_time` varchar(10) DEFAULT NULL COMMENT '值班开始时间',-->
    <!--    `end_time` varchar(10) DEFAULT NULL COMMENT '值班结束时间',-->
    <!--    `remark` varchar(200) DEFAULT NULL COMMENT '备注',-->
    <!--    PRIMARY KEY (`id`)-->
    <!--    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='值班方案信息表';-->

    <!--    CREATE TABLE `t_security_post` (-->
    <!--    `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键ID',-->
    <!--    `post_name` varchar(50) DEFAULT NULL COMMENT '岗位名称',-->
    <!--    `start_date` date DEFAULT NULL COMMENT '开始日期',-->
    <!--    PRIMARY KEY (`id`)-->
    <!--    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='保安岗位信息表';-->
    <select id="selectSecurityScheduleList" parameterType="SecuritySchedule" resultMap="SecurityScheduleResult2">
        select ss.id,
        ss.security_id,
        ss.security_name,
        ss.post_id,
        ss.plan_id,
        ss.resource_id,
        ss.resource_name,
        ss.remark,

        sp.id as spId,
        sp.post_name,
        sp.start_date,

        dp.id as dpId,
        dp.name as dpName,
        dp.rest_day,
        dp.start_time,
        dp.end_time,
        dp.remark as dpRemark

        from t_security_schedule ss
        left join t_security_post sp on ss.post_id = sp.id
        left join t_duty_plan dp on ss.plan_id = dp.id
        <where>
            <if test="securityId != null ">and ss.security_id = #{securityId}</if>
            <if test="securityName != null  and securityName != ''">and ss.security_name like concat('%',
                #{securityName},
                '%')
            </if>
            <if test="postId != null ">and ss.post_id = #{postId}</if>
            <if test="planId != null ">and ss.plan_id = #{planId}</if>
            <if test="resourceId != null ">and ss.resource_id = #{resourceId}</if>
            <if test="resourceName != null  and resourceName != ''">and ss.resource_name like concat('%',
                #{resourceName},
                '%')
            </if>
            <if test="startDateParam != null ">and sp.start_date >= #{startDateParam}</if>
            <if test="endDateParam != null ">and sp.start_date <![CDATA[<= #{endDateParam}]]></if>
            <if test="postName != null  and postName != ''">and sp.post_name like concat('%',
                #{postName},
                '%')
            </if>
        </where>
        order by ss.id desc
    </select>

    <select id="selectSecurityScheduleById" parameterType="Integer" resultMap="SecurityScheduleResult">
        <include refid="selectSecurityScheduleVo"/>
        where id = #{id}
    </select>

    <insert id="insertSecuritySchedule" parameterType="SecuritySchedule" useGeneratedKeys="true" keyProperty="id">
        insert into t_security_schedule
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="securityId != null">security_id,</if>
            <if test="securityName != null">security_name,</if>
            <if test="postId != null">post_id,</if>
            <if test="planId != null">plan_id,</if>
            <if test="resourceId != null">resource_id,</if>
            <if test="resourceName != null">resource_name,</if>
            <if test="remark != null">remark,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="securityId != null">#{securityId},</if>
            <if test="securityName != null">#{securityName},</if>
            <if test="postId != null">#{postId},</if>
            <if test="planId != null">#{planId},</if>
            <if test="resourceId != null">#{resourceId},</if>
            <if test="resourceName != null">#{resourceName},</if>
            <if test="remark != null">#{remark},</if>
        </trim>
    </insert>

    <update id="updateSecuritySchedule" parameterType="SecuritySchedule">
        update t_security_schedule
        <trim prefix="SET" suffixOverrides=",">
            <if test="securityId != null">security_id = #{securityId},</if>
            <if test="securityName != null">security_name = #{securityName},</if>
            <if test="postId != null">post_id = #{postId},</if>
            <if test="planId != null">plan_id = #{planId},</if>
            <if test="resourceId != null">resource_id = #{resourceId},</if>
            <if test="resourceName != null">resource_name = #{resourceName},</if>
            <if test="remark != null">remark = #{remark},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteSecurityScheduleById" parameterType="Integer">
        delete
        from t_security_schedule
        where id = #{id}
    </delete>

    <delete id="deleteSecurityScheduleByIds" parameterType="String">
        delete from t_security_schedule where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>
