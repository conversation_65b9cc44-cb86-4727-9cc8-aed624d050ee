<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ibms.service.realty.web.mapper.InstrumentTypeInfoMapper">

    <resultMap type="InstrumentTypeInfo" id="InstrumentTypeInfoResult">
        <result property="id" column="id"/>
        <result property="name" column="name"/>
        <result property="unit" column="unit"/>
        <result property="remark" column="remark"/>
    </resultMap>

    <sql id="selectInstrumentTypeInfoVo">
        select id, name, unit, remark
        from t_instrument_type_info
    </sql>

    <select id="selectInstrumentTypeInfoList" parameterType="InstrumentTypeInfo" resultMap="InstrumentTypeInfoResult">
        <include refid="selectInstrumentTypeInfoVo"/>
        <where>
            <if test="name != null  and name != ''">and name like concat('%', #{name}, '%')</if>
            <if test="unit != null  and unit != ''">and unit = #{unit}</if>
        </where>
        order by id desc
    </select>

    <select id="selectInstrumentTypeInfoById" parameterType="Integer" resultMap="InstrumentTypeInfoResult">
        <include refid="selectInstrumentTypeInfoVo"/>
        where id = #{id}
    </select>

    <insert id="insertInstrumentTypeInfo" parameterType="InstrumentTypeInfo" useGeneratedKeys="true" keyProperty="id">
        insert into t_instrument_type_info
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="name != null and name != ''">name,</if>
            <if test="unit != null and unit != ''">unit,</if>
            <if test="remark != null">remark,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="name != null and name != ''">#{name},</if>
            <if test="unit != null and unit != ''">#{unit},</if>
            <if test="remark != null">#{remark},</if>
        </trim>
    </insert>

    <update id="updateInstrumentTypeInfo" parameterType="InstrumentTypeInfo">
        update t_instrument_type_info
        <trim prefix="SET" suffixOverrides=",">
            <if test="name != null and name != ''">name = #{name},</if>
            <if test="unit != null and unit != ''">unit = #{unit},</if>
            <if test="remark != null">remark = #{remark},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteInstrumentTypeInfoById" parameterType="Integer">
        delete
        from t_instrument_type_info
        where id = #{id}
    </delete>

    <delete id="deleteInstrumentTypeInfoByIds" parameterType="String">
        delete from t_instrument_type_info where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>