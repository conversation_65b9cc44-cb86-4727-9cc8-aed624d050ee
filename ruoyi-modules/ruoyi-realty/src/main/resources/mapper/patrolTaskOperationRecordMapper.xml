<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ibms.service.realty.web.mapper.PatrolTaskOperationRecordMapper">

    <resultMap type="PatrolTaskOperationRecord" id="PatrolTaskOperationRecordResult">
        <result property="id" column="id"/>
        <result property="taskId" column="task_id"/>
        <result property="content" column="content"/>
        <result property="creatTime" column="creat_time"/>
    </resultMap>

    <sql id="selectPatrolTaskOperationRecordVo">
        select id, task_id, content, creat_time from t_patrol_task_operation_record
    </sql>

    <select id="selectPatrolTaskOperationRecordList" parameterType="PatrolTaskOperationRecord"
            resultMap="PatrolTaskOperationRecordResult">
        <include refid="selectPatrolTaskOperationRecordVo"/>
        <where>
            <if test="taskId != null ">and task_id = #{taskId}</if>
            <if test="content != null  and content != ''">and content like concat('%', #{content}, '%')</if>
            <if test="creatTime != null ">and creat_time = #{creatTime}</if>
        </where>
    </select>

    <select id="selectPatrolTaskOperationRecordById" parameterType="Integer"
            resultMap="PatrolTaskOperationRecordResult">
        <include refid="selectPatrolTaskOperationRecordVo"/>
        where id = #{id}
    </select>

    <insert id="insertPatrolTaskOperationRecord" parameterType="PatrolTaskOperationRecord" useGeneratedKeys="true"
            keyProperty="id">
        insert into t_patrol_task_operation_record
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="taskId != null">task_id,</if>
            <if test="content != null and content != ''">content,</if>
            <if test="creatTime != null">creat_time,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="taskId != null">#{taskId},</if>
            <if test="content != null and content != ''">#{content},</if>
            <if test="creatTime != null">#{creatTime},</if>
        </trim>
    </insert>

    <update id="updatePatrolTaskOperationRecord" parameterType="PatrolTaskOperationRecord">
        update t_patrol_task_operation_record
        <trim prefix="SET" suffixOverrides=",">
            <if test="taskId != null">task_id = #{taskId},</if>
            <if test="content != null and content != ''">content = #{content},</if>
            <if test="creatTime != null">creat_time = #{creatTime},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deletePatrolTaskOperationRecordById" parameterType="Integer">
        delete from t_patrol_task_operation_record where id = #{id}
    </delete>

    <delete id="deletePatrolTaskOperationRecordByIds" parameterType="String">
        delete from t_patrol_task_operation_record where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>
