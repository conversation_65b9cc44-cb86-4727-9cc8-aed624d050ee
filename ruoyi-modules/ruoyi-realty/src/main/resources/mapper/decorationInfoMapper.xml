<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ibms.service.realty.web.mapper.DecorationInfoMapper">

    <resultMap type="DecorationInfo" id="DecorationInfoResult">
        <result property="id" column="id"/>
        <result property="name" column="name"/>
        <result property="status" column="status"/>
    </resultMap>

    <sql id="selectDecorationInfoVo">
        select id, name, status
        from t_decoration_info
    </sql>

    <select id="selectDecorationInfoList" parameterType="DecorationInfo" resultMap="DecorationInfoResult">
        <include refid="selectDecorationInfoVo"/>
        <where>
            <if test="name != null  and name != ''">and name like concat('%', #{name}, '%')</if>
            <if test="status != null ">and status = #{status}</if>
        </where>
        order by id desc
    </select>

    <select id="selectDecorationInfoById" parameterType="Integer" resultMap="DecorationInfoResult">
        <include refid="selectDecorationInfoVo"/>
        where id = #{id}
    </select>

    <insert id="insertDecorationInfo" parameterType="DecorationInfo" useGeneratedKeys="true" keyProperty="id">
        insert into t_decoration_info
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="name != null">name,</if>
            <if test="status != null">status,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="name != null">#{name},</if>
            <if test="status != null">#{status},</if>
        </trim>
    </insert>

    <update id="updateDecorationInfo" parameterType="DecorationInfo">
        update t_decoration_info
        <trim prefix="SET" suffixOverrides=",">
            <if test="name != null">name = #{name},</if>
            <if test="status != null">status = #{status},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteDecorationInfoById" parameterType="Integer">
        delete from t_decoration_info where id = #{id}
    </delete>

    <delete id="deleteDecorationInfoByIds" parameterType="String">
        delete from t_decoration_info where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>
