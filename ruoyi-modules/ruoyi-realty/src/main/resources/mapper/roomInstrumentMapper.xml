<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ibms.service.realty.web.mapper.RoomInstrumentMapper">

    <resultMap type="RoomInstrument" id="RoomInstrumentResult">
        <id property="id" column="id"/>
        <result property="number" column="number"/>
        <result property="name" column="name"/>
        <result property="roomCode" column="room_code"/>
        <result property="instrumentTypeId" column="instrument_type_id"/>

        <result property="instrumentTypeName" column="instrumentTypeName"/>
        <result property="roomName" column="roomName"/>
        <result property="floorName" column="floorName"/>
        <result property="buildingName" column="buildingName"/>
        <result property="roomId" column="roomId"/>
        <result property="floorId" column="floorId"/>
        <result property="buildingId" column="buildingId"/>
    </resultMap>

    <sql id="selectRoomInstrumentVo">
        SELECT tri.id,
        tri.number,
        tri.name,
        tri.room_code,
        tri.instrument_type_id,

        titi.name AS instrumentTypeName,
        tpri.name AS roomName,
        tpri.id AS roomId,
        tpri2.name AS floorName,
        tpri2.id AS floorId,
        tpri3.name AS buildingName,
        tpri3.id AS buildingId


        FROM t_room_instrument tri
        LEFT JOIN t_instrument_type_info titi ON tri.instrument_type_id = titi.id
        LEFT JOIN t_property_resource_info tpri ON tri.room_code = tpri.id
        LEFT JOIN t_property_resource_info tpri2 ON tpri.parent_id = tpri2.id
        LEFT JOIN t_property_resource_info tpri3 ON tpri2.parent_id = tpri3.id
    </sql>

    <select id="selectRoomInstrumentList" parameterType="RoomInstrument" resultMap="RoomInstrumentResult">
        <include refid="selectRoomInstrumentVo"/>
        <where>
            <if test="number != null and number != ''">and tri.number = #{number}</if>
            <if test="name != null and name != ''">and tri.name = #{name}</if>
            <if test="roomCode != null">and tri.room_code = #{roomCode}</if>
            <if test="instrumentTypeId != null">and tri.instrument_type_id = #{instrumentTypeId}</if>

            <if test="roomId != null">and tri.room_code = #{roomId}</if>
            <if test="floorId != null">and tpri2.id = #{floorId}</if>
            <if test="buildingId != null">and tpri3.id = #{buildingId}</if>

        </where>
        order by tri.id desc
    </select>

    <select id="selectRoomInstrumentById" parameterType="Integer" resultMap="RoomInstrumentResult">
        <include refid="selectRoomInstrumentVo"/>
        where tri.id = #{id}
    </select>

    <insert id="insertRoomInstrument" parameterType="RoomInstrument" useGeneratedKeys="true" keyProperty="id">
        insert into t_room_instrument
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="number != null and number != ''">number,</if>
            <if test="name != null and name != ''">name,</if>
            <if test="roomCode != null">room_code,</if>
            <if test="instrumentTypeId != null">instrument_type_id,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="number != null and number != ''">#{number},</if>
            <if test="name != null and name != ''">#{name},</if>
            <if test="roomCode != null">#{roomCode},</if>
            <if test="instrumentTypeId != null">#{instrumentTypeId},</if>
        </trim>
    </insert>

    <update id="updateRoomInstrument" parameterType="RoomInstrument">
        update t_room_instrument
        <set>
            <if test="number != null and number != ''">number = #{number},</if>
            <if test="name != null and name != ''">name = #{name},</if>
            <if test="roomCode != null">room_code = #{roomCode},</if>
            <if test="instrumentTypeId != null">instrument_type_id = #{instrumentTypeId},</if>
        </set>
        where id = #{id}
    </update>

    <delete id="deleteRoomInstrumentById" parameterType="Integer">
        DELETE
        FROM t_room_instrument
        WHERE id = #{id}
    </delete>

    <delete id="deleteRoomInstrumentByIds" parameterType="String">
        delete from t_room_instrument where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>
