<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ibms.service.realty.web.mapper.DecorationTaskOperationRecordMapper">

    <resultMap type="DecorationTaskOperationRecord" id="DecorationTaskOperationRecordResult">
        <result property="id" column="id"/>
        <result property="taskId" column="task_id"/>
        <result property="content" column="content"/>
        <result property="creatTime" column="creat_time"/>
    </resultMap>

    <sql id="selectDecorationTaskOperationRecordVo">
        select id, task_id, content, creat_time
        from t_decoration_task_operation_record
    </sql>

    <select id="selectDecorationTaskOperationRecordList" parameterType="DecorationTaskOperationRecord"
            resultMap="DecorationTaskOperationRecordResult">
        <include refid="selectDecorationTaskOperationRecordVo"/>
        <where>
            <if test="taskId != null  and taskId != ''">and task_id = #{taskId}</if>
            <if test="content != null  and content != ''">and content = #{content}</if>
            <if test="creatTime != null ">and creat_time = #{creatTime}</if>
        </where>
        order by creat_time desc
    </select>

    <select id="selectDecorationTaskOperationRecordById" parameterType="Integer"
            resultMap="DecorationTaskOperationRecordResult">
        <include refid="selectDecorationTaskOperationRecordVo"/>
        where id = #{id}
    </select>

    <insert id="insertDecorationTaskOperationRecord" parameterType="DecorationTaskOperationRecord"
            useGeneratedKeys="true" keyProperty="id">
        insert into t_decoration_task_operation_record
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="taskId != null and taskId != ''">task_id,</if>
            <if test="content != null and content != ''">content,</if>
            <if test="creatTime != null">creat_time,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="taskId != null and taskId != ''">#{taskId},</if>
            <if test="content != null and content != ''">#{content},</if>
            <if test="creatTime != null">#{creatTime},</if>
        </trim>
    </insert>

    <update id="updateDecorationTaskOperationRecord" parameterType="DecorationTaskOperationRecord">
        update t_decoration_task_operation_record
        <trim prefix="SET" suffixOverrides=",">
            <if test="taskId != null and taskId != ''">task_id = #{taskId},</if>
            <if test="content != null and content != ''">content = #{content},</if>
            <if test="creatTime != null">creat_time = #{creatTime},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteDecorationTaskOperationRecordById" parameterType="Integer">
        delete
        from t_decoration_task_operation_record
        where id = #{id}
    </delete>

    <delete id="deleteDecorationTaskOperationRecordByIds" parameterType="String">
        delete from t_decoration_task_operation_record where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>
