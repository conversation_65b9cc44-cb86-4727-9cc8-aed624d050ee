<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ibms.service.realty.web.mapper.ItemRegistrationMapper">

    <resultMap type="ItemRegistration" id="ItemRegistrationResult">
        <result property="id" column="id"/>
        <result property="managementAreaId" column="management_area_id"/>
        <result property="itemName" column="item_name"/>
        <result property="quantity" column="quantity"/>
        <result property="carrier" column="carrier"/>
        <result property="takeOutTime" column="take_out_time"/>
        <result property="takeInTime" column="take_in_time"/>
        <result property="idNumber" column="id_number"/>
        <result property="contactNumber" column="contact_number"/>
        <result property="staffOnDutyId" column="staff_on_duty_id"/>
        <result property="staffOnDutyName" column="staff_on_duty_name"/>
        <result property="licensePlateNumber" column="license_plate_number"/>
        <result property="remarks" column="remarks"/>
    </resultMap>

    <sql id="selectItemRegistrationVo">
        select id,
        management_area_id,
        item_name,
        quantity,
        carrier,
        take_out_time,
        take_in_time,
        id_number,
        contact_number,
        staff_on_duty,
        license_plate_number,
        remarks
        from t_item_registration
    </sql>

    <select id="selectItemRegistrationList" parameterType="ItemRegistration" resultMap="ItemRegistrationResult">
        <include refid="selectItemRegistrationVo"/>
        <where>
            <if test="managementAreaId != null ">and management_area_id = #{managementAreaId}</if>
            <if test="itemName != null  and itemName != ''">and item_name like concat('%', #{itemName}, '%')</if>
            <if test="quantity != null ">and quantity = #{quantity}</if>
            <if test="carrier != null  and carrier != ''">and carrier = #{carrier}</if>
            <if test="takeOutTime != null ">and take_out_time = #{takeOutTime}</if>
            <if test="takeInTime != null ">and take_in_time = #{takeInTime}</if>
            <if test="idNumber != null  and idNumber != ''">and id_number = #{idNumber}</if>
            <if test="contactNumber != null  and contactNumber != ''">and contact_number = #{contactNumber}</if>
            <if test="staffOnDutyId != null">and staff_on_duty_id = #{staffOnDutyId}</if>
            <if test="staffOnDutyName != null  and staffOnDutyName != ''">and staff_on_duty_name = #{staffOnDutyName}
            </if>
            <if test="licensePlateNumber != null  and licensePlateNumber != ''">and license_plate_number =
                #{licensePlateNumber}
            </if>
            <if test="remarks != null  and remarks != ''">and remarks = #{remarks}</if>
        </where>
        order by id desc
    </select>

    <select id="selectItemRegistrationById" parameterType="Integer" resultMap="ItemRegistrationResult">
        <include refid="selectItemRegistrationVo"/>
        where id = #{id}
    </select>

    <insert id="insertItemRegistration" parameterType="ItemRegistration" useGeneratedKeys="true" keyProperty="id">
        insert into t_item_registration
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="managementAreaId != null">management_area_id,</if>
            <if test="itemName != null and itemName != ''">item_name,</if>
            <if test="quantity != null">quantity,</if>
            <if test="carrier != null and carrier != ''">carrier,</if>
            <if test="takeOutTime != null">take_out_time,</if>
            <if test="takeInTime != null">take_in_time,</if>
            <if test="idNumber != null">id_number,</if>
            <if test="contactNumber != null">contact_number,</if>
            <if test="staffOnDutyId != null">staff_on_duty_id,</if>
            <if test="staffOnDutyName != null and staffOnDutyName != ''">staff_on_duty_name,</if>
            <if test="licensePlateNumber != null">license_plate_number,</if>
            <if test="remarks != null">remarks,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="managementAreaId != null">#{managementAreaId},</if>
            <if test="itemName != null and itemName != ''">#{itemName},</if>
            <if test="quantity != null">#{quantity},</if>
            <if test="carrier != null and carrier != ''">#{carrier},</if>
            <if test="takeOutTime != null">#{takeOutTime},</if>
            <if test="takeInTime != null">#{takeInTime},</if>
            <if test="idNumber != null">#{idNumber},</if>
            <if test="contactNumber != null">#{contactNumber},</if>
            <if test="staffOnDutyId != null">#{staffOnDutyId},</if>
            <if test="staffOnDutyName != null">#{staffOnDutyName},</if>
            <if test="licensePlateNumber != null">#{licensePlateNumber},</if>
            <if test="remarks != null">#{remarks},</if>
        </trim>
    </insert>

    <update id="updateItemRegistration" parameterType="ItemRegistration">
        update t_item_registration
        <trim prefix="SET" suffixOverrides=",">
            <if test="managementAreaId != null">management_area_id = #{managementAreaId},</if>
            <if test="itemName != null and itemName != ''">item_name = #{itemName},</if>
            <if test="quantity != null">quantity = #{quantity},</if>
            <if test="carrier != null and carrier != ''">carrier = #{carrier},</if>
            <if test="takeOutTime != null">take_out_time = #{takeOutTime},</if>
            <if test="takeInTime != null">take_in_time = #{takeInTime},</if>
            <if test="idNumber != null">id_number = #{idNumber},</if>
            <if test="contactNumber != null">contact_number = #{contactNumber},</if>
            <if test="staffOnDutyId != null">staff_on_duty_id = #{staffOnDutyId},</if>
            <if test="staffOnDutyName != null and staffOnDutyName != ''">staff_on_duty_name = #{staffOnDutyName},</if>
            <if test="licensePlateNumber != null">license_plate_number = #{licensePlateNumber},</if>
            <if test="remarks != null">remarks = #{remarks},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteItemRegistrationById" parameterType="Integer">
        delete
        from t_item_registration
        where id = #{id}
    </delete>

    <delete id="deleteItemRegistrationByIds" parameterType="String">
        delete from t_item_registration where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>
