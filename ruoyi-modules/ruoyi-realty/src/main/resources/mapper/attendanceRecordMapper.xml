<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ibms.service.realty.web.mapper.AttendanceRecordMapper">

    <resultMap type="AttendanceRecord" id="AttendanceRecordResult">
        <result property="id" column="id"/>
        <result property="userId" column="user_id"/>
        <result property="name" column="name"/>
        <result property="onDutyDate" column="on_duty_date"/>
        <result property="onDutyTime" column="on_duty_time"/>
        <result property="onDutyCheckinTime" column="on_duty_checkin_time"/>
        <result property="onDutyResult" column="on_duty_result"/>
        <result property="onDutyRemark" column="on_duty_remark"/>
        <result property="offDutyDate" column="off_duty_date"/>
        <result property="offDutyTime" column="off_duty_time"/>
        <result property="offDutyCheckinTime" column="off_duty_checkin_time"/>
        <result property="offDutyResult" column="off_duty_result"/>
        <result property="offDutyRemark" column="off_duty_remark"/>

        <result property="dutyDate" column="dutyDate"/>
        <result property="dutyTime" column="dutyTime"/>
        <result property="dutyCheckinTime" column="dutyCheckinTime"/>
        <result property="dutyResult" column="dutyResult"/>
        <result property="dutyRemark" column="dutyRemark"/>
    </resultMap>

    <sql id="selectAttendanceRecordVo">
        SELECT id,
        user_id,
        name,
        on_duty_date,
        on_duty_time,
        on_duty_checkin_time,
        on_duty_result,
        on_duty_remark,
        off_duty_date,
        off_duty_time,
        off_duty_checkin_time,
        off_duty_result,
        off_duty_remark
        FROM t_attendance_record
    </sql>

    <select id="selectAttendanceRecordList" parameterType="AttendanceRecord" resultMap="AttendanceRecordResult">
        SELECT * FROM (
        SELECT id,
        user_id,
        name,
        on_duty_date as dutyDate,
        concat('上班',on_duty_time) as dutyTime,
        on_duty_checkin_time as dutyCheckinTime,
        on_duty_result as dutyResult,
        on_duty_remark as dutyRemark
        FROM t_attendance_record WHERE on_duty_checkin_time IS NOT null
        UNION ALL
        SELECT id,
        user_id,
        name,
        off_duty_date as dutyDate,
        concat('下班',off_duty_time) as dutyTime,
        off_duty_checkin_time as dutyCheckinTime,
        off_duty_result as dutyResult,
        off_duty_remark as dutyRemark
        FROM t_attendance_record WHERE off_duty_checkin_time IS NOT null ) t

        <where>
            <if test="userId != null ">and user_id = #{userId}</if>
            <if test="name != null  and name != ''">and name like concat('%', #{name}, '%')</if>
            <if test="dutyDate != null ">and dutyDate = #{dutyDate}</if>
            <if test="dutyResult != null ">and dutyResult = #{dutyResult}</if>
            <!--            <if test="onDutyDate != null ">and on_duty_date = #{onDutyDate}</if>-->
            <!--            <if test="onDutyTime != null  and onDutyTime != ''">and on_duty_time = #{onDutyTime}</if>-->
            <!--            <if test="onDutyCheckinTime != null ">and on_duty_checkin_time = #{onDutyCheckinTime}</if>-->
            <!--            <if test="onDutyResult != null ">and on_duty_result = #{onDutyResult}</if>-->
            <!--            <if test="offDutyDate != null ">and off_duty_date = #{offDutyDate}</if>-->
            <!--            <if test="offDutyTime != null  and offDutyTime != ''">and off_duty_time = #{offDutyTime}</if>-->
            <!--            <if test="offDutyCheckinTime != null ">and off_duty_checkin_time = #{offDutyCheckinTime}</if>-->
            <!--            <if test="offDutyResult != null ">and off_duty_result = #{offDutyResult}</if>-->
        </where>
        ORDER BY t.dutyDate DESC
    </select>

    <select id="selectAttendanceRecordById" parameterType="Integer" resultMap="AttendanceRecordResult">
        <include refid="selectAttendanceRecordVo"/>
        where id = #{id}
    </select>
    <select id="attendanceStatisticsList"
            resultType="com.ibms.service.realty.web.controller.attendanceRecord.vo.AttendanceStatisticsVo">

        SELECT name,
        COUNT(user_id) AS attendanceDays,
        30 - COUNT(user_id) AS restDays,
        SUM(CASE WHEN on_duty_result = 1 THEN 1 ELSE 0 END) AS lateDays,
        0 AS lateMinutes,
        0 AS earlyLeaveDays,
        0 AS earlyLeaveMinutes,
        0 AS onDutyLackCardDays,
        0 AS offDutyLackCardDays
        FROM t_attendance_record
        <where>
            <if test="name != null and name != ''">and name like concat('%', #{name}, '%')</if>
            <if test="startDate != null and endDate != null">and on_duty_date between #{startDate} and #{endDate}</if>
        </where>

        GROUP BY user_id
    </select>

    <insert id="insertAttendanceRecord" parameterType="AttendanceRecord" useGeneratedKeys="true" keyProperty="id">
        insert into t_attendance_record
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="userId != null">user_id,</if>
            <if test="name != null and name != ''">name,</if>
            <if test="onDutyDate != null">on_duty_date,</if>
            <if test="onDutyTime != null and onDutyTime != ''">on_duty_time,</if>
            <if test="onDutyCheckinTime != null">on_duty_checkin_time,</if>
            <if test="onDutyResult != null">on_duty_result,</if>
            <if test="onDutyRemark != null">on_duty_remark,</if>
            <if test="offDutyDate != null">off_duty_date,</if>
            <if test="offDutyTime != null and offDutyTime != ''">off_duty_time,</if>
            <if test="offDutyCheckinTime != null">off_duty_checkin_time,</if>
            <if test="offDutyResult != null">off_duty_result,</if>
            <if test="offDutyRemark != null">off_duty_remark,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="userId != null">#{userId},</if>
            <if test="name != null and name != ''">#{name},</if>
            <if test="onDutyDate != null">#{onDutyDate},</if>
            <if test="onDutyTime != null and onDutyTime != ''">#{onDutyTime},</if>
            <if test="onDutyCheckinTime != null">#{onDutyCheckinTime},</if>
            <if test="onDutyResult != null">#{onDutyResult},</if>
            <if test="onDutyRemark != null">#{onDutyRemark},</if>
            <if test="offDutyDate != null">#{offDutyDate},</if>
            <if test="offDutyTime != null and offDutyTime != ''">#{offDutyTime},</if>
            <if test="offDutyCheckinTime != null">#{offDutyCheckinTime},</if>
            <if test="offDutyResult != null">#{offDutyResult},</if>
            <if test="offDutyRemark != null">#{offDutyRemark},</if>
        </trim>
    </insert>

    <update id="updateAttendanceRecord" parameterType="AttendanceRecord">
        update t_attendance_record
        <trim prefix="SET" suffixOverrides=",">
            <if test="userId != null">user_id = #{userId},</if>
            <if test="name != null and name != ''">name = #{name},</if>
            <if test="onDutyDate != null">on_duty_date = #{onDutyDate},</if>
            <if test="onDutyTime != null and onDutyTime != ''">on_duty_time = #{onDutyTime},</if>
            <if test="onDutyCheckinTime != null">on_duty_checkin_time = #{onDutyCheckinTime},</if>
            <if test="onDutyResult != null">on_duty_result = #{onDutyResult},</if>
            <if test="onDutyRemark != null">on_duty_remark = #{onDutyRemark},</if>
            <if test="offDutyDate != null">off_duty_date = #{offDutyDate},</if>
            <if test="offDutyTime != null and offDutyTime != ''">off_duty_time = #{offDutyTime},</if>
            <if test="offDutyCheckinTime != null">off_duty_checkin_time = #{offDutyCheckinTime},</if>
            <if test="offDutyResult != null">off_duty_result = #{offDutyResult},</if>
            <if test="offDutyRemark != null">off_duty_remark = #{offDutyRemark},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteAttendanceRecordById" parameterType="Integer">
        DELETE
        FROM t_attendance_record
        WHERE id = #{id}
    </delete>

    <delete id="deleteAttendanceRecordByIds" parameterType="String">
        delete from t_attendance_record where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>
