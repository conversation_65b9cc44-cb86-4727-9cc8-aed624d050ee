<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ibms.service.realty.web.mapper.SecurityFacilitiesMapper">

    <resultMap type="SecurityFacilities" id="SecurityFacilitiesResult">
        <result property="id" column="id"/>
        <result property="name" column="name"/>
        <result property="facilityType" column="facility_type"/>
        <result property="departmentId" column="department_id"/>
        <result property="departmentName" column="department_name"/>
        <result property="location" column="location"/>
        <result property="quantity" column="quantity"/>
        <result property="specification" column="specification"/>
        <result property="managerId" column="manager_id"/>
        <result property="managerName" column="manager_name"/>
        <result property="remark" column="remark"/>
    </resultMap>

    <sql id="selectSecurityFacilitiesVo">
        select id,
        name,
        facility_type,
        department_id,
        department_name,
        location,
        quantity,
        specification,
        manager_id,
        manager_name,
        remark
        from t_security_facilities
    </sql>

    <select id="selectSecurityFacilitiesList" parameterType="SecurityFacilities" resultMap="SecurityFacilitiesResult">
        <include refid="selectSecurityFacilitiesVo"/>
        <where>
            <if test="name != null  and name != ''">and name like concat('%', #{name}, '%')</if>
            <if test="remark != null  and remark != ''">and remark like concat('%', #{remark}, '%')</if>
            <if test="facilityType != null ">and facility_type = #{facilityType}</if>
            <if test="departmentId != null ">and department_id = #{departmentId}</if>
            <if test="departmentName != null  and departmentName != ''">and department_name like concat('%',
                #{departmentName}, '%')
            </if>
            <if test="location != null  and location != ''">and location = #{location}</if>
            <if test="quantity != null ">and quantity = #{quantity}</if>
            <if test="specification != null  and specification != ''">and specification = #{specification}</if>
            <if test="managerId != null ">and manager_id = #{managerId}</if>
            <if test="managerName != null  and managerName != ''">and manager_name like concat('%', #{managerName},
                '%')
            </if>
        </where>
        order by id desc
    </select>

    <select id="selectSecurityFacilitiesById" parameterType="Integer" resultMap="SecurityFacilitiesResult">
        <include refid="selectSecurityFacilitiesVo"/>
        where id = #{id}
    </select>

    <insert id="insertSecurityFacilities" parameterType="SecurityFacilities" useGeneratedKeys="true" keyProperty="id">
        insert into t_security_facilities
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="name != null">name,</if>
            <if test="facilityType != null">facility_type,</if>
            <if test="departmentId != null">department_id,</if>
            <if test="departmentName != null">department_name,</if>
            <if test="location != null">location,</if>
            <if test="quantity != null">quantity,</if>
            <if test="specification != null">specification,</if>
            <if test="managerId != null">manager_id,</if>
            <if test="managerName != null">manager_name,</if>
            <if test="remark != null">remark,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="name != null">#{name},</if>
            <if test="facilityType != null">#{facilityType},</if>
            <if test="departmentId != null">#{departmentId},</if>
            <if test="departmentName != null">#{departmentName},</if>
            <if test="location != null">#{location},</if>
            <if test="quantity != null">#{quantity},</if>
            <if test="specification != null">#{specification},</if>
            <if test="managerId != null">#{managerId},</if>
            <if test="managerName != null">#{managerName},</if>
            <if test="remark != null">#{remark},</if>
        </trim>
    </insert>

    <update id="updateSecurityFacilities" parameterType="SecurityFacilities">
        update t_security_facilities
        <trim prefix="SET" suffixOverrides=",">
            <if test="name != null">name = #{name},</if>
            <if test="facilityType != null">facility_type = #{facilityType},</if>
            <if test="departmentId != null">department_id = #{departmentId},</if>
            <if test="departmentName != null">department_name = #{departmentName},</if>
            <if test="location != null">location = #{location},</if>
            <if test="quantity != null">quantity = #{quantity},</if>
            <if test="specification != null">specification = #{specification},</if>
            <if test="managerId != null">manager_id = #{managerId},</if>
            <if test="managerName != null">manager_name = #{managerName},</if>
            <if test="remark != null">remark = #{remark},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteSecurityFacilitiesById" parameterType="Integer">
        delete
        from t_security_facilities
        where id = #{id}
    </delete>

    <delete id="deleteSecurityFacilitiesByIds" parameterType="String">
        delete from t_security_facilities where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>
