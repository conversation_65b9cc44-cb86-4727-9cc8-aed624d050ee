<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ibms.service.realty.web.mapper.DecorationApplicationSettingsMapper">

    <resultMap type="DecorationApplicationSettings" id="DecorationApplicationSettingsResult">
        <result property="id" column="id"/>
        <result property="notice" column="notice"/>
        <result property="attachmentUrl" column="attachment_url"/>
    </resultMap>

    <sql id="selectDecorationApplicationSettingsVo">
        select id, notice, attachment_url
        from t_decoration_application_settings
    </sql>

    <select id="selectDecorationApplicationSettingsList" parameterType="DecorationApplicationSettings"
            resultMap="DecorationApplicationSettingsResult">
        <include refid="selectDecorationApplicationSettingsVo"/>
        <where>
            <if test="notice != null  and notice != ''">and notice = #{notice}</if>
            <if test="attachmentUrl != null  and attachmentUrl != ''">and attachment_url = #{attachmentUrl}</if>
        </where>
        order by id desc
    </select>

    <select id="selectDecorationApplicationSettingsById" parameterType="Integer"
            resultMap="DecorationApplicationSettingsResult">
        <include refid="selectDecorationApplicationSettingsVo"/>
        where id = #{id}
    </select>

    <insert id="insertDecorationApplicationSettings" parameterType="DecorationApplicationSettings"
            useGeneratedKeys="true" keyProperty="id">
        insert into t_decoration_application_settings
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="notice != null">notice,</if>
            <if test="attachmentUrl != null">attachment_url,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="notice != null">#{notice},</if>
            <if test="attachmentUrl != null">#{attachmentUrl},</if>
        </trim>
    </insert>

    <update id="updateDecorationApplicationSettings" parameterType="DecorationApplicationSettings">
        update t_decoration_application_settings
        <trim prefix="SET" suffixOverrides=",">
            <if test="notice != null">notice = #{notice},</if>
            <if test="attachmentUrl != null">attachment_url = #{attachmentUrl},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteDecorationApplicationSettingsById" parameterType="Integer">
        delete
        from t_decoration_application_settings
        where id = #{id}
    </delete>

    <delete id="deleteDecorationApplicationSettingsByIds" parameterType="String">
        delete from t_decoration_application_settings where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>
