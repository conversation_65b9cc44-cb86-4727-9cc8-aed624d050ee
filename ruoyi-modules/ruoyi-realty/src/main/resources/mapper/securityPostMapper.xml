<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ibms.service.realty.web.mapper.SecurityPostMapper">

    <!--    CREATE TABLE t_security_schedule (-->
    <!--    id INT(11) NOT NULL AUTO_INCREMENT COMMENT '主键ID',-->
    <!--    security_id INT(11) DEFAULT NULL COMMENT '保安ID，对应用户表ID',-->
    <!--    security_name VARCHAR(50) DEFAULT NULL COMMENT '保安姓名',-->
    <!--    post_id INT(11) DEFAULT NULL COMMENT '保安岗位ID，对应保安岗位信息表ID',-->
    <!--    plan_id INT(11) DEFAULT NULL COMMENT '值班方案ID，对应值班方案信息表ID',-->
    <!--    resource_id INT(11) DEFAULT NULL COMMENT '值班位置，对应物业资源信息表ID',-->
    <!--    resource_name VARCHAR(50) DEFAULT NULL COMMENT '位置名称',-->
    <!--    remark VARCHAR(200) DEFAULT NULL COMMENT '备注',-->
    <!--    PRIMARY KEY (id)-->
    <!--    ) ENGINE=INNODB DEFAULT CHARSET=utf8mb4 COMMENT='保安排班表';-->

    <resultMap type="SecurityPost" id="SecurityPostResult">
        <result property="id" column="id"/>
        <result property="postName" column="post_name"/>
        <result property="startDate" column="start_date"/>
        <result property="scheduleCount" column="scheduleCount"/>
    </resultMap>


    <resultMap type="SecurityPost" id="SecurityPostResult2">
        <result property="id" column="id"/>
        <result property="postName" column="post_name"/>
        <result property="startDate" column="start_date"/>
        <result property="scheduleCount" column="scheduleCount"/>
        <collection property="securityScheduleList"
                    ofType="com.ibms.service.realty.web.domain.SecuritySchedule">
            <result property="id" column="schedule_id"/>
            <result property="securityId" column="security_id"/>
            <result property="securityName" column="security_name"/>
            <result property="postId" column="post_id"/>
            <result property="planId" column="plan_id"/>
            <result property="resourceId" column="resource_id"/>
            <result property="resourceName" column="resource_name"/>
            <result property="remark" column="remark"/>
            <!--            <association property="dutyPlan">-->
            <!--                <result property="planId" column="plan_id"/>-->
            <result property="planName" column="plan_name"/>
            <result property="restDay" column="rest_day"/>
            <result property="startTime" column="start_time"/>
            <result property="endTime" column="end_time"/>
            <result property="planRemark" column="plan_remark"/>
            <!--            </association>-->
        </collection>
    </resultMap>

    <!--    <resultMap type="SecuritySchedule" id="SecurityScheduleResult">-->
    <!--        <result property="id" column="schedule_id"/>-->
    <!--        <result property="securityId" column="security_id"/>-->
    <!--        <result property="securityName" column="security_name"/>-->
    <!--        <result property="postId" column="post_id"/>-->
    <!--        <result property="planId" column="plan_id"/>-->
    <!--        <result property="resourceId" column="resource_id"/>-->
    <!--        <result property="resourceName" column="resource_name"/>-->
    <!--        <result property="remark" column="remark"/>-->
    <!--        <association property="dutyPlan">-->
    <!--            <result property="id" column="plan_id"/>-->
    <!--            <result property="name" column="plan_name"/>-->
    <!--            <result property="restDay" column="rest_day"/>-->
    <!--            <result property="startTime" column="start_time"/>-->
    <!--            <result property="endTime" column="end_time"/>-->
    <!--            <result property="remark" column="plan_remark"/>-->
    <!--        </association>-->
    <!--    </resultMap>-->

    <sql id="selectSecurityPostVo">
        select id, post_name, start_date
        from t_security_post
    </sql>

    <select id="selectSecurityPostList" parameterType="SecurityPost" resultMap="SecurityPostResult">
        select sp.id, sp.post_name, sp.start_date,count(ss.id) as scheduleCount
        from t_security_post sp left join t_security_schedule ss on sp.id = ss.post_id
        <where>
            <if test="postName != null  and postName != ''">and post_name like concat('%', #{postName}, '%')</if>
            <if test="startDate != null ">and start_date = #{startDate}</if>
        </where>
        group by sp.id
        order by sp.id desc
    </select>

    <!--    CREATE TABLE `t_duty_plan` (-->
    <!--    `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键ID',-->
    <!--    `name` varchar(50) DEFAULT NULL COMMENT '名称',-->
    <!--    `rest_day` varchar(20) DEFAULT NULL COMMENT '休息时间(休息时间(1到7代表周一到周日，多个逗号隔开))',-->
    <!--    `start_time` varchar(10) DEFAULT NULL COMMENT '值班开始时间',-->
    <!--    `end_time` varchar(10) DEFAULT NULL COMMENT '值班结束时间',-->
    <!--    `remark` varchar(200) DEFAULT NULL COMMENT '备注',-->
    <!--    PRIMARY KEY (`id`)-->
    <!--    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='值班方案信息表';-->
    <select id="selectSecurityPostById" parameterType="Integer" resultMap="SecurityPostResult2">
        select sp.id,
        sp.post_name,
        sp.start_date,

        ss.id as schedule_id,
        ss.security_id,
        ss.security_name,
        ss.post_id,
        ss.plan_id,
        ss.resource_id,
        ss.resource_name,
        ss.remark,

        dp.id as plan_id,
        dp.name as plan_name,
        dp.rest_day,
        dp.start_time,
        dp.end_time,
        dp.remark as plan_remark
        from t_security_post sp
        left join t_security_schedule ss on sp.id = ss.post_id
        left join t_duty_plan dp on ss.plan_id = dp.id
        where sp.id = #{id}
    </select>
    <!--    <select id="getScheduleById" resultMap="SecurityScheduleResult">-->
    <!--        select tss.id,-->
    <!--               tss.security_id,-->
    <!--               tss.security_name,-->
    <!--               tss.post_id,-->
    <!--               tss.plan_id,-->
    <!--               tss.resource_id,-->
    <!--               tss.resource_name,-->
    <!--               tss.remark,-->
    <!--               tdp.id     as plan_id,-->
    <!--               tdp.name   as plan_name,-->
    <!--               tdp.rest_day,-->
    <!--               tdp.start_time,-->
    <!--               tdp.end_time,-->
    <!--               tdp.remark as plan_remark-->
    <!--        from t_security_schedule tss-->
    <!--                 left join t_duty_plan tdp on tss.plan_id = tdp.id-->
    <!--        where tss.id = #{id}-->

    <!--    </select>-->

    <insert id="insertSecurityPost" parameterType="SecurityPost" useGeneratedKeys="true" keyProperty="id">
        insert into t_security_post
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="postName != null">post_name,</if>
            <if test="startDate != null">start_date,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="postName != null">#{postName},</if>
            <if test="startDate != null">#{startDate},</if>
        </trim>
    </insert>

    <update id="updateSecurityPost" parameterType="SecurityPost">
        update t_security_post
        <trim prefix="SET" suffixOverrides=",">
            <if test="postName != null">post_name = #{postName},</if>
            <if test="startDate != null">start_date = #{startDate},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteSecurityPostById" parameterType="Integer">
        delete
        from t_security_post
        where id = #{id}
    </delete>

    <delete id="deleteSecurityPostByIds" parameterType="Integer">
        delete from t_security_post where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>
