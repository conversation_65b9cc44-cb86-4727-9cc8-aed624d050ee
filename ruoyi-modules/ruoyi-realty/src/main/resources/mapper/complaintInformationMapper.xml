<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ibms.service.realty.web.mapper.ComplaintInformationMapper">

    <resultMap type="ComplaintInformation" id="ComplaintInformationResult">
        <result property="complaintId" column="complaint_id"/>
        <result property="complaintTitle" column="complaint_title"/>
        <result property="complaintContent" column="complaint_content"/>
        <result property="replyContent" column="reply_content"/>
        <result property="status" column="status"/>
        <result property="replyerId" column="replyer_id"/>
        <result property="replyerName" column="replyer_name"/>
        <result property="complainantId" column="complainant_id"/>
        <result property="contactPhone" column="contact_phone"/>
        <result property="submitTime" column="submit_time"/>
        <result property="complainantName" column="complainant_name"/>
        <result property="imgUrl" column="img_url"/>
    </resultMap>

    <sql id="selectComplaintInformationVo">
        select complaint_id,
        complaint_title,
        complaint_content,
        reply_content,
        status,
        replyer_id,
        replyer_name,
        complainant_id,
        contact_phone,
        submit_time,
        complainant_name,
        img_url
        from t_complaint_information
    </sql>

    <select id="selectComplaintInformationList" parameterType="ComplaintInformation"
            resultMap="ComplaintInformationResult">
        <include refid="selectComplaintInformationVo"/>
        <where>
            <if test="complaintTitle != null  and complaintTitle != ''">and complaint_title = #{complaintTitle}</if>
            <if test="complaintContent != null  and complaintContent != ''">and complaint_content =
                #{complaintContent}
            </if>
            <if test="replyContent != null  and replyContent != ''">and reply_content = #{replyContent}</if>
            <if test="status != null ">and status = #{status}</if>
            <if test="replyerId != null ">and replyer_id = #{replyerId}</if>
            <if test="replyerName != null  and replyerName != ''">and replyer_name like concat('%', #{replyerName},
                '%')
            </if>
            <if test="complainantId != null ">and complainant_id = #{complainantId}</if>
            <if test="contactPhone != null  and contactPhone != ''">and contact_phone = #{contactPhone}</if>
            <if test="submitTime != null ">and submit_time = #{submitTime}</if>
            <if test="complainantName != null  and complainantName != ''">and complainant_name like concat('%',
                #{complainantName}, '%')
            </if>
            <if test="imgUrl != null  and imgUrl != ''">and img_url = #{imgUrl}</if>
        </where>
        order by submit_time desc
    </select>

    <select id="selectComplaintInformationByComplaintId" parameterType="String" resultMap="ComplaintInformationResult">
        <include refid="selectComplaintInformationVo"/>
        where complaint_id = #{complaintId}
    </select>

    <insert id="insertComplaintInformation" parameterType="ComplaintInformation">
        insert into t_complaint_information
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="complaintId != null">complaint_id,</if>
            <if test="complaintTitle != null">complaint_title,</if>
            <if test="complaintContent != null">complaint_content,</if>
            <if test="replyContent != null">reply_content,</if>
            <if test="status != null">status,</if>
            <if test="replyerId != null">replyer_id,</if>
            <if test="replyerName != null">replyer_name,</if>
            <if test="complainantId != null">complainant_id,</if>
            <if test="contactPhone != null">contact_phone,</if>
            <if test="submitTime != null">submit_time,</if>
            <if test="complainantName != null">complainant_name,</if>
            <if test="imgUrl != null">img_url,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="complaintId != null">#{complaintId},</if>
            <if test="complaintTitle != null">#{complaintTitle},</if>
            <if test="complaintContent != null">#{complaintContent},</if>
            <if test="replyContent != null">#{replyContent},</if>
            <if test="status != null">#{status},</if>
            <if test="replyerId != null">#{replyerId},</if>
            <if test="replyerName != null">#{replyerName},</if>
            <if test="complainantId != null">#{complainantId},</if>
            <if test="contactPhone != null">#{contactPhone},</if>
            <if test="submitTime != null">#{submitTime},</if>
            <if test="complainantName != null">#{complainantName},</if>
            <if test="imgUrl != null">#{imgUrl},</if>
        </trim>
    </insert>

    <update id="updateComplaintInformation" parameterType="ComplaintInformation">
        update t_complaint_information
        <trim prefix="SET" suffixOverrides=",">
            <if test="complaintTitle != null">complaint_title = #{complaintTitle},</if>
            <if test="complaintContent != null">complaint_content = #{complaintContent},</if>
            <if test="replyContent != null">reply_content = #{replyContent},</if>
            <if test="status != null">status = #{status},</if>
            <if test="replyerId != null">replyer_id = #{replyerId},</if>
            <if test="replyerName != null">replyer_name = #{replyerName},</if>
            <if test="complainantId != null">complainant_id = #{complainantId},</if>
            <if test="contactPhone != null">contact_phone = #{contactPhone},</if>
            <if test="submitTime != null">submit_time = #{submitTime},</if>
            <if test="complainantName != null">complainant_name = #{complainantName},</if>
            <if test="imgUrl != null">img_url = #{imgUrl},</if>
        </trim>
        where complaint_id = #{complaintId}
    </update>

    <delete id="deleteComplaintInformationByComplaintId" parameterType="String">
        delete
        from t_complaint_information
        where complaint_id = #{complaintId}
    </delete>

    <delete id="deleteComplaintInformationByComplaintIds" parameterType="String">
        delete from t_complaint_information where complaint_id in
        <foreach item="complaintId" collection="array" open="(" separator="," close=")">
            #{complaintId}
        </foreach>
    </delete>
</mapper>
