<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ibms.service.realty.web.mapper.CleaningToolManagementMapper">

    <resultMap type="CleaningToolManagement" id="CleaningToolManagementResult">
        <result property="id" column="id"/>
        <result property="toolName" column="tool_name"/>
        <result property="toolType" column="tool_type"/>
        <result property="entryDate" column="entry_date"/>
        <result property="totalQuantity" column="total_quantity"/>
        <result property="stockQuantity" column="stock_quantity"/>
        <result property="receivedQuantity" column="received_quantity"/>
        <result property="toolDescription" column="tool_description"/>
        <result property="manufacturer" column="manufacturer"/>
        <result property="model" column="model"/>
        <result property="purchaseDate" column="purchase_date"/>
        <result property="serviceLife" column="service_life"/>
        <result property="usageScope" column="usage_scope"/>
        <result property="ownership" column="ownership"/>
        <result property="otherInformation" column="other_information"/>
        <!--        <collection property="cleaningToolBorrowReturnRecordList" ofType="CleaningToolBorrowReturnRecord">-->
        <!--            <result property="id" column="borrow_return_record_id"/>-->
        <!--            <result property="borrowerId" column="borrower_id"/>-->
        <!--            <result property="borrower" column="borrower"/>-->
        <!--            <result property="borrowDate" column="borrow_date"/>-->
        <!--            <result property="borrowQuantity" column="borrow_quantity"/>-->
        <!--            <result property="returnQuantity" column="return_quantity"/>-->
        <!--            <result property="returnDate" column="return_date"/>-->
        <!--            <result property="borrowerRemark" column="borrower_remark"/>-->
        <!--            <result property="returnRemark" column="return_remark"/>-->
        <!--        </collection>-->
    </resultMap>

    <sql id="selectCleaningToolManagementVo">
        select id,
        tool_name,
        tool_type,
        entry_date,
        total_quantity,
        stock_quantity,
        tool_description,
        (total_quantity - stock_quantity) as received_quantity,
        manufacturer,
        model,
        purchase_date,
        service_life,
        usage_scope,
        ownership,
        other_information
        from t_cleaning_tool_management
    </sql>

    <select id="selectCleaningToolManagementList" parameterType="CleaningToolManagement"
            resultMap="CleaningToolManagementResult">
        <include refid="selectCleaningToolManagementVo"/>
        <where>
            <if test="toolName != null  and toolName != ''">and tool_name like concat('%', #{toolName}, '%')</if>
            <if test="toolType != null  and toolType != ''">and tool_type = #{toolType}</if>
            <if test="entryDate != null ">and entry_date = #{entryDate}</if>
            <if test="totalQuantity != null ">and total_quantity = #{totalQuantity}</if>
            <if test="stockQuantity != null ">and stock_quantity = #{stockQuantity}</if>
            <if test="toolDescription != null  and toolDescription != ''">and tool_description = #{toolDescription}</if>
            <if test="manufacturer != null  and manufacturer != ''">and manufacturer = #{manufacturer}</if>
            <if test="model != null  and model != ''">and model = #{model}</if>
            <if test="purchaseDate != null ">and purchase_date = #{purchaseDate}</if>
            <if test="serviceLife != null ">and service_life = #{serviceLife}</if>
            <if test="usageScope != null  and usageScope != ''">and usage_scope = #{usageScope}</if>
            <if test="ownership != null  and ownership != ''">and ownership = #{ownership}</if>
            <if test="otherInformation != null  and otherInformation != ''">and other_information =
                #{otherInformation}
            </if>
        </where>
        order by entry_date desc
    </select>
    <!--    CREATE TABLE `t_cleaning_tool_borrow_return_record` (-->
    <!--    `id` INT(11) NOT NULL AUTO_INCREMENT COMMENT '主键id',-->
    <!--    `borrower` VARCHAR(50) NOT NULL COMMENT '领用人',-->
    <!--    `borrow_date` DATE NOT NULL COMMENT '领用日期',-->
    <!--    `borrow_quantity` INT(11) NOT NULL COMMENT '领用数量',-->
    <!--    `return_quantity` INT(11) COMMENT '归还数量',-->
    <!--    `return_date` DATE COMMENT '归还日期',-->
    <!--    PRIMARY KEY (`id`)-->
    <!--    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='保洁工具领用归还记录表';-->

    <select id="selectCleaningToolManagementById" parameterType="Integer" resultMap="CleaningToolManagementResult">
        select tctm.id,
        tctm.tool_name,
        tctm.tool_type,
        tctm.entry_date,
        tctm.total_quantity,
        tctm.stock_quantity,
        tctm.tool_description,
        tctm.manufacturer,
        tctm.model,
        tctm.purchase_date,
        tctm.service_life,
        tctm.usage_scope,
        tctm.ownership,
        tctm.other_information
        # tctbrrr.id as borrow_return_record_id,
        # tctbrrr.borrower_id,
        # tctbrrr.borrower,
        # tctbrrr.borrow_date,
        # tctbrrr.borrow_quantity,
        # tctbrrr.return_quantity,
        # tctbrrr.return_date,
        # tctbrrr.borrower_remark,
        # tctbrrr.return_remark
        from t_cleaning_tool_management tctm
        # left join t_cleaning_tool_borrow_return_record tctbrrr on tctm.id = tctbrrr.tool_id
        where tctm.id = #{id}
    </select>
    <select id="selectCleaningToolBorrowReturnRecordList"
            resultType="com.ibms.service.realty.web.domain.CleaningToolBorrowReturnRecord">
        select id,
        tool_id as toolId,
        borrower_id as borrowerId,
        borrower as borrower,
        borrow_date as borrowDate,
        borrow_quantity as borrowQuantity,
        return_quantity as returnQuantity,
        return_date as returnDate,
        borrower_remark as borrowerRemark,
        return_remark as returnRemark

        from t_cleaning_tool_borrow_return_record
        <where>
            <if test="toolId != null">and tool_id = #{toolId}</if>
            <if test="borrowerId != null">and borrower_id = #{borrowerId}</if>
            <if test="borrowDate != null">and borrow_date = #{borrowDate}</if>
            <if test="borrowQuantity != null">and borrow_quantity = #{borrowQuantity}</if>
            <if test="returnQuantity != null">and return_quantity = #{returnQuantity}</if>
            <if test="returnDate != null">and return_date = #{returnDate}</if>
        </where>
    </select>

    <insert id="insertCleaningToolManagement" parameterType="CleaningToolManagement" useGeneratedKeys="true"
            keyProperty="id">
        insert into t_cleaning_tool_management
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="toolName != null and toolName != ''">tool_name,</if>
            <if test="toolType != null and toolType != ''">tool_type,</if>
            <if test="entryDate != null">entry_date,</if>
            <if test="totalQuantity != null">total_quantity,</if>
            <if test="stockQuantity != null">stock_quantity,</if>
            <if test="toolDescription != null">tool_description,</if>
            <if test="manufacturer != null">manufacturer,</if>
            <if test="model != null">model,</if>
            <if test="purchaseDate != null">purchase_date,</if>
            <if test="serviceLife != null">service_life,</if>
            <if test="usageScope != null">usage_scope,</if>
            <if test="ownership != null">ownership,</if>
            <if test="otherInformation != null">other_information,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="toolName != null and toolName != ''">#{toolName},</if>
            <if test="toolType != null and toolType != ''">#{toolType},</if>
            <if test="entryDate != null">#{entryDate},</if>
            <if test="totalQuantity != null">#{totalQuantity},</if>
            <if test="stockQuantity != null">#{stockQuantity},</if>
            <if test="toolDescription != null">#{toolDescription},</if>
            <if test="manufacturer != null">#{manufacturer},</if>
            <if test="model != null">#{model},</if>
            <if test="purchaseDate != null">#{purchaseDate},</if>
            <if test="serviceLife != null">#{serviceLife},</if>
            <if test="usageScope != null">#{usageScope},</if>
            <if test="ownership != null">#{ownership},</if>
            <if test="otherInformation != null">#{otherInformation},</if>
        </trim>
    </insert>

    <insert id="insertCleaningToolBorrowReturnRecord" parameterType="CleaningToolBorrowReturnRecord"
            useGeneratedKeys="true"
            keyProperty="id">
        INSERT INTO t_cleaning_tool_borrow_return_record
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="toolId != null">tool_id,</if>
            <if test="borrowerId != null">borrower_id,</if>
            <if test="borrower != null and borrower != ''">borrower,</if>
            <if test="borrowDate != null">borrow_date,</if>
            <if test="borrowQuantity != null">borrow_quantity,</if>
            <if test="returnQuantity != null">return_quantity,</if>
            <if test="returnDate != null">return_date,</if>
            <if test="borrowerRemark != null">borrower_remark,</if>
            <if test="returnRemark != null">return_remark,</if>
        </trim>
        <trim prefix="VALUES (" suffix=")" suffixOverrides=",">
            <if test="toolId != null">#{toolId},</if>
            <if test="borrowerId != null">#{borrowerId},</if>
            <if test="borrower != null and borrower != ''">#{borrower},</if>
            <if test="borrowDate != null">#{borrowDate},</if>
            <if test="borrowQuantity != null">#{borrowQuantity},</if>
            <if test="returnQuantity != null">#{returnQuantity},</if>
            <if test="returnDate != null">#{returnDate},</if>
            <if test="borrowerRemark != null">#{borrowerRemark},</if>
            <if test="returnRemark != null">#{returnRemark},</if>
        </trim>
    </insert>


    <update id="updateCleaningToolManagement" parameterType="CleaningToolManagement">
        update t_cleaning_tool_management
        <trim prefix="SET" suffixOverrides=",">
            <if test="toolName != null and toolName != ''">tool_name = #{toolName},</if>
            <if test="toolType != null and toolType != ''">tool_type = #{toolType},</if>
            <if test="entryDate != null">entry_date = #{entryDate},</if>
            <if test="totalQuantity != null">total_quantity = #{totalQuantity},</if>
            <if test="stockQuantity != null">stock_quantity = #{stockQuantity},</if>
            <if test="toolDescription != null">tool_description = #{toolDescription},</if>
            <if test="manufacturer != null">manufacturer = #{manufacturer},</if>
            <if test="model != null">model = #{model},</if>
            <if test="purchaseDate != null">purchase_date = #{purchaseDate},</if>
            <if test="serviceLife != null">service_life = #{serviceLife},</if>
            <if test="usageScope != null">usage_scope = #{usageScope},</if>
            <if test="ownership != null">ownership = #{ownership},</if>
            <if test="otherInformation != null">other_information = #{otherInformation},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteCleaningToolManagementById" parameterType="Integer">
        delete
        from t_cleaning_tool_management
        where id = #{id}
    </delete>

    <delete id="deleteCleaningToolManagementByIds" parameterType="String">
        delete from t_cleaning_tool_management where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>
