<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ibms.service.realty.web.mapper.PatrolPlanMapper">

    <resultMap type="PatrolPlan" id="PatrolPlanResult">
        <result property="taskId" column="task_id"/>
        <result property="planName" column="plan_name"/>
        <result property="validityStart" column="validity_start"/>
        <result property="validityEnd" column="validity_end"/>
        <result property="patrolRouteId" column="patrol_route_id"/>
        <result property="patrolFrequency" column="patrol_frequency"/>
        <result property="dailyPatrolTimes" column="daily_patrol_times"/>
        <result property="patrolTimes" column="patrol_times"/>
        <result property="taskPerformer" column="task_performer"/>
        <result property="patrolRouteStr" column="patrol_route_str"/>
        <result property="taskPerformerName" column="task_performer_name"/>
        <result property="operator" column="operator"/>
        <result property="operatorName" column="operator_name"/>
        <result property="createTime" column="create_time"/>
        <result property="status" column="status"/>
        <result property="excuteTime" column="excute_time"/>
        <result property="createBy" column="create_by"/>
        <result property="createByName" column="create_by_name"/>

    </resultMap>

    <sql id="selectPatrolPlanVo">
        select pp.*, pr.name as patrol_route_str
        from t_patrol_plan pp left join t_patrol_route pr on pp.patrol_route_id = pr.id
    </sql>

    <select id="selectPatrolPlanList" parameterType="PatrolPlan" resultMap="PatrolPlanResult">
        <include refid="selectPatrolPlanVo"/>
        <where>
            <if test="planName != null  and planName != ''">and pp.plan_name like concat('%', #{planName}, '%')</if>
            <if test="patrolRouteStr != null  and patrolRouteStr != ''">and pr.name like concat('%', #{patrolRouteStr},
                '%')
            </if>
            <if test="validityStart != null ">and pp.validity_start <![CDATA[<= #{validityStart}]]></if>
            <if test="validityEnd != null ">and pp.validity_end >= #{validityEnd}</if>
            <if test="patrolRouteId != null  and patrolRouteId != ''">and pp.patrol_route_id = #{patrolRouteId}</if>
            <if test="patrolFrequency != null ">and pp.patrol_frequency = #{patrolFrequency}</if>
            <if test="dailyPatrolTimes != null ">and pp.daily_patrol_times = #{dailyPatrolTimes}</if>
            <if test="taskPerformer != null ">and pp.task_performer = #{taskPerformer}</if>
            <if test="createByName != null and createByName != ''">
                and pp.create_by_name like concat('%', #{createByName}, '%')
            </if>
            <if test="statusList != null and statusList.size() > 0">
                <foreach collection="statusList" item="status" open="and pp.status in (" close=")" separator=",">
                    #{status}
                </foreach>
            </if>
        </where>
        order by pp.create_time desc
    </select>

    <select id="selectPatrolPlanByTaskId" parameterType="String" resultMap="PatrolPlanResult">
        <include refid="selectPatrolPlanVo"/>
        where task_id = #{taskId}
    </select>

    <insert id="insertPatrolPlan" parameterType="PatrolPlan">
        insert into t_patrol_plan
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="taskId != null">task_id,</if>
            <if test="planName != null and planName != ''">plan_name,</if>
            <if test="validityStart != null">validity_start,</if>
            <if test="validityEnd != null">validity_end,</if>
            <if test="patrolRouteId != null and patrolRouteId != ''">patrol_route_id,</if>
            <if test="patrolFrequency != null">patrol_frequency,</if>
            <if test="patrolTimes != null">patrol_times,</if>
            <if test="dailyPatrolTimes != null">daily_patrol_times,</if>
            <if test="taskPerformer != null">task_performer,</if>
            <if test="taskPerformerName != null">task_performer_name,</if>
            <if test="operator != null">operator,</if>
            <if test="operatorName != null">operator_name,</if>
            <if test="createTime != null">create_time,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createByName != null">create_by_name,</if>
            <if test="status != null">status,</if>
            <if test="excuteTime != null">excute_time,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="taskId != null">#{taskId},</if>
            <if test="planName != null and planName != ''">#{planName},</if>
            <if test="validityStart != null">#{validityStart},</if>
            <if test="validityEnd != null">#{validityEnd},</if>
            <if test="patrolRouteId != null and patrolRouteId != ''">#{patrolRouteId},</if>
            <if test="patrolFrequency != null">#{patrolFrequency},</if>
            <if test="patrolTimes != null">#{patrolTimes},</if>
            <if test="dailyPatrolTimes != null">#{dailyPatrolTimes},</if>
            <if test="taskPerformer != null">#{taskPerformer},</if>
            <if test="taskPerformerName != null">#{taskPerformerName},</if>
            <if test="operator != null">#{operator},</if>
            <if test="operatorName != null">#{operatorName},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createByName != null">#{createByName},</if>
            <if test="status != null">#{status},</if>
            <if test="excuteTime != null">#{excuteTime},</if>
        </trim>
    </insert>

    <update id="updatePatrolPlan" parameterType="PatrolPlan">
        update t_patrol_plan
        <trim prefix="SET" suffixOverrides=",">
            <if test="planName != null and planName != ''">plan_name = #{planName},</if>
            <if test="validityStart != null">validity_start = #{validityStart},</if>
            <if test="validityEnd != null">validity_end = #{validityEnd},</if>
            <if test="patrolRouteId != null and patrolRouteId != ''">patrol_route_id = #{patrolRouteId},</if>
            <if test="patrolTimes != null">patrol_times = #{patrolTimes},</if>
            <if test="patrolFrequency != null">patrol_frequency = #{patrolFrequency},</if>
            <if test="dailyPatrolTimes != null">daily_patrol_times = #{dailyPatrolTimes},</if>
            <if test="taskPerformer != null">task_performer = #{taskPerformer},</if>
            <if test="taskPerformerName != null">task_performer_name = #{taskPerformerName},</if>
            <if test="operator != null">operator = #{operator},</if>
            <if test="operatorName != null">operator_name = #{operatorName},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createByName != null">create_by_name = #{createByName},</if>
            <if test="status != null">status = #{status},</if>
            <if test="excuteTime != null">excute_time = #{excuteTime},</if>
        </trim>
        where task_id = #{taskId}
    </update>

    <delete id="deletePatrolPlanByTaskId" parameterType="String">
        delete
        from t_patrol_plan
        where task_id = #{taskId}
    </delete>

    <delete id="deletePatrolPlanByTaskIds" parameterType="String">
        delete from t_patrol_plan where task_id in
        <foreach item="taskId" collection="array" open="(" separator="," close=")">
            #{taskId}
        </foreach>
    </delete>
</mapper>