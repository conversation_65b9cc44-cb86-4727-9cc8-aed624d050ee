<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ibms.service.realty.web.mapper.VehicleInformationMapper">

    <resultMap type="VehicleInformation" id="VehicleInformationResult">
        <result property="id" column="id"/>
        <result property="licensePlateNumber" column="license_plate_number"/>
        <result property="vehicleBrandId" column="vehicle_brand_id"/>
        <result property="vehicleTypeId" column="vehicle_type_id"/>
        <result property="chassisNumber" column="chassis_number"/>
        <result property="engineNumber" column="engine_number"/>
        <result property="vehicleModel" column="vehicle_model"/>
        <result property="vehicleStatus" column="vehicle_status"/>
        <result property="defaultDriver" column="default_driver"/>
        <result property="purchaseDate" column="purchase_date"/>
        <result property="seatCount" column="seat_count"/>
        <result property="loadCapacity" column="load_capacity"/>
        <result property="departmentId" column="department_id"/>
        <result property="departmentName" column="department_name"/>
        <result property="remark" column="remark"/>
        <result property="attachmentUrl" column="attachment_url"/>

        <result property="vehicleBrandName" column="vehicle_brand_name"/>
        <result property="vehicleTypeName" column="vehicle_type_name"/>
    </resultMap>

    <sql id="selectVehicleInformationVo">
        SELECT vi.id,
        vi.license_plate_number,
        vi.vehicle_brand_id,
        vi.vehicle_type_id,
        vi.chassis_number,
        vi.engine_number,
        vi.vehicle_model,
        vi.vehicle_status,
        vi.default_driver,
        vi.purchase_date,
        vi.seat_count,
        vi.load_capacity,
        vi.department_id,
        vi.department_name,
        vi.remark,
        vi.attachment_url,

        ps.value AS vehicle_brand_name,
        ps2.value AS vehicle_type_name
        FROM t_vehicle_information vi
        LEFT JOIN t_parameter_settings ps ON vi.vehicle_brand_id = ps.id
        LEFT JOIN t_parameter_settings ps2 ON vi.vehicle_type_id = ps2.id
    </sql>

    <select id="selectVehicleInformationList" parameterType="VehicleInformation" resultMap="VehicleInformationResult">
        <include refid="selectVehicleInformationVo"/>
        <where>
            <if test="licensePlateNumber != null  and licensePlateNumber != ''">and vi.license_plate_number like
                concat('%',
                #{licensePlateNumber}, '%')
            </if>
            <if test="vehicleBrandId != null ">and vi.vehicle_brand_id = #{vehicleBrandId}</if>
            <if test="vehicleTypeId != null ">and vi.vehicle_type_id = #{vehicleTypeId}</if>
            <if test="chassisNumber != null  and chassisNumber != ''">and vi.chassis_number = #{chassisNumber}</if>
            <if test="engineNumber != null  and engineNumber != ''">and vi.engine_number = #{engineNumber}</if>
            <if test="vehicleModel != null  and vehicleModel != ''">and vi.vehicle_model = #{vehicleModel}</if>
            <if test="vehicleStatus != null  and vehicleStatus != ''">and vi.vehicle_status = #{vehicleStatus}</if>
            <if test="defaultDriver != null  and defaultDriver != ''">and vi.default_driver = #{defaultDriver}</if>
            <if test="purchaseDate != null ">and vi.purchase_date = #{purchaseDate}</if>
            <if test="seatCount != null ">and vi.seat_count = #{seatCount}</if>
            <if test="loadCapacity != null ">and vi.load_capacity = #{loadCapacity}</if>
            <if test="departmentId != null ">and vi.department_id = #{departmentId}</if>
            <if test="departmentName != null  and departmentName != ''">and vi.department_name like concat('%',
                #{departmentName}, '%')
            </if>
            <if test="attachmentUrl != null  and attachmentUrl != ''">and vi.attachment_url = #{attachmentUrl}</if>
        </where>
        order by vi.id desc
    </select>

    <select id="selectVehicleInformationById" parameterType="Integer" resultMap="VehicleInformationResult">
        <include refid="selectVehicleInformationVo"/>
        where vi.id = #{id}
    </select>

    <insert id="insertVehicleInformation" parameterType="VehicleInformation" useGeneratedKeys="true" keyProperty="id">
        insert into t_vehicle_information
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="licensePlateNumber != null">license_plate_number,</if>
            <if test="vehicleBrandId != null">vehicle_brand_id,</if>
            <if test="vehicleTypeId != null">vehicle_type_id,</if>
            <if test="chassisNumber != null">chassis_number,</if>
            <if test="engineNumber != null">engine_number,</if>
            <if test="vehicleModel != null">vehicle_model,</if>
            <if test="vehicleStatus != null">vehicle_status,</if>
            <if test="defaultDriver != null">default_driver,</if>
            <if test="purchaseDate != null">purchase_date,</if>
            <if test="seatCount != null">seat_count,</if>
            <if test="loadCapacity != null">load_capacity,</if>
            <if test="departmentId != null">department_id,</if>
            <if test="departmentName != null">department_name,</if>
            <if test="remark != null">remark,</if>
            <if test="attachmentUrl != null">attachment_url,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="licensePlateNumber != null">#{licensePlateNumber},</if>
            <if test="vehicleBrandId != null">#{vehicleBrandId},</if>
            <if test="vehicleTypeId != null">#{vehicleTypeId},</if>
            <if test="chassisNumber != null">#{chassisNumber},</if>
            <if test="engineNumber != null">#{engineNumber},</if>
            <if test="vehicleModel != null">#{vehicleModel},</if>
            <if test="vehicleStatus != null">#{vehicleStatus},</if>
            <if test="defaultDriver != null">#{defaultDriver},</if>
            <if test="purchaseDate != null">#{purchaseDate},</if>
            <if test="seatCount != null">#{seatCount},</if>
            <if test="loadCapacity != null">#{loadCapacity},</if>
            <if test="departmentId != null">#{departmentId},</if>
            <if test="departmentName != null">#{departmentName},</if>
            <if test="remark != null">#{remark},</if>
            <if test="attachmentUrl != null">#{attachmentUrl},</if>
        </trim>
    </insert>

    <update id="updateVehicleInformation" parameterType="VehicleInformation">
        update t_vehicle_information
        <trim prefix="SET" suffixOverrides=",">
            <if test="licensePlateNumber != null">license_plate_number = #{licensePlateNumber},</if>
            <if test="vehicleBrandId != null">vehicle_brand_id = #{vehicleBrandId},</if>
            <if test="vehicleTypeId != null">vehicle_type_id = #{vehicleTypeId},</if>
            <if test="chassisNumber != null">chassis_number = #{chassisNumber},</if>
            <if test="engineNumber != null">engine_number = #{engineNumber},</if>
            <if test="vehicleModel != null">vehicle_model = #{vehicleModel},</if>
            <if test="vehicleStatus != null">vehicle_status = #{vehicleStatus},</if>
            <if test="defaultDriver != null">default_driver = #{defaultDriver},</if>
            <if test="purchaseDate != null">purchase_date = #{purchaseDate},</if>
            <if test="seatCount != null">seat_count = #{seatCount},</if>
            <if test="loadCapacity != null">load_capacity = #{loadCapacity},</if>
            <if test="departmentId != null">department_id = #{departmentId},</if>
            <if test="departmentName != null">department_name = #{departmentName},</if>
            <if test="remark != null">remark = #{remark},</if>
            <if test="attachmentUrl != null">attachment_url = #{attachmentUrl},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteVehicleInformationById" parameterType="Integer">
        DELETE
        FROM t_vehicle_information
        WHERE id = #{id}
    </delete>

    <delete id="deleteVehicleInformationByIds" parameterType="String">
        delete from t_vehicle_information where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>
