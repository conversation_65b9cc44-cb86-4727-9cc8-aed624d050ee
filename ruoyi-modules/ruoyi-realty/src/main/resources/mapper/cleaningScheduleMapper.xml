<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ibms.service.realty.web.mapper.CleaningScheduleMapper">

    <resultMap type="CleaningSchedule" id="CleaningScheduleResult">
        <result property="taskId" column="task_id"/>
        <result property="taskName" column="task_name"/>
        <result property="cleanPlanId" column="clean_plan_id"/>
        <result property="cleanPlanName" column="clean_plan_name"/>
        <result property="regionId" column="region_id"/>
        <result property="regionName" column="region_name"/>
        <result property="status" column="status"/>
        <result property="plannedStartTime" column="planned_start_time"/>
        <result property="plannedEndTime" column="planned_end_time"/>
        <result property="patroller" column="patroller"/>
        <result property="patrollerName" column="patroller_name"/>
        <result property="actualStartTime" column="actual_start_time"/>
        <result property="actualEndTime" column="actual_end_time"/>
        <result property="remark" column="remark"/>
        <result property="img" column="img"/>
    </resultMap>

    <sql id="selectCleaningScheduleVo">
        select task_id,
        task_name,
        clean_plan_id,
        clean_plan_name,
        region_id,
        region_name,
        status,
        planned_start_time,
        planned_end_time,
        patroller,
        patroller_name,
        actual_start_time,
        actual_end_time,
        remark,
        img
        from t_cleaning_schedule
    </sql>

    <select id="selectCleaningScheduleList" parameterType="CleaningSchedule" resultMap="CleaningScheduleResult">
        <include refid="selectCleaningScheduleVo"/>
        <where>
            <if test="taskName != null  and taskName != ''">and task_name like concat('%', #{taskName}, '%')</if>
            <if test="cleanPlanId != null ">and clean_plan_id = #{cleanPlanId}</if>
            <if test="regionId != null ">and region_id = #{regionId}</if>
            <if test="regionName != null and regionName != ''">and region_name like concat('%', #{regionName}, '%')</if>
            <if test="cleanPlanName != null  and cleanPlanName != ''">and clean_plan_name like concat('%',
                #{cleanPlanName}, '%')
            </if>
            <if test="status != null and status != ''">and status = #{status}</if>
            <if test="plannedStartTime != null ">and DATE_FORMAT(planned_start_time,'%y-%m-%d') >=
                DATE_FORMAT(#{plannedStartTime},'%y-%m-%d')
            </if>
            <if test="plannedEndTime != null ">and DATE_FORMAT(planned_end_time,'%y-%m-%d')
                <![CDATA[<=DATE_FORMAT(#{plannedEndTime},'%y-%m-%d')]]></if>
            <if test="patroller != null ">and patroller = #{patroller}</if>
            <if test="patrollerName != null and patrollerName != ''">and patroller_name like concat('%',
                #{patrollerName}, '%')
            </if>
            <if test="actualStartTime != null ">and actual_start_time = #{actualStartTime}</if>
            <if test="actualEndTime != null ">and actual_end_time = #{actualEndTime}</if>
        </where>
        order by planned_start_time desc
    </select>

    <select id="selectCleaningScheduleByTaskId" parameterType="Integer" resultMap="CleaningScheduleResult">
        <include refid="selectCleaningScheduleVo"/>
        where task_id = #{taskId}
    </select>

    <insert id="insertCleaningSchedule" parameterType="CleaningSchedule" useGeneratedKeys="true" keyProperty="taskId">
        insert into t_cleaning_schedule
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="taskName != null and taskName != ''">task_name,</if>
            <if test="cleanPlanId != null">clean_plan_id,</if>
            <if test="cleanPlanName != null and cleanPlanName != ''">clean_plan_name,</if>
            <if test="regionId != null">region_id,</if>
            <if test="regionName != null and regionName != ''">region_name,</if>
            <if test="status != null">status,</if>
            <if test="plannedStartTime != null">planned_start_time,</if>
            <if test="plannedEndTime != null">planned_end_time,</if>
            <if test="patroller != null">patroller,</if>
            <if test="patrollerName != null">patroller_name,</if>
            <if test="actualStartTime != null">actual_start_time,</if>
            <if test="actualEndTime != null">actual_end_time,</if>
            <if test="remark != null">remark,</if>
            <if test="img != null">img,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="taskName != null and taskName != ''">#{taskName},</if>
            <if test="cleanPlanId != null">#{cleanPlanId},</if>
            <if test="cleanPlanName != null and cleanPlanName != ''">#{cleanPlanName},</if>
            <if test="regionId != null">#{regionId},</if>
            <if test="regionName != null">#{regionName},</if>
            <if test="status != null">#{status},</if>
            <if test="plannedStartTime != null">#{plannedStartTime},</if>
            <if test="plannedEndTime != null">#{plannedEndTime},</if>
            <if test="patroller != null">#{patroller},</if>
            <if test="patrollerName != null">#{patrollerName},</if>
            <if test="actualStartTime != null">#{actualStartTime},</if>
            <if test="actualEndTime != null">#{actualEndTime},</if>
            <if test="remark != null">#{remark},</if>
            <if test="img != null">#{img},</if>
        </trim>
    </insert>

    <update id="updateCleaningSchedule" parameterType="CleaningSchedule">
        update t_cleaning_schedule
        <trim prefix="SET" suffixOverrides=",">
            <if test="taskName != null and taskName != ''">task_name = #{taskName},</if>
            <if test="cleanPlanId != null">clean_plan_id = #{cleanPlanId},</if>
            <if test="cleanPlanName != null and cleanPlanName != ''">clean_plan_name = #{cleanPlanName},</if>
            <if test="regionId != null">region_id = #{regionId},</if>
            <if test="regionName != null and regionName != ''">region_name = #{regionName},</if>
            <if test="status != null">status = #{status},</if>
            <if test="plannedStartTime != null">planned_start_time = #{plannedStartTime},</if>
            <if test="plannedEndTime != null">planned_end_time = #{plannedEndTime},</if>
            <if test="patroller != null">patroller = #{patroller},</if>
            <if test="patrollerName != null">patroller_name = #{patrollerName},</if>
            <if test="actualStartTime != null">actual_start_time = #{actualStartTime},</if>
            <if test="actualEndTime != null">actual_end_time = #{actualEndTime},</if>
            <if test="remark != null">remark = #{remark},</if>
            <if test="img != null">img = #{img},</if>
        </trim>
        where task_id = #{taskId}
    </update>

    <delete id="deleteCleaningScheduleByTaskId" parameterType="Integer">
        delete
        from t_cleaning_schedule
        where task_id = #{taskId}
    </delete>

    <delete id="deleteCleaningScheduleByTaskIds" parameterType="String">
        delete from t_cleaning_schedule where task_id in
        <foreach item="taskId" collection="array" open="(" separator="," close=")">
            #{taskId}
        </foreach>
    </delete>
</mapper>
