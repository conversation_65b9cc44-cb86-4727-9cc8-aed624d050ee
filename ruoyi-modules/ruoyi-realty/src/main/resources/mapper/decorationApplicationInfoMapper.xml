<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ibms.service.realty.web.mapper.DecorationApplicationInfoMapper">

    <resultMap type="DecorationApplicationInfo" id="DecorationApplicationInfoResult">
        <result property="id" column="id"/>
        <result property="roomCode" column="room_code"/>
        <result property="applyBody" column="apply_body"/>
        <result property="applyer" column="applyer"/>
        <result property="applyerName" column="applyer_name"/>
        <result property="phone" column="phone"/>
        <result property="startDate" column="start_date"/>
        <result property="endDate" column="end_date"/>
        <result property="decorationCompany" column="decoration_company"/>
        <result property="companyQualification" column="company_qualification"/>
        <result property="principal" column="principal"/>
        <result property="principalCard" column="principal_card"/>
        <result property="principalPhone" column="principal_phone"/>
        <result property="workNumber" column="work_number"/>
        <result property="workContent" column="work_content"/>
        <result property="remark" column="remark"/>
        <result property="data" column="data"/>
        <result property="accessory" column="accessory"/>
        <result property="acceptanceAppointmentDate" column="acceptance_appointment_date"/>
        <result property="status" column="status"/>
        <result property="createTime" column="create_time"/>

        <result property="area" column="area"/>
        <result property="roomName" column="roomName"/>
        <result property="floorName" column="floorName"/>
        <result property="buildingName" column="buildingName"/>

    </resultMap>

    <sql id="selectDecorationApplicationInfoVo">
        SELECT tdai.id,
        tdai.room_code,
        tdai.apply_body,
        tdai.applyer,
        tdai.applyer_name,
        tdai.phone,
        tdai.start_date,
        tdai.end_date,
        tdai.decoration_company,
        tdai.company_qualification,
        tdai.principal,
        tdai.principal_card,
        tdai.principal_phone,
        tdai.work_number,
        tdai.work_content,
        tdai.remark,
        tdai.data,
        tdai.accessory,
        tdai.acceptance_appointment_date,
        tdai.status,
        tdai.create_time,

        r.name AS roomName
        FROM t_decoration_application_info tdai
        LEFT JOIN t_property_resource_info r ON tdai.room_code = r.id

    </sql>

    <select id="selectDecorationApplicationInfoList" parameterType="DecorationApplicationInfo"
            resultMap="DecorationApplicationInfoResult">
        select tdai.id,
        tdai.room_code,
        tdai.apply_body,
        tdai.applyer,
        tdai.applyer_name,
        tdai.phone,
        tdai.start_date,
        tdai.end_date,
        tdai.decoration_company,
        tdai.company_qualification,
        tdai.principal,
        tdai.principal_card,
        tdai.principal_phone,
        tdai.work_number,
        tdai.work_content,
        tdai.remark,
        tdai.data,
        tdai.accessory,
        tdai.acceptance_appointment_date,
        tdai.status,
        tdai.create_time,

        r.name as roomName,
        f.name as floorName,
        b.name as buildingName
        from t_decoration_application_info tdai
        LEFT JOIN t_property_resource_info r ON tdai.room_code = r.id AND r.type = 2
        LEFT JOIN t_property_resource_info f ON r.parent_id = f.id AND f.type = 1
        LEFT JOIN t_property_resource_info b ON f.parent_id = b.id AND b.type = 0
        <where>
            <if test="roomCode != null">and tdai.room_code = #{roomCode}</if>
            <!--            <if test="roomName != null  and roomName != ''">and tdai.room_name = #{roomName}</if>-->
            <if test="applyer != null ">and applyer = #{applyer}</if>
            <if test="applyerName != null and applyerName != ''">and tdai.applyer_name like
                concat('%',#{applyerName},'%')
            </if>
            <if test="applyBody != null ">and tdai.apply_body = #{applyBody}</if>
            <if test="phone != null  and phone != ''">and tdai.phone = #{phone}</if>
            <if test="startDate != null">and tdai.start_date <![CDATA[<= #{startDate}]]></if>
            <if test="endDate != null ">and tdai.end_date >= #{endDate}</if>
            <if test="decorationCompany != null  and decorationCompany != ''">and tdai.decoration_company =
                #{decorationCompany}
            </if>
            <if test="companyQualification != null">and tdai.company_qualification =
                #{companyQualification}
            </if>
            <if test="principal != null  and principal != ''">and tdai.principal = #{principal}</if>
            <if test="principalCard != null  and principalCard != ''">and tdai.principal_card = #{principalCard}</if>
            <if test="principalPhone != null  and principalPhone != ''">and tdai.principal_phone = #{principalPhone}
            </if>
            <if test="workNumber != null ">and tdai.work_number = #{workNumber}</if>
            <if test="workContent != null ">and tdai.work_content = #{workContent}</if>
            <if test="data != null  and data != ''">and tdai.data = #{data}</if>
            <if test="accessory != null  and accessory != ''">and tdai.accessory = #{accessory}</if>
            <if test="status != null and status != ''">and tdai.status = #{status}</if>
            <if test="acceptanceAppointmentDate != null ">and tdai.acceptance_appointment_date =
                #{acceptanceAppointmentDate}
            </if>
            <if test="statusParams != null ">
                <foreach collection="statusParams" item="statusParam" open="and tdai.status in (" close=")"
                         separator=",">
                    #{statusParam}
                </foreach>
            </if>

        </where>
        order by tdai.create_time desc
    </select>

    <select id="selectDecorationApplicationInfoById" parameterType="Integer"
            resultMap="DecorationApplicationInfoResult">
        SELECT tdai.id,
        tdai.room_code,
        tdai.apply_body,
        tdai.applyer,
        tdai.applyer_name,
        tdai.phone,
        tdai.start_date,
        tdai.end_date,
        tdai.decoration_company,
        tdai.company_qualification,
        tdai.principal,
        tdai.principal_card,
        tdai.principal_phone,
        tdai.work_number,
        tdai.work_content,
        tdai.remark,
        tdai.data,
        tdai.accessory,
        tdai.acceptance_appointment_date,
        tdai.status,
        tdai.create_time,

        r.area,
        r.name AS roomName
        FROM t_decoration_application_info tdai
        LEFT JOIN t_property_resource_info r ON tdai.room_code = r.id
        WHERE tdai.id = #{id}
    </select>

    <select id="selectDecorationApplicationRelation"
            resultType="com.ibms.service.realty.web.controller.decorationApplicationInfo.vo.DecorationApplicationRelationVo">
        SELECT daar.id,
        daar.status,
        dac.name AS acceptName,
        dac.id AS acceptId
        FROM t_decoration_application_accept dac
        INNER JOIN t_decoration_applicate_accept_relation daar ON daar.accept_id = dac.id
        WHERE daar.application_id = #{id}
    </select>

    <select id="selectApplicationAcceptAll"
            resultType="com.ibms.service.realty.web.controller.decorationApplicationInfo.vo.DecorationApplicationRelationVo">
        SELECT name AS acceptName,
        id AS acceptId
        FROM t_decoration_application_accept
        WHERE status = '0'
    </select>

    <select id="selectDecorationApplicationProblem"
            resultType="com.ibms.service.realty.web.controller.decorationApplicationInfo.vo.DecorationApplicationProblemVo">

        SELECT dap.id,
        dap.application_id AS applicationId,
        dap.description,
        dap.submiter AS submiter,
        dap.submiter_name AS submiterName,
        dap.create_time AS createTime
        FROM t_decoration_application_problem dap
        LEFT JOIN t_decoration_application_info dai ON dap.application_id = dai.id
        WHERE dai.id = #{applicationId}
    </select>

    <insert id="insertDecorationApplicationInfo" parameterType="DecorationApplicationInfo" useGeneratedKeys="true"
            keyProperty="id">
        insert into t_decoration_application_info
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="roomCode != null">room_code,</if>
            <if test="applyer != null">applyer,</if>
            <if test="applyerName != null">applyer_name,</if>
            <if test="applyBody != null">apply_body,</if>
            <if test="phone != null and phone != ''">phone,</if>
            <if test="startDate != null">start_date,</if>
            <if test="endDate != null">end_date,</if>
            <if test="decorationCompany != null">decoration_company,</if>
            <if test="companyQualification != null">company_qualification,</if>
            <if test="principal != null">principal,</if>
            <if test="principalCard != null">principal_card,</if>
            <if test="principalPhone != null">principal_phone,</if>
            <if test="workNumber != null">work_number,</if>
            <if test="workContent != null">work_content,</if>
            <if test="remark != null">remark,</if>
            <if test="data != null">data,</if>
            <if test="accessory != null">accessory,</if>
            <if test="acceptanceAppointmentDate != null">acceptance_appointment_date,</if>
            <if test="status != null">status,</if>
            <if test="createTime != null">create_time,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="roomCode != null">#{roomCode},</if>
            <if test="applyer != null">#{applyer},</if>
            <if test="applyerName != null">#{applyerName},</if>
            <if test="applyBody != null">#{applyBody},</if>
            <if test="phone != null and phone != ''">#{phone},</if>
            <if test="startDate != null">#{startDate},</if>
            <if test="endDate != null">#{endDate},</if>
            <if test="decorationCompany != null">#{decorationCompany},</if>
            <if test="companyQualification != null">#{companyQualification},</if>
            <if test="principal != null">#{principal},</if>
            <if test="principalCard != null">#{principalCard},</if>
            <if test="principalPhone != null">#{principalPhone},</if>
            <if test="workNumber != null">#{workNumber},</if>
            <if test="workContent != null">#{workContent},</if>
            <if test="remark != null">#{remark},</if>
            <if test="data != null">#{data},</if>
            <if test="accessory != null">#{accessory},</if>
            <if test="acceptanceAppointmentDate != null">#{acceptanceAppointmentDate},</if>
            <if test="status != null">#{status},</if>
            <if test="createTime != null">#{createTime},</if>
        </trim>
    </insert>
    <insert id="insertDecorationApplicationRelation">
        insert into t_decoration_application_problem
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="applicationId != null and applicationId != ''">application_id,</if>
            <if test="status != null">status,</if>
            <if test="description != null">description,</if>
            <if test="correctiveAction != null">corrective_action,</if>
            <if test="correctiveTime != null">corrective_time,</if>
            <if test="createTime != null">create_time,</if>
            <if test="solvingTime != null">solving_time,</if>
            <if test="img != null">img,</if>
            <if test="submiter != null">submiter,</if>
            <if test="submiterName != null">submiter_name,</if>
            <if test="solvingDescription != null">solving_description,</if>
            <if test="solvingImg != null">solving_img,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="applicationId != null">#{applicationId},</if>
            <if test="status != null">#{status},</if>
            <if test="description != null">#{description},</if>
            <if test="correctiveAction != null">#{correctiveAction},</if>
            <if test="correctiveTime != null">#{correctiveTime},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="solvingTime != null">#{solvingTime},</if>
            <if test="img != null">#{img},</if>
            <if test="submiter != null">#{submiter},</if>
            <if test="submiterName != null">#{submiterName},</if>
            <if test="solvingDescription != null">#{solvingDescription},</if>
            <if test="solvingImg != null">#{solvingImg},</if>
        </trim>
    </insert>

    <insert id="insertBatchRelations">
        insert into t_decoration_applicate_accept_relation(application_id, accept_id,status) VALUES
        <foreach collection="list" item="item" index="index" separator=",">
            (#{item.applicationId}, #{item.acceptId},#{item.status})
        </foreach>
    </insert>

    <update id="updateDecorationApplicationInfo" parameterType="DecorationApplicationInfo">
        update t_decoration_application_info
        <trim prefix="SET" suffixOverrides=",">
            <if test="roomCode != null">room_code = #{roomCode},</if>
            <if test="applyer != null">applyer = #{applyer},</if>
            <if test="applyerName != null">applyer_name = #{applyerName},</if>
            <if test="applyBody != null">apply_body = #{applyBody},</if>
            <if test="phone != null and phone != ''">phone = #{phone},</if>
            <if test="startDate != null">start_date = #{startDate},</if>
            <if test="endDate != null">end_date = #{endDate},</if>
            <if test="decorationCompany != null">decoration_company = #{decorationCompany},</if>
            <if test="companyQualification != null">company_qualification = #{companyQualification},</if>
            <if test="principal != null">principal = #{principal},</if>
            <if test="principalCard != null">principal_card = #{principalCard},</if>
            <if test="principalPhone != null">principal_phone = #{principalPhone},</if>
            <if test="workNumber != null">work_number = #{workNumber},</if>
            <if test="workContent != null">work_content = #{workContent},</if>
            <if test="remark != null">remark = #{remark},</if>
            <if test="data != null">data = #{data},</if>
            <if test="accessory != null">accessory = #{accessory},</if>
            <if test="acceptanceAppointmentDate != null">acceptance_appointment_date = #{acceptanceAppointmentDate},
            </if>
            <if test="status != null">status = #{status},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
        </trim>
        where id = #{id}
    </update>

    <update id="updateDecorationApplicationRelation">
        UPDATE t_decoration_applicate_accept_relation
        SET status = #{status}
        WHERE id = #{id}
    </update>

    <delete id="deleteDecorationApplicationInfoById" parameterType="Integer">
        DELETE
        FROM t_decoration_application_info
        WHERE id = #{id}
    </delete>

    <delete id="deleteDecorationApplicationInfoByIds" parameterType="String">
        delete from t_decoration_application_info where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>
