<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ibms.service.realty.web.mapper.ShiftInfoMapper">

    <resultMap type="ShiftInfo" id="ShiftInfoResult">
        <result property="id" column="id"/>
        <result property="shiftName" column="shift_name"/>
        <result property="startTime" column="start_time"/>
        <result property="startCheckinOrigin" column="start_checkin_origin"/>
        <result property="startCheckinTerminus" column="start_checkin_terminus"/>
        <result property="startCheckinEnabled" column="start_checkin_enabled"/>
        <result property="startLateMinutes" column="start_late_minutes"/>
        <result property="endTime" column="end_time"/>
        <result property="endCheckinOrigin" column="end_checkin_origin"/>
        <result property="endCheckinTerminus" column="end_checkin_terminus"/>
        <result property="endCheckinEnabled" column="end_checkin_enabled"/>
        <result property="endLateMinutes" column="end_late_minutes"/>
        <result property="checkinLocationRange" column="checkin_location_range"/>
        <result property="createBy" column="create_by"/>
        <result property="createByName" column="create_by_name"/>
        <result property="updatedTime" column="updated_time"/>


        <collection property="shiftLocationList" ofType="com.ibms.service.realty.web.domain.ShiftLocation">
            <result property="id" column="shift_location_id"/>
            <result property="shiftId" column="shift_id"/>
            <result property="checkinLocation" column="checkin_location"/>
            <result property="location" column="location"/>
            <result property="longitude" column="longitude"/>
            <result property="latitude" column="latitude"/>
        </collection>
    </resultMap>

    <sql id="selectShiftInfoVo">
        SELECT id,
        shift_name,
        start_time,
        start_checkin_origin,
        start_checkin_terminus,
        start_checkin_enabled,
        start_late_minutes,
        end_time,
        end_checkin_origin,
        end_checkin_terminus,
        end_checkin_enabled,
        end_late_minutes,
        checkin_location_range,
        create_by,
        create_by_name,
        updated_time
        FROM t_shift_info
    </sql>

    <select id="selectShiftInfoList" parameterType="ShiftInfo" resultMap="ShiftInfoResult">
        <include refid="selectShiftInfoVo"/>
        <where>
            <if test="shiftName != null  and shiftName != ''">and shift_name like concat('%', #{shiftName}, '%')</if>
            <if test="startTime != null  and startTime != ''">and start_time = #{startTime}</if>
            <if test="startCheckinEnabled != null ">and start_checkin_enabled = #{startCheckinEnabled}</if>
            <if test="startLateMinutes != null ">and start_late_minutes = #{startLateMinutes}</if>
            <if test="endTime != null  and endTime != ''">and end_time = #{endTime}</if>
            <if test="endCheckinEnabled != null ">and end_checkin_enabled = #{endCheckinEnabled}</if>
            <if test="endLateMinutes != null ">and end_late_minutes = #{endLateMinutes}</if>
            <if test="checkinLocationRange != null ">and checkin_location_range = #{checkinLocationRange}</if>
            <if test="updatedTime != null ">and updated_time = #{updatedTime}</if>
        </where>
        order by updated_time desc
    </select>

    <select id="selectShiftInfoById" parameterType="Integer" resultMap="ShiftInfoResult">
        SELECT tsi.id,
        tsi.shift_name,
        tsi.start_time,
        tsi.start_checkin_origin,
        tsi.start_checkin_terminus,
        tsi.start_checkin_enabled,
        tsi.start_late_minutes,
        tsi.end_time,
        tsi.end_checkin_origin,
        tsi.end_checkin_terminus,
        tsi.end_checkin_enabled,
        tsi.end_late_minutes,
        tsi.checkin_location_range,
        tsi.create_by,
        tsi.create_by_name,
        tsi.updated_time,

        tsl.id AS shift_location_id,
        tsl.checkin_location,
        tsl.location,
        tsl.longitude,
        tsl.latitude
        FROM t_shift_info tsi
        LEFT JOIN t_shift_location tsl ON tsi.id = tsl.shift_id
        WHERE tsi.id = #{id}
    </select>

    <insert id="insertShiftInfo" parameterType="ShiftInfo" useGeneratedKeys="true" keyProperty="id">
        insert into t_shift_info
        (
        <trim prefix="" suffix="" suffixOverrides=",">
            <if test="shiftName != null">shift_name,</if>
            <if test="startTime != null">start_time,</if>
            <if test="startCheckinOrigin != null">start_checkin_origin,</if>
            <if test="startCheckinTerminus != null">start_checkin_terminus,</if>
            <if test="startCheckinEnabled != null">start_checkin_enabled,</if>
            <if test="startLateMinutes != null">start_late_minutes,</if>
            <if test="endTime != null">end_time,</if>
            <if test="endCheckinOrigin != null">end_checkin_origin,</if>
            <if test="endCheckinTerminus != null">end_checkin_terminus,</if>
            <if test="endCheckinEnabled != null">end_checkin_enabled,</if>
            <if test="endLateMinutes != null">end_late_minutes,</if>
            <if test="checkinLocationRange != null">checkin_location_range,</if>
            <if test="createBy != null">create_by,</if>
            <if test="updatedTime != null">updated_time,</if>
            <if test="createByName != null">create_by_name,</if>
        </trim>
        )
        values
        (
        <trim prefix="" suffix="" suffixOverrides=",">
            <if test="shiftName != null">#{shiftName},</if>
            <if test="startTime != null">#{startTime},</if>
            <if test="startCheckinOrigin != null">#{startCheckinOrigin},</if>
            <if test="startCheckinTerminus != null">#{startCheckinTerminus},</if>
            <if test="startCheckinEnabled != null">#{startCheckinEnabled},</if>
            <if test="startLateMinutes != null">#{startLateMinutes},</if>
            <if test="endTime != null">#{endTime},</if>
            <if test="endCheckinOrigin != null">#{endCheckinOrigin},</if>
            <if test="endCheckinTerminus != null">#{endCheckinTerminus},</if>
            <if test="endCheckinEnabled != null">#{endCheckinEnabled},</if>
            <if test="endLateMinutes != null">#{endLateMinutes},</if>
            <if test="checkinLocationRange != null">#{checkinLocationRange},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="updatedTime != null">#{updatedTime},</if>
            <if test="createByName != null">#{createByName},</if>
        </trim>
        )
    </insert>

    <update id="updateShiftInfo" parameterType="ShiftInfo">
        update t_shift_info
        set
        <trim prefix="" suffix="" suffixOverrides=",">
            <if test="shiftName != null">shift_name = #{shiftName},</if>
            <if test="startTime != null">start_time = #{startTime},</if>
            <if test="endTime != null">end_time = #{endTime},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="updatedTime != null">updated_time = #{updatedTime},</if>
            <if test="createByName != null">create_by_name = #{createByName},</if>
            start_checkin_origin = #{startCheckinOrigin},
            start_checkin_terminus = #{startCheckinTerminus},
            start_checkin_enabled = #{startCheckinEnabled},
            start_late_minutes = #{startLateMinutes},
            end_checkin_origin = #{endCheckinOrigin},
            end_checkin_terminus = #{endCheckinTerminus},
            end_checkin_enabled = #{endCheckinEnabled},
            end_late_minutes = #{endLateMinutes},
            checkin_location_range = #{checkinLocationRange},
        </trim>
        where id = #{id}
    </update>


    <delete id="deleteShiftInfoById" parameterType="Integer">
        DELETE
        FROM t_shift_info
        WHERE id = #{id}
    </delete>

    <delete id="deleteShiftInfoByIds" parameterType="String">
        delete from t_shift_info where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <!--    CREATE TABLE `t_shift_location` (-->
    <!--    `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键id',-->
    <!--    `shift_id` int(11) DEFAULT NULL COMMENT '关联班次id，t_shift_info',-->
    <!--    `checkin_location` varchar(255) DEFAULT NULL COMMENT '考勤地点名称',-->
    <!--    `location` varchar(255) DEFAULT NULL COMMENT '定位地址',-->
    <!--    `longitude` varchar(20) DEFAULT NULL COMMENT '经度',-->
    <!--    `latitude` varchar(20) DEFAULT NULL COMMENT '纬度',-->
    <!--    PRIMARY KEY (`id`)-->
    <!--    ) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='班次定位表';-->
    <delete id="deleteShiftLocationByShiftId">
        DELETE
        FROM t_shift_location
        WHERE shift_id = #{shiftId}
    </delete>

    <insert id="insertShiftLocation" parameterType="ShiftLocation" useGeneratedKeys="true" keyProperty="id">
        insert into t_shift_location
        (
        <trim prefix="" suffix="" suffixOverrides=",">
            <if test="shiftId != null">shift_id,</if>
            <if test="checkinLocation != null">checkin_location,</if>
            <if test="location != null">location,</if>
            <if test="longitude != null">longitude,</if>
            <if test="latitude != null">latitude,</if>
        </trim>
        )
        values
        (
        <trim prefix="" suffix="" suffixOverrides=",">
            <if test="shiftId != null">#{shiftId},</if>
            <if test="checkinLocation != null">#{checkinLocation},</if>
            <if test="location != null">#{location},</if>
            <if test="longitude != null">#{longitude},</if>
            <if test="latitude != null">#{latitude},</if>
        </trim>
        )
    </insert>
</mapper>
