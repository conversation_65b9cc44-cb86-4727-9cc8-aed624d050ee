<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ibms.service.realty.web.mapper.CleaningTaskOperationRecordMapper">

    <resultMap type="CleaningTaskOperationRecord" id="CleaningTaskOperationRecordResult">
        <result property="id" column="id"/>
        <result property="taskId" column="task_id"/>
        <result property="content" column="content"/>
        <result property="creatTime" column="creat_time"/>
    </resultMap>

    <sql id="selectCleaningTaskOperationRecordVo">
        select id, task_id, content, creat_time from t_cleaning_task_operation_record
    </sql>

    <select id="selectCleaningTaskOperationRecordList" parameterType="CleaningTaskOperationRecord"
            resultMap="CleaningTaskOperationRecordResult">
        <include refid="selectCleaningTaskOperationRecordVo"/>
        <where>
            <if test="taskId != null ">and task_id = #{taskId}</if>
            <if test="content != null  and content != ''">and content like concat('%', #{content}, '%')</if>
            <if test="creatTime != null ">and creat_time = #{creatTime}</if>
        </where>
        order by creat_time desc
    </select>

    <select id="selectCleaningTaskOperationRecordById" parameterType="Integer"
            resultMap="CleaningTaskOperationRecordResult">
        <include refid="selectCleaningTaskOperationRecordVo"/>
        where id = #{id}
    </select>

    <insert id="insertCleaningTaskOperationRecord" parameterType="CleaningTaskOperationRecord" useGeneratedKeys="true"
            keyProperty="id">
        insert into t_cleaning_task_operation_record
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="taskId != null">task_id,</if>
            <if test="content != null and content != ''">content,</if>
            <if test="creatTime != null">creat_time,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="taskId != null">#{taskId},</if>
            <if test="content != null and content != ''">#{content},</if>
            <if test="creatTime != null">#{creatTime},</if>
        </trim>
    </insert>

    <update id="updateCleaningTaskOperationRecord" parameterType="CleaningTaskOperationRecord">
        update t_cleaning_task_operation_record
        <trim prefix="SET" suffixOverrides=",">
            <if test="taskId != null">task_id = #{taskId},</if>
            <if test="content != null and content != ''">content = #{content},</if>
            <if test="creatTime != null">creat_time = #{creatTime},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteCleaningTaskOperationRecordById" parameterType="Integer">
        delete from t_cleaning_task_operation_record where id = #{id}
    </delete>

    <delete id="deleteCleaningTaskOperationRecordByIds" parameterType="String">
        delete from t_cleaning_task_operation_record where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>
