<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ibms.service.realty.web.mapper.OfficeCategoryMapper">

    <resultMap type="OfficeCategory" id="OfficeCategoryResult">
        <result property="id" column="id"/>
        <result property="type" column="type"/>
        <result property="name" column="name"/>
        <result property="remark" column="remark"/>
    </resultMap>

    <sql id="selectOfficeCategoryVo">
        SELECT id, type, name, remark
        FROM t_office_category
    </sql>

    <select id="selectOfficeCategoryList" parameterType="OfficeCategory"
            resultMap="OfficeCategoryResult">
        <include refid="selectOfficeCategoryVo"/>
        <where>
            <if test="name != null  and name != ''">and name like concat('%', #{name}, '%')</if>
            <if test="type != null">and type = #{type}</if>
            <if test="contentParam != null  and contentParam != ''">and content like concat('%', #{contentParam}, '%')
            </if>
        </where>
        order by id desc
    </select>

    <select id="selectOfficeCategoryById" parameterType="Integer" resultMap="OfficeCategoryResult">
        <include refid="selectOfficeCategoryVo"/>
        where id = #{id}
    </select>

    <insert id="insertOfficeCategory" parameterType="OfficeCategory" useGeneratedKeys="true"
            keyProperty="id">
        insert into t_office_category
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="name != null">name,</if>
            <if test="type != null">type ,</if>
            <if test="remark != null">remark,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="name != null">#{name},</if>
            <if test="type != null">#{type},</if>
            <if test="remark != null">#{remark},</if>
        </trim>
    </insert>

    <update id="updateOfficeCategory" parameterType="OfficeCategory">
        update t_office_category
        <trim prefix="SET" suffixOverrides=",">
            <if test="name != null">name = #{name},</if>
            <if test="type != null">type = #{type},</if>
            <if test="remark != null">remark = #{remark},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteOfficeCategoryById" parameterType="Integer">
        DELETE
        FROM t_office_category
        WHERE id = #{id}
    </delete>

    <delete id="deleteOfficeCategoryByIds" parameterType="String">
        delete from t_office_category where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>