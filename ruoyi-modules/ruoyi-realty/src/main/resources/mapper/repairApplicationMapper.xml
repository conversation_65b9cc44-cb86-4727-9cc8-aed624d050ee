<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ibms.service.realty.web.mapper.RepairApplicationMapper">

    <resultMap type="RepairApplication" id="RepairApplicationResult">
        <result property="id" column="id"/>
        <result property="repairType" column="repair_type"/>
        <result property="title" column="title"/>
        <result property="detail" column="detail"/>
        <result property="locationType" column="location_type"/>
        <result property="location" column="location"/>
        <result property="applicant" column="applicant"/>
        <result property="contactPhone" column="contact_phone"/>
        <result property="appointmentTime" column="appointment_time"/>
        <result property="repairPrice" column="repair_price"/>
        <result property="repairPerson" column="repair_person"/>
        <result property="feedbackMethod" column="feedback_method"/>
        <result property="feedbackContent" column="feedback_content"/>
        <result property="feedbackResult" column="feedback_result"/>
        <result property="img" column="img"/>
        <result property="status" column="status"/>
    </resultMap>

    <sql id="selectRepairApplicationVo">
        select id, repair_type, title, detail, location_type, location, applicant, contact_phone, appointment_time,
        repair_price, repair_person, feedback_method, feedback_content, feedback_result, img, status from
        t_repair_application
    </sql>

    <select id="selectRepairApplicationList" parameterType="RepairApplication" resultMap="RepairApplicationResult">
        <include refid="selectRepairApplicationVo"/>
        <where>
            <if test="repairType != null ">and repair_type = #{repairType}</if>
            <if test="title != null  and title != ''">and title = #{title}</if>
            <if test="detail != null  and detail != ''">and detail = #{detail}</if>
            <if test="locationType != null ">and location_type = #{locationType}</if>
            <if test="location != null  and location != ''">and location = #{location}</if>
            <if test="applicant != null ">and applicant = #{applicant}</if>
            <if test="contactPhone != null  and contactPhone != ''">and contact_phone = #{contactPhone}</if>
            <if test="appointmentTime != null ">and appointment_time = #{appointmentTime}</if>
            <if test="repairPrice != null ">and repair_price = #{repairPrice}</if>
            <if test="repairPerson != null ">and repair_person = #{repairPerson}</if>
            <if test="feedbackMethod != null ">and feedback_method = #{feedbackMethod}</if>
            <if test="feedbackContent != null  and feedbackContent != ''">and feedback_content = #{feedbackContent}</if>
            <if test="feedbackResult != null  and feedbackResult != ''">and feedback_result = #{feedbackResult}</if>
            <if test="img != null  and img != ''">and img = #{img}</if>
            <if test="status != null ">and status = #{status}</if>
        </where>
        order by id desc
    </select>

    <select id="selectRepairApplicationById" parameterType="Long" resultMap="RepairApplicationResult">
        <include refid="selectRepairApplicationVo"/>
        where id = #{id}
    </select>

    <insert id="insertRepairApplication" parameterType="RepairApplication" useGeneratedKeys="true" keyProperty="id">
        insert into t_repair_application
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="repairType != null">repair_type,</if>
            <if test="title != null and title != ''">title,</if>
            <if test="detail != null">detail,</if>
            <if test="locationType != null">location_type,</if>
            <if test="location != null and location != ''">location,</if>
            <if test="applicant != null">applicant,</if>
            <if test="contactPhone != null and contactPhone != ''">contact_phone,</if>
            <if test="appointmentTime != null">appointment_time,</if>
            <if test="repairPrice != null">repair_price,</if>
            <if test="repairPerson != null">repair_person,</if>
            <if test="feedbackMethod != null">feedback_method,</if>
            <if test="feedbackContent != null">feedback_content,</if>
            <if test="feedbackResult != null">feedback_result,</if>
            <if test="img != null">img,</if>
            <if test="status != null">status,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="repairType != null">#{repairType},</if>
            <if test="title != null and title != ''">#{title},</if>
            <if test="detail != null">#{detail},</if>
            <if test="locationType != null">#{locationType},</if>
            <if test="location != null and location != ''">#{location},</if>
            <if test="applicant != null">#{applicant},</if>
            <if test="contactPhone != null and contactPhone != ''">#{contactPhone},</if>
            <if test="appointmentTime != null">#{appointmentTime},</if>
            <if test="repairPrice != null">#{repairPrice},</if>
            <if test="repairPerson != null">#{repairPerson},</if>
            <if test="feedbackMethod != null">#{feedbackMethod},</if>
            <if test="feedbackContent != null">#{feedbackContent},</if>
            <if test="feedbackResult != null">#{feedbackResult},</if>
            <if test="img != null">#{img},</if>
            <if test="status != null">#{status},</if>
        </trim>
    </insert>

    <update id="updateRepairApplication" parameterType="RepairApplication">
        update t_repair_application
        <trim prefix="SET" suffixOverrides=",">
            <if test="repairType != null">repair_type = #{repairType},</if>
            <if test="title != null and title != ''">title = #{title},</if>
            <if test="detail != null">detail = #{detail},</if>
            <if test="locationType != null">location_type = #{locationType},</if>
            <if test="location != null and location != ''">location = #{location},</if>
            <if test="applicant != null">applicant = #{applicant},</if>
            <if test="contactPhone != null and contactPhone != ''">contact_phone = #{contactPhone},</if>
            <if test="appointmentTime != null">appointment_time = #{appointmentTime},</if>
            <if test="repairPrice != null">repair_price = #{repairPrice},</if>
            <if test="repairPerson != null">repair_person = #{repairPerson},</if>
            <if test="feedbackMethod != null">feedback_method = #{feedbackMethod},</if>
            <if test="feedbackContent != null">feedback_content = #{feedbackContent},</if>
            <if test="feedbackResult != null">feedback_result = #{feedbackResult},</if>
            <if test="img != null">img = #{img},</if>
            <if test="status != null">status = #{status},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteRepairApplicationById" parameterType="Long">
        delete from t_repair_application where id = #{id}
    </delete>

    <delete id="deleteRepairApplicationByIds" parameterType="String">
        delete from t_repair_application where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>
