<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ibms.service.realty.web.mapper.UserComplaintMapper">

    <resultMap type="UserComplaint" id="UserComplaintResult">
        <result property="id" column="id"/>
        <result property="complaintTitle" column="complaint_title"/>
        <result property="contactInfo" column="contact_info"/>
        <result property="imageUrls" column="image_urls"/>
        <result property="complaintDescription" column="complaint_description"/>
        <result property="submitterId" column="submitter_id"/>
        <result property="createTime" column="create_time"/>
        <result property="replyContent" column="reply_content"/>
        <result property="replyTime" column="reply_time"/>
        <result property="replierId" column="replier_id"/>
    </resultMap>

    <sql id="selectUserComplaintVo">
        select id,
        complaint_title,
        contact_info,
        image_urls,
        complaint_description,
        submitter_id,
        create_time,
        reply_content,
        reply_time,
        replier_id
        from t_user_complaint
    </sql>

    <select id="selectUserComplaintList" parameterType="UserComplaint" resultMap="UserComplaintResult">
        <include refid="selectUserComplaintVo"/>
        <where>
            <if test="complaintTitle != null  and complaintTitle != ''">and complaint_title = #{complaintTitle}</if>
            <if test="contactInfo != null  and contactInfo != ''">and contact_info = #{contactInfo}</if>
            <if test="imageUrls != null  and imageUrls != ''">and image_urls = #{imageUrls}</if>
            <if test="complaintDescription != null  and complaintDescription != ''">and complaint_description =
                #{complaintDescription}
            </if>
            <if test="submitterId != null ">and submitter_id = #{submitterId}</if>
            <if test="replyContent != null  and replyContent != ''">and reply_content = #{replyContent}</if>
            <if test="replyTime != null ">and reply_time = #{replyTime}</if>
            <if test="replierId != null ">and replier_id = #{replierId}</if>
        </where>
    </select>

    <select id="selectUserComplaintById" parameterType="Integer" resultMap="UserComplaintResult">
        <include refid="selectUserComplaintVo"/>
        where id = #{id}
    </select>

    <insert id="insertUserComplaint" parameterType="UserComplaint" useGeneratedKeys="true" keyProperty="id">
        insert into t_user_complaint
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="complaintTitle != null and complaintTitle != ''">complaint_title,</if>
            <if test="contactInfo != null and contactInfo != ''">contact_info,</if>
            <if test="imageUrls != null">image_urls,</if>
            <if test="complaintDescription != null">complaint_description,</if>
            <if test="submitterId != null">submitter_id,</if>
            <if test="createTime != null">create_time,</if>
            <if test="replyContent != null">reply_content,</if>
            <if test="replyTime != null">reply_time,</if>
            <if test="replierId != null">replier_id,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="complaintTitle != null and complaintTitle != ''">#{complaintTitle},</if>
            <if test="contactInfo != null and contactInfo != ''">#{contactInfo},</if>
            <if test="imageUrls != null">#{imageUrls},</if>
            <if test="complaintDescription != null">#{complaintDescription},</if>
            <if test="submitterId != null">#{submitterId},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="replyContent != null">#{replyContent},</if>
            <if test="replyTime != null">#{replyTime},</if>
            <if test="replierId != null">#{replierId},</if>
        </trim>
    </insert>

    <update id="updateUserComplaint" parameterType="UserComplaint">
        update t_user_complaint
        <trim prefix="SET" suffixOverrides=",">
            <if test="complaintTitle != null and complaintTitle != ''">complaint_title = #{complaintTitle},</if>
            <if test="contactInfo != null and contactInfo != ''">contact_info = #{contactInfo},</if>
            <if test="imageUrls != null">image_urls = #{imageUrls},</if>
            <if test="complaintDescription != null">complaint_description = #{complaintDescription},</if>
            <if test="submitterId != null">submitter_id = #{submitterId},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="replyContent != null">reply_content = #{replyContent},</if>
            <if test="replyTime != null">reply_time = #{replyTime},</if>
            <if test="replierId != null">replier_id = #{replierId},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteUserComplaintById" parameterType="Integer">
        delete
        from t_user_complaint
        where id = #{id}
    </delete>

    <delete id="deleteUserComplaintByIds" parameterType="String">
        delete from t_user_complaint where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>
