<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ibms.service.realty.web.mapper.CleaningProblemRecordMapper">

    <resultMap type="CleaningProblemRecord" id="CleaningProblemRecordResult">
        <result property="id" column="id"/>
        <result property="relationTaskId" column="relation_task_id"/>
        <result property="status" column="status"/>
        <result property="description" column="description"/>
        <result property="solution" column="solution"/>
        <result property="creatTime" column="creat_time"/>
        <result property="solvingTime" column="solving_time"/>
        <result property="submiter" column="submiter"/>
        <result property="submiterName" column="submiter_name"/>
        <result property="principal" column="principal"/>
        <result property="img" column="img"/>
        <result property="solvingDescription" column="solving_description"/>
        <result property="solvingImg" column="solving_img"/>
    </resultMap>

    <sql id="selectCleaningProblemRecordVo">
        select id,
        relation_task_id,
        status,
        description,
        solution,
        creat_time,
        solving_time,
        submiter,
        submiter_name,
        principal,
        img,
        solving_description,
        solving_img
        from t_cleaning_problem_record
    </sql>

    <select id="selectCleaningProblemRecordList" parameterType="CleaningProblemRecord"
            resultMap="CleaningProblemRecordResult">
        <include refid="selectCleaningProblemRecordVo"/>
        <where>
            <if test="relationTaskId != null ">and relation_task_id = #{relationTaskId}</if>
            <if test="status != null ">and status = #{status}</if>
            <if test="description != null  and description != ''">and description like concat('%', #{description},
                '%')
            </if>
            <if test="solution != null  and solution != ''">and solution like concat('%', #{solution}, '%')</if>
            <if test="creatTime != null ">and creat_time = #{creatTime}</if>
            <if test="solvingTime != null ">and solving_time = #{solvingTime}</if>
            <if test="submiter != null ">and submiter = #{submiter}</if>
            <if test="submiterName != null ">and submiter_name = #{submiterName}</if>
            <if test="principal != null ">and principal = #{principal}</if>
            <if test="img != null  and img != ''">and img = #{img}</if>
            <if test="solvingDescription != null ">and solving_description = #{solvingDescription}</if>
            <if test="solvingImg != null  and solvingImg != ''">and solving_img = #{solvingImg}</if>
        </where>
        order by creat_time desc
    </select>

    <select id="selectCleaningProblemRecordById" parameterType="Integer" resultMap="CleaningProblemRecordResult">
        <include refid="selectCleaningProblemRecordVo"/>
        where id = #{id}
    </select>

    <insert id="insertCleaningProblemRecord" parameterType="CleaningProblemRecord" useGeneratedKeys="true"
            keyProperty="id">
        insert into t_cleaning_problem_record
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="relationTaskId != null">relation_task_id,</if>
            <if test="status != null">status,</if>
            <if test="description != null">description,</if>
            <if test="solution != null">solution,</if>
            <if test="creatTime != null">creat_time,</if>
            <if test="solvingTime != null">solving_time,</if>
            <if test="submiter != null">submiter,</if>
            <if test="submiterName != null">submiter_name,</if>
            <if test="principal != null">principal,</if>
            <if test="img != null">img,</if>
            <if test="solvingDescription != null">solving_description,</if>
            <if test="solvingImg != null">solving_img,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="relationTaskId != null">#{relationTaskId},</if>
            <if test="status != null">#{status},</if>
            <if test="description != null">#{description},</if>
            <if test="solution != null">#{solution},</if>
            <if test="creatTime != null">#{creatTime},</if>
            <if test="solvingTime != null">#{solvingTime},</if>
            <if test="submiter != null">#{submiter},</if>
            <if test="submiterName != null">#{submiterName},</if>
            <if test="principal != null">#{principal},</if>
            <if test="img != null">#{img},</if>
            <if test="solvingDescription != null">#{solvingDescription},</if>
            <if test="solvingImg != null">#{solvingImg},</if>
        </trim>
    </insert>

    <update id="updateCleaningProblemRecord" parameterType="CleaningProblemRecord">
        update t_cleaning_problem_record
        <trim prefix="SET" suffixOverrides=",">
            <if test="relationTaskId != null">relation_task_id = #{relationTaskId},</if>
            <if test="status != null">status = #{status},</if>
            <if test="description != null">description = #{description},</if>
            <if test="solution != null">solution = #{solution},</if>
            <if test="creatTime != null">creat_time = #{creatTime},</if>
            <if test="solvingTime != null">solving_time = #{solvingTime},</if>
            <if test="submiter != null">submiter = #{submiter},</if>
            <if test="submiterName != null">submiter_name = #{submiterName},</if>
            <if test="principal != null">principal = #{principal},</if>
            <if test="img != null">img = #{img},</if>
            <if test="solvingDescription != null">solving_description = #{solvingDescription},</if>
            <if test="solvingImg != null">solving_img = #{solvingImg},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteCleaningProblemRecordById" parameterType="Integer">
        delete
        from t_cleaning_problem_record
        where id = #{id}
    </delete>

    <delete id="deleteCleaningProblemRecordByIds" parameterType="String">
        delete from t_cleaning_problem_record where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>
