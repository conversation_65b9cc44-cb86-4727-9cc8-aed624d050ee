<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ibms.service.realty.web.mapper.OfficeDocumentMapper">

    <resultMap type="OfficeDocument" id="OfficeDocumentResult">
        <result property="id" column="id"/>
        <result property="documentNumber" column="document_number"/>
        <result property="documentName" column="document_name"/>
        <result property="documentTypeId" column="document_type_id"/>
        <result property="archiveTime" column="archive_time"/>
        <result property="effectiveDate" column="effective_date"/>
        <result property="expiryDate" column="expiry_date"/>
        <result property="version" column="version"/>
        <result property="remark" column="remark"/>
        <result property="creatorId" column="creator_id"/>
        <result property="creator" column="creator"/>
        <result property="reviewerId" column="reviewer_id"/>
        <result property="reviewer" column="reviewer"/>
        <result property="reviewerTime" column="reviewer_time"/>
        <result property="accessoryUrl" column="accessory_url"/>
        <result property="status" column="status"/>

        <result property="documentTypeName" column="document_type_name"/>

    </resultMap>

    <sql id="selectOfficeDocumentVo">
        SELECT id,
        document_number,
        document_name,
        document_type_id,
        archive_time,
        effective_date,
        expiry_date,
        version,
        remark,
        creator_id,
        creator,
        reviewer_id,
        reviewer,
        reviewer_time,
        accessory_url,
        status
        FROM t_office_document
    </sql>

    <select id="selectOfficeDocumentList" parameterType="OfficeDocument" resultMap="OfficeDocumentResult">
        SELECT tod.id,
        tod.document_number,
        tod.document_name,
        tod.document_type_id,
        tod.archive_time,
        tod.effective_date,
        tod.expiry_date,
        tod.version,
        tod.remark,
        tod.creator_id,
        tod.creator,
        tod.reviewer_id,
        tod.reviewer,
        tod.reviewer_time,
        tod.accessory_url,
        tod.status,

        toc.name AS document_type_name
        FROM t_office_document tod
        LEFT JOIN t_office_category toc ON toc.id = tod.document_type_id
        <where>
            <if test="documentNumber != null  and documentNumber != ''">and tod.document_number = #{documentNumber}</if>
            <if test="documentName != null  and documentName != ''">and tod.document_name like concat('%',
                #{documentName},
                '%')
            </if>
            <if test="documentTypeId != null ">and tod.document_type_id = #{documentTypeId}</if>
            <if test="archiveTime != null ">and tod.archive_time = #{archiveTime}</if>
            <if test="archiveTimeStart != null ">and DATE_FORMAT(tod.archive_time,'%y-%m-%d') >=
                DATE_FORMAT(#{archiveTimeStart},'%y-%m-%d')
            </if>
            <if test="archiveTimeEnd != null ">and DATE_FORMAT(tod.archive_time,'%y-%m-%d')
                <![CDATA[<= DATE_FORMAT(#{archiveTimeEnd},'%y-%m-%d')]]></if>
            <if test="effectiveDate != null ">and tod.effective_date = #{effectiveDate}</if>
            <if test="expiryDate != null ">and tod.expiry_date = #{expiryDate}</if>
            <if test="version != null  and version != ''">and tod.version = #{version}</if>
            <if test="creatorId != null ">and tod.creator_id = #{creatorId}</if>
            <if test="creator != null  and creator != ''">and tod.creator = #{creator}</if>
            <if test="reviewerId != null ">and tod.reviewer_id = #{reviewerId}</if>
            <if test="reviewer != null  and reviewer != ''">and tod.reviewer = #{reviewer}</if>
            <if test="status != null  and status != ''">and tod.status = #{status}</if>
        </where>
        order by tod.id desc
    </select>

    <select id="selectOfficeDocumentById" parameterType="Integer" resultMap="OfficeDocumentResult">
        <include refid="selectOfficeDocumentVo"/>
        WHERE id = #{id}
    </select>

    <insert id="insertOfficeDocument" parameterType="OfficeDocument" useGeneratedKeys="true" keyProperty="id">
        insert into t_office_document
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="documentNumber != null">document_number,</if>
            <if test="documentName != null">document_name,</if>
            <if test="documentTypeId != null">document_type_id,</if>
            <if test="archiveTime != null">archive_time,</if>
            <if test="effectiveDate != null">effective_date,</if>
            <if test="expiryDate != null">expiry_date,</if>
            <if test="version != null">version,</if>
            <if test="remark != null">remark,</if>
            <if test="creatorId != null">creator_id,</if>
            <if test="creator != null">creator,</if>
            <if test="reviewerId != null">reviewer_id,</if>
            <if test="reviewer != null">reviewer,</if>
            <if test="reviewerTime != null">reviewer_time,</if>
            <if test="accessoryUrl != null">accessory_url,</if>
            <if test="status != null">status,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="documentNumber != null">#{documentNumber},</if>
            <if test="documentName != null">#{documentName},</if>
            <if test="documentTypeId != null">#{documentTypeId},</if>
            <if test="archiveTime != null">#{archiveTime},</if>
            <if test="effectiveDate != null">#{effectiveDate},</if>
            <if test="expiryDate != null">#{expiryDate},</if>
            <if test="version != null">#{version},</if>
            <if test="remark != null">#{remark},</if>
            <if test="creatorId != null">#{creatorId},</if>
            <if test="creator != null">#{creator},</if>
            <if test="reviewerId != null">#{reviewerId},</if>
            <if test="reviewer != null">#{reviewer},</if>
            <if test="reviewerTime != null">#{reviewerTime},</if>
            <if test="accessoryUrl != null">#{accessoryUrl},</if>
            <if test="status != null">#{status},</if>
        </trim>
    </insert>

    <update id="updateOfficeDocument" parameterType="OfficeDocument">
        update t_office_document
        <trim prefix="SET" suffixOverrides=",">
            <if test="documentNumber != null">document_number = #{documentNumber},</if>
            <if test="documentName != null">document_name = #{documentName},</if>
            <if test="documentTypeId != null">document_type_id = #{documentTypeId},</if>
            <if test="version != null">version = #{version},</if>
            <if test="creatorId != null">creator_id = #{creatorId},</if>
            <if test="creator != null">creator = #{creator},</if>
            <if test="reviewerId != null">reviewer_id = #{reviewerId},</if>
            <if test="reviewer != null">reviewer = #{reviewer},</if>
            <if test="reviewerTime != null">reviewer_time = #{reviewerTime},</if>
            <if test="status != null">status = #{status},</if>
            remark = #{remark},
            archive_time = #{archiveTime},
            effective_date = #{effectiveDate},
            expiry_date = #{expiryDate},
            accessory_url = #{accessoryUrl},
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteOfficeDocumentById" parameterType="Integer">
        DELETE
        FROM t_office_document
        WHERE id = #{id}
    </delete>

    <delete id="deleteOfficeDocumentByIds" parameterType="String">
        delete from t_office_document where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>
