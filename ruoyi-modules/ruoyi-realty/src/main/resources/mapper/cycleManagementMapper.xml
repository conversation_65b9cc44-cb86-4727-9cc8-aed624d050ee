<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ibms.service.realty.web.mapper.CycleManagementMapper">

    <resultMap type="CycleManagement" id="CycleManagementResult">
        <result property="id" column="id"/>
        <result property="name" column="name"/>
        <result property="days" column="days"/>
        <result property="shiftIds" column="shift_ids"/>
    </resultMap>

    <sql id="selectCycleManagementVo">
        select id, name, days, shift_ids
        from t_cycle_management
    </sql>

    <select id="selectCycleManagementList" parameterType="CycleManagement" resultMap="CycleManagementResult">
        <include refid="selectCycleManagementVo"/>
        <where>
            <if test="name != null  and name != ''">and name like concat('%', #{name}, '%')</if>
            <if test="days != null ">and days = #{days}</if>
            <if test="shiftIds != null  and shiftIds != ''">and shift_ids = #{shiftIds}</if>
        </where>
        order by id desc
    </select>

    <select id="selectCycleManagementById" parameterType="Integer" resultMap="CycleManagementResult">
        <include refid="selectCycleManagementVo"/>
        where id = #{id}
    </select>

    <insert id="insertCycleManagement" parameterType="CycleManagement" useGeneratedKeys="true" keyProperty="id">
        insert into t_cycle_management
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="name != null and name != ''">name,</if>
            <if test="days != null">days,</if>
            <if test="shiftIds != null and shiftIds != ''">shift_ids,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="name != null and name != ''">#{name},</if>
            <if test="days != null">#{days},</if>
            <if test="shiftIds != null and shiftIds != ''">#{shiftIds},</if>
        </trim>
    </insert>

    <update id="updateCycleManagement" parameterType="CycleManagement">
        update t_cycle_management
        <trim prefix="SET" suffixOverrides=",">
            <if test="name != null and name != ''">name = #{name},</if>
            <if test="days != null">days = #{days},</if>
            <if test="shiftIds != null and shiftIds != ''">shift_ids = #{shiftIds},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteCycleManagementById" parameterType="Integer">
        delete
        from t_cycle_management
        where id = #{id}
    </delete>

    <delete id="deleteCycleManagementByIds" parameterType="String">
        delete from t_cycle_management where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>
