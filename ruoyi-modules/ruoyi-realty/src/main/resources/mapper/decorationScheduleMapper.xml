<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ibms.service.realty.web.mapper.DecorationScheduleMapper">
    <resultMap type="DecorationSchedule" id="DecorationScheduleResult">
        <result property="taskId" column="task_id"/>
        <result property="taskName" column="task_name"/>
        <result property="decorationPlanId" column="decoration_plan_id"/>
        <result property="decorationPlanName" column="decoration_plan_name"/>
        <result property="roomCode" column="room_code"/>
        <result property="roomName" column="room_name"/>
        <result property="status" column="status"/>
        <result property="plannedStartTime" column="planned_start_time"/>
        <result property="plannedEndTime" column="planned_end_time"/>
        <result property="patrollerId" column="patroller_id"/>
        <result property="patrollerName" column="patroller_name"/>
        <result property="actualStartTime" column="actual_start_time"/>
        <result property="actualEndTime" column="actual_end_time"/>
        <result property="remark" column="remark"/>
        <result property="img" column="img"/>

        <result property="buildName" column="build_name"/>

        <collection property="decorationScheduleInfoList"
                    ofType="com.ibms.service.realty.web.controller.decorationSchedule.vo.DecorationScheduleInfoVo">
            <result property="id" column="schedule_project_id"/>
            <result property="scheduleInfoId" column="schedule_info_id"/>
            <result property="infoId" column="info_id"/>
            <result property="scheduleInfoStatus" column="schedule_info_status"/>
            <result property="infoName" column="info_name"/>
        </collection>
    </resultMap>

    <sql id="selectDecorationScheduleVo">
        SELECT tds.task_id,
        tds.task_name,
        tds.decoration_plan_id,
        tds.decoration_plan_name,
        tds.room_code,
        tds.room_name,
        tds.status,
        tds.planned_start_time,
        tds.planned_end_time,
        tds.patroller_id,
        tds.patroller_name,
        tds.actual_start_time,
        tds.actual_end_time,
        tds.remark,
        tds.img,
        tpri3.name AS build_name
        FROM t_decoration_schedule tds
        LEFT JOIN t_property_resource_info tpri1 ON tds.room_code = tpri1.id
        LEFT JOIN t_property_resource_info tpri2 ON tpri1.parent_id = tpri2.id
        LEFT JOIN t_property_resource_info tpri3 ON tpri2.parent_id = tpri3.id
    </sql>

    <select id="selectDecorationScheduleList" parameterType="DecorationSchedule" resultMap="DecorationScheduleResult">
        <include refid="selectDecorationScheduleVo"/>
        <where>
            <if test="taskName != null  and taskName != ''">and tds.task_name like concat('%', #{taskName}, '%')</if>
            <if test="decorationPlanId != null ">and tds.decoration_plan_id = #{decorationPlanId}</if>
            <if test="decorationPlanName != null  and decorationPlanName != ''">and tds.decoration_plan_name like
                concat('%', #{decorationPlanName}, '%')
            </if>
            <if test="roomCode != null ">and tds.room_code = #{roomCode}</if>
            <if test="roomName != null and roomName != ''">and tds.room_name like concat('%',#{roomName},'%')</if>
            <if test="status != null and status != ''">and tds.status = #{status}</if>
            <if test="plannedStartTime != null ">and DATE_FORMAT(tds.planned_start_time ,'%y-%m-%d') >=
                DATE_FORMAT(#{plannedStartTime} ,'%y-%m-%d')
            </if>
            <if test="plannedEndTime != null ">and DATE_FORMAT(tds.planned_end_time ,'%y-%m-%d')
                <![CDATA[<= DATE_FORMAT(#{plannedEndTime} ,'%y-%m-%d')]]></if>
            <if test="patrollerId != null">and tds.patroller_id = #{patrollerId}</if>
            <if test="patrollerName != null  and patrollerName != ''">and tds.patroller_name = #{patrollerName}</if>
            <if test="actualStartTime != null ">and tds.actual_start_time = #{actualStartTime}</if>
            <if test="actualEndTime != null ">and tds.actual_end_time = #{actualEndTime}</if>
        </where>
        order by planned_start_time desc
    </select>

    <select id="selectDecorationScheduleByTaskId" parameterType="String" resultMap="DecorationScheduleResult">
        SELECT td.task_id,
        td.task_name,
        td.decoration_plan_id,
        td.decoration_plan_name,
        td.room_code,
        td.room_name,
        td.status,
        td.planned_start_time,
        td.planned_end_time,
        td.patroller_id,
        td.patroller_name,
        td.actual_start_time,
        td.actual_end_time,
        td.remark,
        td.img,

        tdsi.id AS schedule_project_id,
        tdsi.task_id AS schedule_info_id,
        tdsi.info_id,
        tdsi.status AS schedule_info_status,
        tdi.name AS info_name

        FROM t_decoration_schedule td
        LEFT JOIN t_decoration_schedule_info tdsi ON td.task_id = tdsi.task_id
        INNER JOIN t_decoration_info tdi ON tdsi.info_id = tdi.id
        WHERE td.task_id = #{taskId}
    </select>

    <!--    insert后返回主键id-->
    <insert id="insertDecorationSchedule" parameterType="DecorationSchedule" useGeneratedKeys="true"
            keyProperty="taskId">
        insert into t_decoration_schedule
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="taskId != null">task_id,</if>
            <if test="taskName != null and taskName != ''">task_name,</if>
            <if test="decorationPlanId != null">decoration_plan_id,</if>
            <if test="decorationPlanName != null and decorationPlanName != ''">decoration_plan_name,</if>
            <if test="roomCode != null and roomCode != ''">room_code,</if>
            <if test="roomName != null and roomName != ''">room_name,</if>
            <if test="status != null">status,</if>
            <if test="plannedStartTime != null">planned_start_time,</if>
            <if test="plannedEndTime != null">planned_end_time,</if>
            <if test="patrollerId != null and patrollerId != ''">patroller_id,</if>
            <if test="patrollerName != null">patroller_name,</if>
            <if test="actualStartTime != null">actual_start_time,</if>
            <if test="actualEndTime != null">actual_end_time,</if>
            <if test="remark != null">remark,</if>
            <if test="img != null">img,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="taskId != null">#{taskId},</if>
            <if test="taskName != null and taskName != ''">#{taskName},</if>
            <if test="decorationPlanId != null">#{decorationPlanId},</if>
            <if test="decorationPlanName != null and decorationPlanName != ''">#{decorationPlanName},</if>
            <if test="roomCode != null">#{roomCode},</if>
            <if test="roomName != null">#{roomName},</if>
            <if test="status != null">#{status},</if>
            <if test="plannedStartTime != null">#{plannedStartTime},</if>
            <if test="plannedEndTime != null">#{plannedEndTime},</if>
            <if test="patrollerId != null and patrollerId != ''">#{patrollerId},</if>
            <if test="patrollerName != null">#{patrollerName},</if>
            <if test="actualStartTime != null">#{actualStartTime},</if>
            <if test="actualEndTime != null">#{actualEndTime},</if>
            <if test="remark != null">#{remark},</if>
            <if test="img != null">#{img},</if>
        </trim>
    </insert>

    <!--    void insertBatchInfoVos(List<DecorationScheduleInfoVo> decorationScheduleInfoVos);-->
    <insert id="insertBatchInfoVos">
        insert into t_decoration_schedule_info(task_id,info_id,status) VALUES
        <foreach collection="list" item="item" index="index" separator=",">
            <trim prefix="(" suffix=")" suffixOverrides=",">
                #{item.scheduleInfoId},
                #{item.infoId},
                #{item.scheduleInfoStatus}
            </trim>
        </foreach>
    </insert>

    <update id="updateDecorationSchedule" parameterType="DecorationSchedule">
        update t_decoration_schedule
        <trim prefix="SET" suffixOverrides=",">
            <if test="taskName != null and taskName != ''">task_name = #{taskName},</if>
            <if test="decorationPlanId != null">decoration_plan_id = #{decorationPlanId},</if>
            <if test="decorationPlanName != null and decorationPlanName != ''">decoration_plan_name =
                #{decorationPlanName},
            </if>
            <if test="roomCode != null and roomCode != ''">room_code = #{roomCode},</if>
            <if test="roomName != null and roomName != ''">room_name = #{roomName},</if>
            <if test="status != null">status = #{status},</if>
            <if test="plannedStartTime != null">planned_start_time = #{plannedStartTime},</if>
            <if test="plannedEndTime != null">planned_end_time = #{plannedEndTime},</if>
            <if test="patrollerId != null and patrollerId != ''">patroller_id = #{patrollerId},</if>
            <if test="patrollerName != null">patroller_name = #{patrollerName},</if>
            <if test="actualStartTime != null">actual_start_time = #{actualStartTime},</if>
            <if test="actualEndTime != null">actual_end_time = #{actualEndTime},</if>
            <if test="remark != null">remark = #{remark},</if>
            <if test="img != null">img = #{img},</if>
        </trim>
        where task_id = #{taskId}
    </update>
    <update id="updateDecorationScheduleInfo">
        UPDATE t_decoration_schedule_info
        SET status = #{scheduleInfoStatus}
        WHERE id = #{id}
    </update>


    <delete id="deleteDecorationScheduleByTaskId" parameterType="String">
        DELETE
        FROM t_decoration_schedule
        WHERE task_id = #{taskId}
    </delete>

    <delete id="deleteDecorationScheduleByTaskIds" parameterType="String">
        delete from t_decoration_schedule where task_id in
        <foreach item="taskId" collection="array" open="(" separator="," close=")">
            #{taskId}
        </foreach>
    </delete>

    <delete id="deleteDecorationScheduleInfoByTaskId">
        DELETE
        FROM t_decoration_schedule_info
        WHERE task_id = #{taskId}
    </delete>
</mapper>
