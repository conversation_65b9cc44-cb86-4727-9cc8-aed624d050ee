<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ibms.service.realty.web.mapper.KnowledgeDataMapper">

    <resultMap type="KnowledgeData" id="KnowledgeDataResult">
        <result property="id" column="id"/>
        <result property="questionType" column="question_type"/>
        <result property="title" column="title"/>
        <result property="represent" column="represent"/>
        <result property="annex" column="annex"/>
        <result property="remark" column="remark"/>
    </resultMap>

    <sql id="selectKnowledgeDataVo">
        SELECT id, question_type, title, represent, annex, remark
        FROM t_knowledge_data
    </sql>

    <select id="selectKnowledgeDataList" parameterType="KnowledgeData" resultMap="KnowledgeDataResult">
        <include refid="selectKnowledgeDataVo"/>
        <where>
            <if test="questionType != null  and questionType != ''">and question_type = #{questionType}</if>
            <if test="title != null  and title != ''">and title = #{title}</if>
            <if test="represent != null  and represent != ''">and represent = #{represent}</if>
            <if test="annex != null  and annex != ''">and annex = #{annex}</if>
        </where>
        order by id desc
    </select>

    <select id="selectKnowledgeDataById" parameterType="Long" resultMap="KnowledgeDataResult">
        <include refid="selectKnowledgeDataVo"/>
        where id = #{id}
    </select>

    <insert id="insertKnowledgeData" parameterType="KnowledgeData" useGeneratedKeys="true" keyProperty="id">
        insert into t_knowledge_data
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="questionType != null">question_type,</if>
            <if test="title != null">title,</if>
            <if test="represent != null">represent,</if>
            <if test="annex != null">annex,</if>
            <if test="remark != null">remark,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="questionType != null">#{questionType},</if>
            <if test="title != null">#{title},</if>
            <if test="represent != null">#{represent},</if>
            <if test="annex != null">#{annex},</if>
            <if test="remark != null">#{remark},</if>
        </trim>
    </insert>

    <update id="updateKnowledgeData" parameterType="KnowledgeData">
        update t_knowledge_data
        <trim prefix="SET" suffixOverrides=",">
            <if test="questionType != null">question_type = #{questionType},</if>
            <if test="title != null">title = #{title},</if>
            <if test="represent != null">represent = #{represent},</if>
            <if test="annex != null">annex = #{annex},</if>
            <if test="remark != null">remark = #{remark},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteKnowledgeDataById" parameterType="Long">
        DELETE
        FROM t_knowledge_data
        WHERE id = #{id}
    </delete>

    <delete id="deleteKnowledgeDataByIds" parameterType="String">
        delete from t_knowledge_data where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>