<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ibms.service.realty.web.mapper.InstrumentReadingMapper">

    <resultMap type="InstrumentReading" id="InstrumentReadingResult">
        <result property="id" column="id"/>
        <result property="instrumentTypeId" column="instrument_type_id"/>
        <result property="roomNumber" column="room_number"/>
        <result property="previousReading" column="previous_reading"/>
        <result property="currentReading" column="current_reading"/>
        <result property="readingDate" column="reading_date"/>
        <result property="img" column="img"/>
        <result property="type" column="type"/>
    </resultMap>

    <sql id="selectInstrumentReadingVo">
        select id, instrument_type_id, room_number, previous_reading, current_reading, reading_date, img, type from
        t_instrument_reading
    </sql>

    <select id="selectInstrumentReadingList" parameterType="InstrumentReading" resultMap="InstrumentReadingResult">
        <include refid="selectInstrumentReadingVo"/>
        <where>
            <if test="instrumentTypeId != null ">and instrument_type_id = #{instrumentTypeId}</if>
            <if test="roomNumber != null  and roomNumber != ''">and room_number = #{roomNumber}</if>
            <if test="previousReading != null ">and previous_reading = #{previousReading}</if>
            <if test="currentReading != null ">and current_reading = #{currentReading}</if>
            <if test="readingDate != null ">and reading_date = #{readingDate}</if>
            <if test="img != null  and img != ''">and img = #{img}</if>
            <if test="type != null ">and type = #{type}</if>
        </where>
        order by id desc
    </select>

    <select id="selectInstrumentReadingById" parameterType="Integer" resultMap="InstrumentReadingResult">
        <include refid="selectInstrumentReadingVo"/>
        where id = #{id}
    </select>

    <insert id="insertInstrumentReading" parameterType="InstrumentReading" useGeneratedKeys="true" keyProperty="id">
        insert into t_instrument_reading
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="instrumentTypeId != null">instrument_type_id,</if>
            <if test="roomNumber != null and roomNumber != ''">room_number,</if>
            <if test="previousReading != null">previous_reading,</if>
            <if test="currentReading != null">current_reading,</if>
            <if test="readingDate != null">reading_date,</if>
            <if test="img != null">img,</if>
            <if test="type != null">type,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="instrumentTypeId != null">#{instrumentTypeId},</if>
            <if test="roomNumber != null and roomNumber != ''">#{roomNumber},</if>
            <if test="previousReading != null">#{previousReading},</if>
            <if test="currentReading != null">#{currentReading},</if>
            <if test="readingDate != null">#{readingDate},</if>
            <if test="img != null">#{img},</if>
            <if test="type != null">#{type},</if>
        </trim>
    </insert>

    <update id="updateInstrumentReading" parameterType="InstrumentReading">
        update t_instrument_reading
        <trim prefix="SET" suffixOverrides=",">
            <if test="instrumentTypeId != null">instrument_type_id = #{instrumentTypeId},</if>
            <if test="roomNumber != null and roomNumber != ''">room_number = #{roomNumber},</if>
            <if test="previousReading != null">previous_reading = #{previousReading},</if>
            <if test="currentReading != null">current_reading = #{currentReading},</if>
            <if test="readingDate != null">reading_date = #{readingDate},</if>
            <if test="img != null">img = #{img},</if>
            <if test="type != null">type = #{type},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteInstrumentReadingById" parameterType="Integer">
        delete from t_instrument_reading where id = #{id}
    </delete>

    <delete id="deleteInstrumentReadingByIds" parameterType="String">
        delete from t_instrument_reading where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>
