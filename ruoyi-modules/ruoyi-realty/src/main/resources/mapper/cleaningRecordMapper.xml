<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ibms.service.realty.web.mapper.CleaningRecordMapper">

    <resultMap type="CleaningRecord" id="CleaningRecordResult">
        <result property="id" column="id"/>
        <result property="cleaningRegionId" column="cleaning_region_id"/>
        <result property="projectName" column="project_name"/>
        <result property="projectType" column="project_type"/>
        <result property="plannedCompletionDate" column="planned_completion_date"/>
        <result property="actualCompletionDate" column="actual_completion_date"/>
        <result property="responsiblePersonId" column="responsible_person_id"/>
        <result property="responsiblePersonName" column="responsible_person_name"/>
        <result property="status" column="status"/>
        <result property="content" column="content"/>
        <result property="remark" column="remark"/>
        <result property="cleaningRegionName" column="cleaning_region_name"/>
    </resultMap>

    <sql id="selectCleaningRecordVo">
        select tcr.id,
        tcr.cleaning_region_id,
        tcr.project_name,
        tcr.project_type,
        tcr.planned_completion_date,
        tcr.actual_completion_date,
        tcr.responsible_person_id,
        tcr.responsible_person_name,
        tcr.status,
        tcr.content,
        tcr.remark,

        tcri.area_name as cleaning_region_name
        from t_cleaning_record tcr
        left join t_cleaning_region_info tcri on tcr.cleaning_region_id = tcri.id
    </sql>

    <select id="selectCleaningRecordList" parameterType="CleaningRecord" resultMap="CleaningRecordResult">
        <include refid="selectCleaningRecordVo"/>
        <where>
            <if test="cleaningRegionId != null ">and tcr.cleaning_region_id = #{cleaningRegionId}</if>
            <if test="projectName != null  and projectName != ''">and tcr.project_name like concat('%', #{projectName},
                '%')
            </if>
            <if test="responsiblePersonName != null  and responsiblePersonName != ''">and tcr.responsible_person_name
                like concat('%', #{responsiblePersonName},
                '%')
            </if>
            <if test="projectType != null  and projectType != ''">and tcr.project_type = #{projectType}</if>
            <if test="plannedCompletionDate != null ">and tcr.planned_completion_date = #{plannedCompletionDate}</if>
            <if test="plannedCompletionDateStart != null ">and tcr.planned_completion_date >=
                #{plannedCompletionDateStart}
            </if>
            <if test="plannedCompletionDateEnd != null ">and tcr.planned_completion_date
                <![CDATA[<= #{plannedCompletionDateEnd}]]></if>
            <if test="actualCompletionDate != null ">and tcr.actual_completion_date = #{actualCompletionDate}</if>
            <if test="responsiblePersonId != null ">and tcr.responsible_person_id = #{responsiblePersonId}</if>
            <if test="status != null  and status != ''">and tcr.status = #{status}</if>
            <if test="content != null  and content != ''">and tcr.content = #{content}</if>
        </where>
        order by tcr.id desc
    </select>

    <select id="selectCleaningRecordById" parameterType="Integer" resultMap="CleaningRecordResult">
        <include refid="selectCleaningRecordVo"/>
        where tcr.id = #{id}
    </select>

    <insert id="insertCleaningRecord" parameterType="CleaningRecord" useGeneratedKeys="true" keyProperty="id">
        insert into t_cleaning_record
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="cleaningRegionId != null">cleaning_region_id,</if>
            <if test="projectName != null and projectName != ''">project_name,</if>
            <if test="projectType != null and projectType != ''">project_type,</if>
            <if test="plannedCompletionDate != null">planned_completion_date,</if>
            <if test="actualCompletionDate != null">actual_completion_date,</if>
            <if test="responsiblePersonId != null">responsible_person_id,</if>
            <if test="responsiblePersonName != null">responsible_person_name,</if>
            <if test="status != null">status,</if>
            <if test="content != null">content,</if>
            <if test="remark != null">remark,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="cleaningRegionId != null">#{cleaningRegionId},</if>
            <if test="projectName != null and projectName != ''">#{projectName},</if>
            <if test="projectType != null and projectType != ''">#{projectType},</if>
            <if test="plannedCompletionDate != null">#{plannedCompletionDate},</if>
            <if test="actualCompletionDate != null">#{actualCompletionDate},</if>
            <if test="responsiblePersonId != null">#{responsiblePersonId},</if>
            <if test="responsiblePersonName != null">#{responsiblePersonName},</if>
            <if test="status != null">#{status},</if>
            <if test="content != null">#{content},</if>
            <if test="remark != null">#{remark},</if>
        </trim>
    </insert>

    <update id="updateCleaningRecord" parameterType="CleaningRecord">
        update t_cleaning_record
        <trim prefix="SET" suffixOverrides=",">
            <if test="cleaningRegionId != null">cleaning_region_id = #{cleaningRegionId},</if>
            <if test="projectName != null and projectName != ''">project_name = #{projectName},</if>
            <if test="projectType != null and projectType != ''">project_type = #{projectType},</if>
            <if test="plannedCompletionDate != null">planned_completion_date = #{plannedCompletionDate},</if>
            <if test="actualCompletionDate != null">actual_completion_date = #{actualCompletionDate},</if>
            <if test="responsiblePersonId != null">responsible_person_id = #{responsiblePersonId},</if>
            <if test="responsiblePersonName != null">responsible_person_name = #{responsiblePersonName},</if>
            <if test="status != null">status = #{status},</if>
            <if test="content != null">content = #{content},</if>
            <if test="remark != null">remark = #{remark},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteCleaningRecordById" parameterType="Integer">
        delete
        from t_cleaning_record
        where id = #{id}
    </delete>

    <delete id="deleteCleaningRecordByIds" parameterType="String">
        delete from t_cleaning_record where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>
