<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ibms.service.realty.web.mapper.ParameterSettingsMapper">

    <resultMap type="ParameterSettings" id="ParameterSettingsResult">
        <result property="id" column="id"/>
        <result property="type" column="type"/>
        <result property="typeName" column="type_name"/>
        <result property="value" column="value"/>
        <result property="status" column="status"/>
    </resultMap>

    <sql id="selectParameterSettingsVo">
        select id, type, type_name, value, status from t_parameter_settings
    </sql>

    <select id="selectParameterSettingsList" parameterType="ParameterSettings" resultMap="ParameterSettingsResult">
        <include refid="selectParameterSettingsVo"/>
        <where>
            <if test="type != null  and type != ''">and type = #{type}</if>
            <if test="typeName != null  and typeName != ''">and type_name like concat('%', #{typeName}, '%')</if>
            <if test="value != null  and value != ''">and value like concat('%', #{value}, '%')</if>
            <if test="status != null ">and status = #{status}</if>
        </where>
        order by id desc
    </select>

    <select id="selectParameterSettingsById" parameterType="Integer" resultMap="ParameterSettingsResult">
        <include refid="selectParameterSettingsVo"/>
        where id = #{id}
    </select>

    <insert id="insertParameterSettings" parameterType="ParameterSettings" useGeneratedKeys="true" keyProperty="id">
        insert into t_parameter_settings
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="type != null and type != ''">type,</if>
            <if test="typeName != null and typeName != ''">type_name,</if>
            <if test="value != null and value != ''">value,</if>
            <if test="status != null">status,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="type != null and type != ''">#{type},</if>
            <if test="typeName != null and typeName != ''">#{typeName},</if>
            <if test="value != null and value != ''">#{value},</if>
            <if test="status != null">#{status},</if>
        </trim>
    </insert>

    <update id="updateParameterSettings" parameterType="ParameterSettings">
        update t_parameter_settings
        <trim prefix="SET" suffixOverrides=",">
            <if test="type != null and type != ''">type = #{type},</if>
            <if test="typeName != null and typeName != ''">type_name = #{typeName},</if>
            <if test="value != null and value != ''">value = #{value},</if>
            <if test="status != null">status = #{status},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteParameterSettingsById" parameterType="Integer">
        delete from t_parameter_settings where id = #{id}
    </delete>

    <delete id="deleteParameterSettingsByIds" parameterType="String">
        delete from t_parameter_settings where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>
