package com.ibms.service.realty;

import com.ibms.service.realty.task.CleanPlanTask;
import com.ibms.service.realty.task.DecorationPlanTask;
import com.ibms.service.realty.task.PatrolPlanTask;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

@SpringBootTest
public class RealtyApplicationTest {

    @Autowired
    private CleanPlanTask cleanPlanTask;

    @Autowired
    private PatrolPlanTask patrolPlanTask;

    @Autowired
    private DecorationPlanTask decorationPlanTask;

    @Test
    public void test() {
        cleanPlanTask.execute();
    }

    @Test
    public void test1() {
        patrolPlanTask.execute();
    }

//    @Test
//    public void test2() {
//        decorationPlanTask.execute();
//    }

}
